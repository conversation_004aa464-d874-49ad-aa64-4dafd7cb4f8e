{"project_info": {"project_number": "1061241821839", "project_id": "voca-dex", "storage_bucket": "voca-dex.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1061241821839:android:a510b3bd52c5e6bc486efa", "android_client_info": {"package_name": "com.example.vocadex"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB_nsTyoy9H_mT5n1-23OEXMprS4fo_zHQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1061241821839:android:9db909179316d458486efa", "android_client_info": {"package_name": "com.tabemedia.vocadex"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB_nsTyoy9H_mT5n1-23OEXMprS4fo_zHQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}