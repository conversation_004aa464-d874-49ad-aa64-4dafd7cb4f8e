// lib/core/router/route_names.dart

abstract class RouteNames {
  // Auth Routes
  static const splash = '/';
  static const login = '/login';
  static const signup = '/signup';
  static const forgotPassword = '/forgot-password';
  static const onboarding = '/onboarding';
  static const welcome = '/welcome';

  // Main App Routes
  static const home = '/home';
  static const profile = '/profile';
  static const explore = '/explore';
  static const settings = '/settings';
  static const about = '/settings';
  static const language = '/language';
  static const quiz = '/quiz';
  static const challengeQuiz = '/challenge-quiz';
  static const exercises = '/exercises';
  static const vocabDeck = '/deck';
  static const quizResults = '/quiz-results';
  static const quizStats = '/quiz-stats';
  static const leaderboard = '/leaderboard';
  // Nested Routes Example
  static const profileEdit = '/profile/edit';
  static const profileEmail = '/profile/email';
  static const profilePassword = '/profile/password';
  static const profileSettings = '/profile/settings';
  static const profileSupport = '/profile/support';
  static const profileFaq = '/profile/faq';
  static const profilePrivacy = '/profile/privacy';
  static const profileTerms = '/profile/terms';
  static const profileAbout = '/profile/about';

  static const List<String> publicRoutes = [
    splash,
    login,
    signup,
    forgotPassword,
  ];
}
