// lib/core/router/router_provider.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/auth/presentation/forgot_password_screen.dart';
import 'package:vocadex/src/features/auth/presentation/login_screen.dart';
import 'package:vocadex/src/features/auth/presentation/splash_screen.dart';
import 'package:vocadex/src/features/bottom_navbar/bottom_navbar.dart';
import 'package:vocadex/src/features/dashboard/presentation/dashboard_screen.dart';
import 'package:vocadex/src/features/leaderboard/leaderboard_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/guided_onboarding_screen.dart';
import 'package:vocadex/src/features/stats_screen/user_stats_screen.dart';
import 'package:vocadex/src/features/user/presentation/profile_screen.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/presentation/vocab_carousel.dart';
import 'package:vocadex/src/features/vocab_deck/presentation/vocab_deck_screen.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_screen.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_results_screen.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_analysis_screen.dart';

import 'route_names.dart';

part 'router_provider.g.dart';

@riverpod
GoRouter router(Ref ref) {
  //final shellNavigatorKey = GlobalKey<NavigatorState>();

  return GoRouter(
    initialLocation: '/',
    routes: [
      // Auth & Public Routes
      GoRoute(
        path: '/',
        name: RouteNames.splash,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        name: RouteNames.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        name: RouteNames.signup,
        builder: (context, state) => const GuidedOnboardingScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: RouteNames.forgotPassword,
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/welcome',
        name: RouteNames.welcome,
        builder: (context, state) => const GuidedOnboardingScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        name: RouteNames.onboarding,
        builder: (context, state) => const GuidedOnboardingScreen(),
      ),
      GoRoute(
        path: '/quiz',
        name: RouteNames.quiz,
        builder: (context, state) => const QuizScreen(),
      ),

      GoRoute(
        path: '/quiz/results',
        name: RouteNames.quizResults,
        builder: (context, state) =>
            QuizResultScreen(quiz: state.extra as Quiz),
      ),
      GoRoute(
        path: '/quiz-stats',
        name: RouteNames.quizStats,
        builder: (context, state) => const QuizAnalysisScreen(),
      ),
      GoRoute(
        path: '/leaderboard',
        name: RouteNames.leaderboard,
        builder: (context, state) => const LeaderboardScreen(),
      ),
      GoRoute(
        path: '/profile',
        name: RouteNames.profile,
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/vocab-card/:id',
        name: 'vocab-card',
        builder: (context, state) {
          final index = int.tryParse(state.pathParameters['id'] ?? '0') ?? 0;
          final cards = state.extra as List<VocabCard>;
          return VocabCarouselScreen(
            cards: cards,
            initialIndex: index,
          );
        },
      ),

      // Shell Route for Bottom Navigation
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return Scaffold(
            resizeToAvoidBottomInset: false,
            body: navigationShell,
            bottomNavigationBar: const BottomNavBar(),
            floatingActionButton: const AddButton(),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
          );
        },
        branches: [
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/home',
                name: RouteNames.home,
                builder: (context, state) => const Dashboard(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/deck',
                name: RouteNames.vocabDeck,
                builder: (context, state) => const VocabDeckScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/exercises',
                name: RouteNames.exercises,
                builder: (context, state) => const QuizAnalysisScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/stats',
                name: 'stats',
                // builder: (context, state) => const StatsAndAchievementsScreen(),
                builder: (context, state) => const UserStatsScreen(),
              ),
            ],
          ),
        ],
      ),
    ],
  );
}

   // redirect: (context, state) {
    //   // Add authentication redirect logic here
    //   final isInPublicRoute =
    //       RouteNames.publicRoutes.contains(state.matchedLocation);
    //   // Example: Get auth state from your auth provider
    //   // final isAuthenticated = ref.read(authProvider).isAuthenticated;

    //   // if (!isAuthenticated && !isInPublicRoute) {
    //   //   return RouteNames.login;
    //   // }

    //   // if (isAuthenticated && isInPublicRoute) {
    //   //   return RouteNames.home;
    //   // }

    //   return null;
    // },

/*!SECTION

// lib/core/router/router_provider.dart
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'transitions/custom_transitions.dart';
import 'route_names.dart';

part 'router_provider.g.dart';

@riverpod
GoRouter router(RouterRef ref) {
  return GoRouter(
    initialLocation: '/',
    routes: [
      // Auth Routes with fade transition
      GoRoute(
        path: '/',
        name: RouteNames.splash,
        pageBuilder: (context, state) => CustomTransitions.fadeTransition(
          child: const SplashScreen(),
          state: state,
        ),
      ),
      GoRoute(
        path: '/login',
        name: RouteNames.login,
        pageBuilder: (context, state) => CustomTransitions.slideTransition(
          child: const LoginScreen(),
          state: state,
        ),
      ),
      GoRoute(
        path: '/signup',
        name: RouteNames.signup,
        pageBuilder: (context, state) => CustomTransitions.slideTransition(
          child: const SignupScreen(),
          state: state,
        ),
      ),
      GoRoute(
        path: '/forgot-password',
        name: RouteNames.forgotPassword,
        pageBuilder: (context, state) => CustomTransitions.bottomToTopTransition(
          child: const ForgotPasswordScreen(),
          state: state,
        ),
      ),

      // Shell Route for Bottom Navigation
      ShellRoute(
        builder: (context, state, child) {
          return Scaffold(
            body: child,
            bottomNavigationBar: const BottomNavBar(),
          );
        },
        routes: [
          GoRoute(
            path: '/home',
            name: RouteNames.home,
            pageBuilder: (context, state) => CustomTransitions.fadeTransition(
              child: const HomeScreen(),
              state: state,
            ),
          ),
          GoRoute(
            path: '/profile',
            name: RouteNames.profile,
            pageBuilder: (context, state) => CustomTransitions.fadeTransition(
              child: const ProfileScreen(),
              state: state,
            ),
            routes: [
              GoRoute(
                path: 'edit',
                name: RouteNames.profileEdit,
                pageBuilder: (context, state) => CustomTransitions.slideTransition(
                  child: const ProfileEditScreen(),
                  state: state,
                ),
              ),
              GoRoute(
                path: 'settings',
                name: RouteNames.profileSettings,
                pageBuilder: (context, state) => CustomTransitions.slideTransition(
                  child: const ProfileSettingsScreen(),
                  state: state,
                ),
              ),
            ],
          ),
        ],
      ),
    ],
    // Optional: Add error builder for custom error pages
    errorBuilder: (context, state) => CustomTransitions.fadeTransition(
      child: ErrorScreen(error: state.error),
      state: state,
    ),
  );
}





*/