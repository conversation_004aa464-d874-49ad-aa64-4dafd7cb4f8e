import 'package:flutter/material.dart';
import '../constants/constants_color.dart';

/// Utility class that provides convenient access to theme colors
/// This makes it easier to use theme colors throughout the app without
/// having to manually check the brightness each time
class ThemeUtils {
  /// Get the primary color based on the current theme
  static Color getPrimaryColor(BuildContext context) {
    return AppColors.getPrimaryColor(Theme.of(context).brightness);
  }

  /// Get the secondary color based on the current theme
  static Color getSecondaryColor(BuildContext context) {
    return AppColors.getSecondaryColor(Theme.of(context).brightness);
  }

  /// Get the background color based on the current theme
  static Color getBackgroundColor(BuildContext context) {
    return AppColors.getBackgroundColor(Theme.of(context).brightness);
  }

  /// Get the text color based on the current theme
  static Color getTextColor(BuildContext context) {
    return AppColors.getTextColor(Theme.of(context).brightness);
  }

  /// Get the button foreground color based on the current theme
  static Color getButtonForegroundColor(BuildContext context) {
    return AppColors.getButtonForegroundColor(Theme.of(context).brightness);
  }

  /// Get the button background color based on the current theme
  static Color getButtonBackgroundColor(BuildContext context) {
    return AppColors.getButtonBackgroundColor(Theme.of(context).brightness);
  }

  /// Get the correct color based on the current theme
  static Color getCorrectColor(BuildContext context) {
    return AppColors.getCorrectColor(Theme.of(context).brightness);
  }

  /// Get the incorrect color based on the current theme
  static Color getIncorrectColor(BuildContext context) {
    return AppColors.getIncorrectColor(Theme.of(context).brightness);
  }

  /// Get the warning color based on the current theme
  static Color getWarningColor(BuildContext context) {
    return AppColors.getWarningColor(Theme.of(context).brightness);
  }

  /// Get the info color based on the current theme
  static Color getInfoColor(BuildContext context) {
    return AppColors.getInfoColor(Theme.of(context).brightness);
  }

  /// Get the success color based on the current theme
  static Color getSuccessColor(BuildContext context) {
    return AppColors.getSuccessColor(Theme.of(context).brightness);
  }

  /// Get the failure color based on the current theme
  static Color getFailureColor(BuildContext context) {
    return AppColors.getFailureColor(Theme.of(context).brightness);
  }

  /// Get the wild status color (same for both themes)
  static Color getWildColor(BuildContext context) {
    return AppColors.wild;
  }

  /// Get the tamed status color (same for both themes)
  static Color getTamedColor(BuildContext context) {
    return AppColors.tamed;
  }

  /// Get the mastered status color (same for both themes)
  static Color getMasteredColor(BuildContext context) {
    return AppColors.mastered;
  }

  /// Get the FAB button color (same for both themes)
  static Color getButtonFABColor(BuildContext context) {
    return AppColors.buttonFAB;
  }

  /// Get the level color for A1 level (same for both themes)
  static Color getLevelA1Color(BuildContext context) {
    return AppColors.levelA1;
  }

  /// Get the level color for A2 level (same for both themes)
  static Color getLevelA2Color(BuildContext context) {
    return AppColors.levelA2;
  }

  /// Get the level color for B1 level (same for both themes)
  static Color getLevelB1Color(BuildContext context) {
    return AppColors.levelB1;
  }

  /// Get the level color for B2 level (same for both themes)
  static Color getLevelB2Color(BuildContext context) {
    return AppColors.levelB2;
  }

  /// Get the level color for C1 level (same for both themes)
  static Color getLevelC1Color(BuildContext context) {
    return AppColors.levelC1;
  }

  /// Get the level color for C2 level (same for both themes)
  static Color getLevelC2Color(BuildContext context) {
    return AppColors.levelC2;
  }

  /// Get the level color for unknown level (same for both themes)
  static Color getLevelUnknownColor(BuildContext context) {
    return AppColors.levelUnknown;
  }

  /// Check if the current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
}
