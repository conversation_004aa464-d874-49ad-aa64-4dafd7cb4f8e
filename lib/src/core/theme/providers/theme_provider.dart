// lib/core/theme/providers/theme_provider.dart

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'theme_provider.g.dart';

@riverpod
class ThemeNotifier extends _$ThemeNotifier {
  static const _themeKey = 'theme_mode';

  @override
  Future<ThemeMode> build() async {
    final prefs = await SharedPreferences.getInstance();
    final isLightMode = prefs.getBool(_themeKey) ?? true;
    return isLightMode ? ThemeMode.light : ThemeMode.dark;
  }

  Future<void> toggleTheme() async {
    final currentState = state.valueOrNull;
    if (currentState == null) return;

    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final prefs = await SharedPreferences.getInstance();
      final newMode =
          currentState == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;

      await prefs.setBool(_themeKey, newMode == ThemeMode.light);
      return newMode;
    });
  }
}
