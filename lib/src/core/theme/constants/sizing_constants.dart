// lib/core/theme/constants/sizing_constants.dart

import 'package:flutter/material.dart';

/// Defines the sizing system for the application
sealed class AppSizing {
  // Padding & Margin Scales
  static const double spaceXXS = 2.0;
  static const double spaceXS = 4.0;
  static const double spaceS = 8.0;
  static const double spaceM = 16.0;
  static const double spaceL = 24.0;
  static const double spaceXL = 32.0;
  static const double spaceXXL = 40.0;

  // Border Radius Values
  static const double radiusNone = 0.0;
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusFull = 999.0;

  // Icon Sizes
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 40.0;

  // Button Sizes
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonWidthMin = 64.0;
  static const double buttonWidthMax = 312.0;

  // Input Field Sizes
  static const double inputHeightS = 40.0;
  static const double inputHeightM = 48.0;
  static const double inputHeightL = 56.0;

  // Component Spacing
  static const double componentSpacingXS = 4.0;
  static const double componentSpacingS = 8.0;
  static const double componentSpacingM = 16.0;
  static const double componentSpacingL = 24.0;
  static const double componentSpacingXL = 32.0;

  // Screen Edge Insets
  static const EdgeInsets screenEdgeInsets = EdgeInsets.all(16.0);
  static const EdgeInsets screenEdgeInsetsHorizontal =
      EdgeInsets.symmetric(horizontal: 16.0);
  static const EdgeInsets screenEdgeInsetsVertical =
      EdgeInsets.symmetric(vertical: 16.0);
}

/// Extension for easy access to sizing in widgets
extension SizingExtension on Widget {
  // Padding Extensions
  Widget paddingAll(double value) => Padding(
        padding: EdgeInsets.all(value),
        child: this,
      );

  Widget paddingHorizontal(double value) => Padding(
        padding: EdgeInsets.symmetric(horizontal: value),
        child: this,
      );

  Widget paddingVertical(double value) => Padding(
        padding: EdgeInsets.symmetric(vertical: value),
        child: this,
      );

  // Margin Extensions using Container
  Widget marginAll(double value) => Container(
        margin: EdgeInsets.all(value),
        child: this,
      );

  Widget marginHorizontal(double value) => Container(
        margin: EdgeInsets.symmetric(horizontal: value),
        child: this,
      );

  Widget marginVertical(double value) => Container(
        margin: EdgeInsets.symmetric(vertical: value),
        child: this,
      );

  // Border Radius Extensions using ClipRRect
  Widget withRadius(double radius) => ClipRRect(
        borderRadius: BorderRadius.circular(radius),
        child: this,
      );
}

/// Extension for responsive sizing
extension ResponsiveSizing on BuildContext {
  // Screen Size Helpers
  double get screenWidth => MediaQuery.of(this).size.width;
  double get screenHeight => MediaQuery.of(this).size.height;

  // Responsive Width Helper
  double percentWidth(double percent) => screenWidth * percent;

  // Responsive Height Helper
  double percentHeight(double percent) => screenHeight * percent;

  // Safe Area Padding
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
}
