// // lib/core/theme/constants/typography_constants.dart

// import 'package:flutter/material.dart';

// /// Defines the typography system for the application
// sealed class AppTypography {
//   const AppTypography._();
//   // Font Families
//   static const String primaryFontFamily = 'HostGrotesk';
//   static const String secondaryFontFamily = 'Inter';

//   // Font Sizes
//   static const double displayLarge = 57.0;
//   static const double displayMedium = 45.0;
//   static const double displaySmall = 36.0;

//   static const double headlineLarge = 32.0;
//   static const double headlineMedium = 28.0;
//   static const double headlineSmall = 24.0;

//   static const double titleLarge = 22.0;
//   static const double titleMedium = 16.0;
//   static const double titleSmall = 14.0;

//   static const double bodyLarge = 16.0;
//   static const double bodyMedium = 14.0;
//   static const double bodySmall = 12.0;

//   static const double labelLarge = 14.0;
//   static const double labelMedium = 12.0;
//   static const double labelSmall = 11.0;

//   // Font Weights
//   static const FontWeight light = FontWeight.w300;
//   static const FontWeight regular = FontWeight.w400;
//   static const FontWeight medium = FontWeight.w500;
//   static const FontWeight semiBold = FontWeight.w600;
//   static const FontWeight bold = FontWeight.w700;

//   // Line Heights
//   static const double heightSmall = 1.0;
//   static const double heightMedium = 1.2;
//   static const double heightLarge = 1.5;
//   static const double heightXLarge = 1.8;

//   // Letter Spacing
//   static const double spacingTight = -0.5;
//   static const double spacingNormal = 0.0;
//   static const double spacingWide = 0.5;
//   static const double spacingXWide = 1.0;
// }

// /// Extension for creating TextStyles
// extension TextStyleExtension on TextTheme {
//   // Display Styles
//   TextStyle get displayLargeCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.displayLarge,
//         fontWeight: AppTypography.regular,
//         letterSpacing: AppTypography.spacingNormal,
//         height: AppTypography.heightLarge,
//       );

//   TextStyle get displayMediumCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.displayMedium,
//         fontWeight: AppTypography.regular,
//         letterSpacing: AppTypography.spacingNormal,
//         height: AppTypography.heightLarge,
//       );

//   // Headline Styles
//   TextStyle get headlineLargeCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.headlineLarge,
//         fontWeight: AppTypography.medium,
//         letterSpacing: AppTypography.spacingTight,
//         height: AppTypography.heightMedium,
//       );

//   // Body Styles
//   TextStyle get bodyLargeCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.bodyLarge,
//         fontWeight: AppTypography.regular,
//         letterSpacing: AppTypography.spacingNormal,
//         height: AppTypography.heightMedium,
//       );

//   TextStyle get bodyMediumCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.bodyMedium,
//         fontWeight: AppTypography.regular,
//         letterSpacing: AppTypography.spacingNormal,
//         height: AppTypography.heightMedium,
//       );

//   // Label Styles
//   TextStyle get labelLargeCustom => TextStyle(
//         fontFamily: AppTypography.primaryFontFamily,
//         fontSize: AppTypography.labelLarge,
//         fontWeight: AppTypography.medium,
//         letterSpacing: AppTypography.spacingWide,
//         height: AppTypography.heightSmall,
//       );
// }

// lib/core/theme/constants/typography_constants.dart

import 'package:flutter/material.dart';

/// Defines the typography system for the application
sealed class AppTypography {
  const AppTypography._();
  // Font Families
  static const String primaryFontFamily = 'HostGrotesk';
  static const String secondaryFontFamily = 'HostGrotesk';

  // Font Sizes
  static const double displayLarge = 57.0;
  static const double displayMedium = 45.0;
  static const double displaySmall = 36.0;

  static const double headlineLarge = 32.0;
  static const double headlineMedium = 28.0;
  static const double headlineSmall = 24.0;

  static const double titleLarge = 22.0;
  static const double titleMedium = 16.0;
  static const double titleSmall = 14.0;

  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;

  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 11.0;

  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Line Heights
  static const double heightSmall = 1.0;
  static const double heightMedium = 1.2;
  static const double heightLarge = 1.5;
  static const double heightXLarge = 1.8;

  // Letter Spacing
  static const double spacingTight = -0.5;
  static const double spacingNormal = 0.0;
  static const double spacingWide = 0.5;
  static const double spacingXWide = 1.0;
}

/// Extension for creating TextStyles
extension TextStyleExtension on TextTheme {
  // Display Styles
  TextStyle get displayLargeCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.displayLarge,
        fontWeight: AppTypography.bold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightLarge,
      );

  TextStyle get displayMediumCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.displayMedium,
        fontWeight: AppTypography.bold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightLarge,
      );

  TextStyle get displaySmallCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.displaySmall,
        fontWeight: AppTypography.bold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightMedium,
      );

  // Headline Styles
  TextStyle get headlineLargeCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.headlineLarge,
        fontWeight: AppTypography.bold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightMedium,
      );

  TextStyle get headlineMediumCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.headlineMedium,
        fontWeight: AppTypography.semiBold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightMedium,
      );

  TextStyle get headlineSmallCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.headlineSmall,
        fontWeight: AppTypography.semiBold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightMedium,
      );

  // Title Styles
  TextStyle get titleLargeCustom => TextStyle(
        fontFamily: AppTypography.secondaryFontFamily,
        fontSize: AppTypography.titleLarge,
        fontWeight: AppTypography.semiBold,
        letterSpacing: AppTypography.spacingTight,
        height: AppTypography.heightMedium,
      );

  TextStyle get titleMediumCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.titleMedium,
        fontWeight: AppTypography.medium,
        letterSpacing: AppTypography.spacingNormal,
        height: AppTypography.heightMedium,
      );

  TextStyle get titleSmallCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.titleSmall,
        fontWeight: AppTypography.medium,
        letterSpacing: AppTypography.spacingNormal,
        height: AppTypography.heightMedium,
      );

  // Body Styles
  TextStyle get bodyLargeCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodyLarge,
        fontWeight: AppTypography.regular,
        letterSpacing: AppTypography.spacingNormal,
        height: AppTypography.heightMedium,
      );

  TextStyle get bodyMediumCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodyMedium,
        fontWeight: AppTypography.regular,
        letterSpacing: AppTypography.spacingNormal,
        height: AppTypography.heightMedium,
      );

  TextStyle get bodySmallCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.bodySmall,
        fontWeight: AppTypography.regular,
        letterSpacing: AppTypography.spacingNormal,
        height: AppTypography.heightMedium,
      );

  // Label Styles
  TextStyle get labelLargeCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.labelLarge,
        fontWeight: AppTypography.medium,
        letterSpacing: AppTypography.spacingWide,
        height: AppTypography.heightSmall,
      );

  TextStyle get labelMediumCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.labelMedium,
        fontWeight: AppTypography.medium,
        letterSpacing: AppTypography.spacingWide,
        height: AppTypography.heightSmall,
      );

  TextStyle get labelSmallCustom => TextStyle(
        fontFamily: AppTypography.primaryFontFamily,
        fontSize: AppTypography.labelSmall,
        fontWeight: AppTypography.medium,
        letterSpacing: AppTypography.spacingWide,
        height: AppTypography.heightSmall,
      );
}
