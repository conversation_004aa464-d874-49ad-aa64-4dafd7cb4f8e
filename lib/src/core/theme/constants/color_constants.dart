// // lib/core/theme/constants/constants_color.dart

// import 'package:flutter/material.dart';

// /// Defines the color schemes for the application
// class AppColors {
//   static const Color transparent = Color(0x00000000);

//   // Light Theme Colors
//   static const Color backgroundLight = Color(0xFFF5F5F5);
//   static const Color primaryLight = Color.fromARGB(255, 185, 231, 187);
//   static const Color secondaryLight = Color(0xFF03DAC6);
//   static const Color textLight = Color(0xFF212121);
//   static const Color buttonForegroundLight = Color(0xFFFFFFFF);
//   static const Color buttonBackgroundLight = Color(0xFF6200EE);
//   static const Color correctLight = Color(0xFF4CAF50);
//   static const Color incorrectLight = Color(0xFFF44336);
//   static const Color warningLight = Color(0xFFFF9800);
//   static const Color infoLight = Color(0xFF2196F3);
//   static const Color successLight = Color(0xFF4CAF50);
//   static const Color failureLight = Color(0xFFF44336);
//   // Dark Theme Colors
//   static const Color backgroundDark = Color(0xFF121212);
//   static const Color primaryDark = Color(0xFFBB86FC);
//   static const Color secondaryDark = Color(0xFF03DAC6);
//   static const Color textDark = Color(0xFFFFFFFF);
//   static const Color buttonForegroundDark = Color(0xFF000000);
//   static const Color buttonBackgroundDark = Color(0xFFBB86FC);
//   static const Color correctDark = Color(0xFF81C784);
//   static const Color incorrectDark = Color(0xFFE57373);
//   static const Color warningDark = Color(0xFFFFB74D);
//   static const Color infoDark = Color(0xFF64B5F6);
//   static const Color successDark = Color(0xFF81C784);
//   static const Color failureDark = Color(0xFFE57373);

//   // Level Colors (Same for both themes)
//   static const Color levelA1 = Color(0xFFB3FFE6);
//   static const Color levelA2 = Color(0xFFC4FDC4);
//   static const Color levelB1 = Color(0xFFFFF089);
//   static const Color levelB2 = Color(0xFFFFD699);
//   static const Color levelC1 = Color(0xFFFF9999);
//   static const Color levelC2 = Color(0xFFFFB6C1);
//   static const Color levelUnknown = Color(0xFFE5E7EB);

//   // Status Colors (Same for both themes)
//   static const Color wild = Color.fromARGB(255, 251, 162, 163);
//   static const Color tamed = Color(0xFFFFC977);
//   static const Color mastered = Color.fromARGB(255, 127, 196, 103);
//   static const Color buttonFAB = Color.fromARGB(255, 185, 231, 187);

//   static const Color topLeftGradientColor = Color.fromARGB(255, 227, 250, 185);
//   static const Color topRightGradientColor = Color.fromARGB(255, 250, 170, 104);
//   static const Color centerLeftGradientColor = Color.fromARGB(255, 227, 250, 185);
//   static const Color centerRightGradientColor = Color.fromARGB(255, 247, 149, 69);
//   // Helper method to get colors based on theme brightness
//   static Color getBackgroundColor(Brightness brightness) {
//     return brightness == Brightness.light ? backgroundLight : backgroundDark;
//   }

//   static Color getPrimaryColor(Brightness brightness) {
//     return brightness == Brightness.light ? primaryLight : primaryDark;
//   }

//   static Color getSecondaryColor(Brightness brightness) {
//     return brightness == Brightness.light ? secondaryLight : secondaryDark;
//   }

//   static Color getTextColor(Brightness brightness) {
//     return brightness == Brightness.light ? textLight : textDark;
//   }

//   static Color getButtonForegroundColor(Brightness brightness) {
//     return brightness == Brightness.light
//         ? buttonForegroundLight
//         : buttonForegroundDark;
//   }

//   static Color getButtonBackgroundColor(Brightness brightness) {
//     return brightness == Brightness.light
//         ? buttonBackgroundLight
//         : buttonBackgroundDark;
//   }

//   static Color getCorrectColor(Brightness brightness) {
//     return brightness == Brightness.light ? correctLight : correctDark;
//   }

//   static Color getIncorrectColor(Brightness brightness) {
//     return brightness == Brightness.light ? incorrectLight : incorrectDark;
//   }

//   static Color getWarningColor(Brightness brightness) {
//     return brightness == Brightness.light ? warningLight : warningDark;
//   }

//   static Color getInfoColor(Brightness brightness) {
//     return brightness == Brightness.light ? infoLight : infoDark;
//   }

//   static Color getSuccessColor(Brightness brightness) {
//     return brightness == Brightness.light ? successLight : successDark;
//   }

//   static Color getFailureColor(Brightness brightness) {
//     return brightness == Brightness.light ? failureLight : failureDark;
//   }
// }

// //   // Accent Colors
// //   Color get accent => brightness == Brightness.light
// //       ? AppColors.accentLight
// //       : AppColors.accentDark;

// //   // Surface Colors
// //   Color get customSurface => brightness == Brightness.light
// //       ? AppColors.surfaceLight
// //       : AppColors.surfaceDark;

// //   // Background Colors
// //   Color get customBackground => brightness == Brightness.light
// //       ? AppColors.backgroundLight
// //       : AppColors.backgroundDark;

// //   // Error Colors
// //   Color get customError => brightness == Brightness.light
// //       ? AppColors.errorLight
// //       : AppColors.errorDark;

// //   // Success Colors
// //   Color get success => brightness == Brightness.light
// //       ? AppColors.successLight
// //       : AppColors.successDark;

// //   // Warning Colors
// //   Color get warning => brightness == Brightness.light
// //       ? AppColors.warningLight
// //       : AppColors.warningDark;

// //   // Text Colors
// //   Color get textPrimary => brightness == Brightness.light
// //       ? AppColors.textPrimaryLight
// //       : AppColors.textPrimaryDark;

// //   Color get textSecondary => brightness == Brightness.light
// //       ? AppColors.textSecondaryLight
// //       : AppColors.textSecondaryDark;

// //   // Gradient Colors
// //   LinearGradient get primaryGradient => LinearGradient(
// //         begin: Alignment.topLeft,
// //         end: Alignment.bottomRight,
// //         colors: [
// //           brightness == Brightness.light
// //               ? AppColors.gradientStartLight
// //               : AppColors.gradientStartDark,
// //           brightness == Brightness.light
// //               ? AppColors.gradientEndLight
// //               : AppColors.gradientEndDark,
// //         ],
// //       );
// // }
