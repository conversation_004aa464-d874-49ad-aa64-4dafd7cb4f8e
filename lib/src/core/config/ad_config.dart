import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/ads/models/admob_config_model.dart';
import 'package:vocadex/src/features/ads/providers/admob_config_provider.dart';

class AdConfig {
  // Default fallback values (same as before)
  static const String _defaultAppIdAndroid =
      'ca-app-pub-3940256099942544~**********';
  static const String _defaultInterstitialAdUnitIdAndroid =
      'ca-app-pub-3940256099942544/**********';
  static const String _defaultBannerAdUnitIdAndroid =
      'ca-app-pub-3940256099942544/**********';

  static const String _defaultAppIdiOS =
      'ca-app-pub-3940256099942544~**********';
  static const String _defaultInterstitialAdUdiOS =
      'ca-app-pub-3940256099942544/**********';
  static const String _defaultBannerAdUnitIdiOS =
      'ca-app-pub-3940256099942544/**********';

  // Cached config for synchronous access
  static AdMobConfig? _cachedConfig;

  /// Initialize the config cache - should be called at app startup
  static Future<void> initialize(WidgetRef ref) async {
    try {
      _cachedConfig = await ref.read(admobConfigProvider.future);
    } catch (e) {
      // If fetching fails, use default config
      _cachedConfig = const AdMobConfig();
    }
  }

  /// Get AdMob App ID based on platform
  static String getAdmobAppId() {
    if (Platform.isAndroid) {
      return _cachedConfig?.appIdAndroid ?? _defaultAppIdAndroid;
    } else {
      return _cachedConfig?.appIdiOS ?? _defaultAppIdiOS;
    }
  }

  /// Get Banner Ad Unit ID based on platform
  static String getBannerAdUnitId() {
    if (Platform.isAndroid) {
      return _cachedConfig?.bannerAdUnitIdAndroid ??
          _defaultBannerAdUnitIdAndroid;
    } else {
      return _cachedConfig?.bannerAdUnitIdiOS ?? _defaultBannerAdUnitIdiOS;
    }
  }

  /// Get Interstitial Ad Unit ID based on platform
  static String getInterstitialAdUnitId() {
    if (Platform.isAndroid) {
      return _cachedConfig?.interstitialAdUnitIdAndroid ??
          _defaultInterstitialAdUnitIdAndroid;
    } else {
      return _cachedConfig?.interstitialAdUnitIdiOS ??
          _defaultInterstitialAdUdiOS;
    }
  }

  /// Update the cached config (call when config changes)
  static void updateCache(AdMobConfig config) {
    _cachedConfig = config;
  }
}
