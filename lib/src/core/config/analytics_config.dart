/// Analytics configuration constants
class AnalyticsConfig {
  // Replace with your actual Mixpanel project token
  static const String mixpanelProjectToken = '509a8e272689649f06e33c6226966c28';

  // Analytics feature flags
  static const bool enableAnalytics = true;
  static const bool enableDebugLogging = true;

  // Event names constants for consistency
  static const String eventOnboardingCompleted = 'Onboarding Completed';
  static const String eventWordAdded = 'Word Added';
  static const String eventQuizAttempted = 'Quiz Attempted';
  static const String eventQuizCompleted = 'Quiz Completed';
  static const String eventAppLaunched = 'App Launched';
  static const String eventSessionEnded = 'Session Ended';
  static const String eventPaywallShown = 'Paywall Shown';
  static const String eventSubscriptionPurchased = 'Subscription Purchased';
  static const String eventFeatureUsed = 'Feature Used';
  static const String eventPronunciationPlayed = 'Pronunciation Played';
  static const String eventWordMasteryChanged = 'Word Mastery Changed';
  static const String eventWeeklyGoalCompleted = 'Weekly Goal Completed';
  static const String eventAdShown = 'Ad Shown';
  static const String eventAdClicked = 'Ad Clicked';
  static const String eventErrorOccurred = 'Error Occurred';
  static const String eventUserSignedUp = 'User Signed Up';
  static const String eventUserSignedIn = 'User Signed In';
  static const String eventSearchPerformed = 'Search Performed';
  static const String eventFiltersApplied = 'Filters Applied';
  static const String eventLearningStreakAchieved = 'Learning Streak Achieved';
  static const String eventDailyActiveUser = 'Daily Active User';

  // User property names
  static const String propUserId = 'User ID';
  static const String propIsPremium = 'Is Premium';
  static const String propSignUpDate = 'Sign Up Date';
  static const String propOnboardingCompleted = 'Onboarding Completed';
  static const String propTotalWordsAdded = 'Total Words Added';
  static const String propTotalQuizzesCompleted = 'Total Quizzes Completed';
  static const String propPlatform = 'Platform';
  static const String propAppVersion = 'App Version';
  static const String propPremiumPurchaseDate = 'Premium Purchase Date';
}
