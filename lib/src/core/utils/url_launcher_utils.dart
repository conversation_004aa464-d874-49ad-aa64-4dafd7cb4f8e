// lib/core/utils/url_launcher_utils.dart
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/localization/app_strings.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

class UrlLauncherUtils {
  static Future<void> launchURL(
    BuildContext context,
    String url, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: mode);
      } else {
        showFailureToast(
          context,
          title: 'URL Error',
          description: AppStrings.urlError,
        );
      }
    } catch (e) {
      showFailureToast(
        context,
        title: 'URL Error',
        description: AppStrings.urlError,
      );
    }
  }

  static Future<void> launchEmail(
    BuildContext context, {
    required String email,
    String? subject,
    String? body,
  }) async {
    try {
      final Uri emailUri = Uri(
        scheme: 'mailto',
        path: email,
        queryParameters: {
          if (subject != null) 'subject': subject,
          if (body != null) 'body': body,
        },
      );

      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        showFailureToast(
          context,
          title: 'Email Error',
          description: AppStrings.emailError,
        );
      }
    } catch (e) {
      showFailureToast(
        context,
        title: 'Email Error',
        description: AppStrings.emailError,
      );
    }
  }
}
