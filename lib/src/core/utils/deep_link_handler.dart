// lib/core/utils/deep_link_handler.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';

class DeepLinkHandler {
  static void handleDeepLink(BuildContext context, String link) {
    final uri = Uri.parse(link);

    // Example deep link handling
    switch (uri.path) {
      case '/profile':
        context.pushNamed(RouteNames.profile);
        break;
      case '/settings':
        context.pushNamed(RouteNames.settings);
        break;
      // Add more cases as needed
    }
  }
}
