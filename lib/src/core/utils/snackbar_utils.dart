// // lib/core/utils/snackbar_utils.dart
// import 'package:flutter/material.dart';

// class SnackbarUtils {
//   static void showError(BuildContext context, String message) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Theme.of(context).colorScheme.error,
//         behavior: SnackBarBehavior.floating,
//       ),
//     );
//   }

//   static void showSuccess(BuildContext context, String message) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Theme.of(context).colorScheme.primary,
//         behavior: SnackBarBehavior.floating,
//       ),
//     );
//   }
// }
