// lib/src/core/utils/auth_error_utils.dart
import 'package:firebase_auth/firebase_auth.dart';

/// Utility class for converting Firebase Auth error codes to user-friendly messages
class AuthErrorUtils {
  /// Converts Firebase Auth exceptions to user-friendly error messages
  static String getErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      return _getFirebaseAuthErrorMessage(error.code);
    }
    
    // Handle other types of errors
    String errorString = error.toString();
    
    // Check if it's a Firebase Auth error wrapped in another exception
    if (errorString.contains('[firebase_auth/')) {
      final regex = RegExp(r'\[firebase_auth/([^\]]+)\]');
      final match = regex.firstMatch(errorString);
      if (match != null) {
        final errorCode = match.group(1);
        if (errorCode != null) {
          return _getFirebaseAuthErrorMessage(errorCode);
        }
      }
    }
    
    // Fallback to generic error message
    return 'An unexpected error occurred. Please try again.';
  }

  /// Maps Firebase Auth error codes to user-friendly messages
  static String _getFirebaseAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      // Login-related errors
      case 'user-not-found':
        return 'No account found with this email address. Please check your email or create a new account.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'invalid-credential':
      case 'invalid-email-password':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support for assistance.';
      case 'too-many-requests':
        return 'Too many failed login attempts. Please try again later or reset your password.';
      case 'operation-not-allowed':
        return 'Email/password sign-in is not enabled. Please contact support.';
      
      // Email-related errors
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'email-already-in-use':
        return 'An account with this email already exists. Please sign in instead.';
      
      // Password-related errors
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password with at least 8 characters.';
      case 'requires-recent-login':
        return 'For security reasons, please sign in again to continue.';
      
      // Network-related errors
      case 'network-request-failed':
        return 'Network error. Please check your internet connection and try again.';
      case 'timeout':
        return 'Request timed out. Please check your connection and try again.';
      
      // Account creation errors
      case 'email-already-exists':
        return 'An account with this email already exists. Please sign in instead.';
      case 'invalid-password':
        return 'Invalid password. Please enter a valid password.';
      
      // Token/credential errors
      case 'invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'invalid-verification-id':
        return 'Invalid verification ID. Please try again.';
      case 'credential-already-in-use':
        return 'This credential is already associated with a different account.';
      
      // Account linking errors
      case 'provider-already-linked':
        return 'This account is already linked with this provider.';
      case 'no-such-provider':
        return 'This account is not linked with this provider.';
      
      // Generic/unknown errors
      case 'internal-error':
        return 'An internal error occurred. Please try again later.';
      case 'invalid-api-key':
        return 'Invalid API key. Please contact support.';
      case 'app-deleted':
        return 'This app has been deleted. Please contact support.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}
