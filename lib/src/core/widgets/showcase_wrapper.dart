import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:vocadex/src/services/showcase_service.dart';

/// A wrapper widget that provides showcase functionality for the app
class ShowcaseWrapper extends ConsumerWidget {
  final Widget child;
  final bool showShowcase;

  const ShowcaseWrapper({
    Key? key,
    required this.child,
    this.showShowcase = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ShowCaseWidget(
      onStart: (index, key) {
        debugPrint('Showcase started at index: $index with key: $key');
      },
      onComplete: (index, key) async {
        debugPrint('Showcase completed at index: $index with key: $key');

        // If this is the last showcase, mark all showcases as seen
        if (key.toString().contains('addButton')) {
          final showcaseService = ref.read(showcaseServiceProvider);
          await showcaseService.markAddButtonShowcaseAsSeen();
          await showcaseService.markShowcaseAsSeen();
        } else if (key.toString().contains('navBar')) {
          final showcaseService = ref.read(showcaseServiceProvider);
          await showcaseService.markNavBarShowcaseAsSeen();
        } else if (key.toString().contains('dashboard')) {
          final showcaseService = ref.read(showcaseServiceProvider);
          await showcaseService.markDashboardShowcaseAsSeen();
        } else if (key.toString().contains('vocabDeck')) {
          final showcaseService = ref.read(showcaseServiceProvider);
          await showcaseService.markVocabDeckShowcaseAsSeen();
        }
      },
      blurValue: 1,
      autoPlay: showShowcase,
      autoPlayDelay: const Duration(seconds: 3),
      builder: (context) => child,
    );
  }
}

/// A provider that returns a GlobalKey for the showcase
final showcaseKeyProvider = Provider.family<GlobalKey, String>((ref, id) {
  return GlobalKey();
});
