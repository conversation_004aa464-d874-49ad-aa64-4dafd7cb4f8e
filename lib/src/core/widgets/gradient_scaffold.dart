// // lib/core/presentation/widgets/gradient_scaffold.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart'';
// import 'package:vocadex/src/core/theme/providers/theme_provider.dart';

// class GradientScaffold extends ConsumerWidget {
//   final Widget child;
//   final List<Color>? gradientColors;
//   final PreferredSizeWidget? appBar;
//   final bool? resizeToAvoidBottomInset;
//   final Widget? floatingActionButton;
//   final FloatingActionButtonLocation? floatingActionButtonLocation;
//   final Widget? bottomNavigationBar;
//   final bool extendBody;
//   final bool extendBodyBehindAppBar;

//   const GradientScaffold({
//     super.key,
//     required this.child,
//     this.gradientColors,
//     this.appBar,
//     this.resizeToAvoidBottomInset,
//     this.floatingActionButton,
//     this.floatingActionButtonLocation,
//     this.bottomNavigationBar,
//     this.extendBody = false,
//     this.extendBodyBehindAppBar = false,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final isDarkMode = ref.watch(themeNotifierProvider).value == ThemeMode.dark;

//     return Container(
//       decoration: BoxDecoration(
//         gradient: isDarkMode
//             ? AppColors.backgroundGradientDark
//             : AppColors.backgroundGradientLight,
//       ),
//       child: Scaffold(
//         backgroundColor: AppColors.transparent,
//         appBar: appBar != null
//             ? AppBar(
//                 backgroundColor: AppColors.transparent,
//                 elevation: 0,
//                 title: appBar is AppBar ? (appBar as AppBar).title : null,
//                 actions: appBar is AppBar ? (appBar as AppBar).actions : null,
//                 leading: appBar is AppBar ? (appBar as AppBar).leading : null,
//               )
//             : null,
//         resizeToAvoidBottomInset: resizeToAvoidBottomInset,
//         floatingActionButton: floatingActionButton,
//         floatingActionButtonLocation: floatingActionButtonLocation,
//         bottomNavigationBar: bottomNavigationBar != null
//             ? Theme(
//                 data: Theme.of(context).copyWith(
//                   bottomNavigationBarTheme: const BottomNavigationBarThemeData(
//                     backgroundColor: AppColors.transparent,
//                     elevation: 0,
//                   ),
//                 ),
//                 child: bottomNavigationBar!,
//               )
//             : null,
//         extendBody: extendBody,
//         extendBodyBehindAppBar: extendBodyBehindAppBar,
//         body: child,
//       ),
//     );
//   }
// }

// // class GradientScaffold extends ConsumerWidget {
// //   final Widget child;
// //   final List<Color>? gradientColors;
// //   final PreferredSizeWidget? appBar;
// //   final bool? resizeToAvoidBottomInset;
// //   final Widget? floatingActionButton;
// //   final FloatingActionButtonLocation? floatingActionButtonLocation;
// //   final Widget? bottomNavigationBar;
// //   final bool extendBody;
// //   final bool extendBodyBehindAppBar;

// //   const GradientScaffold({
// //     super.key,
// //     required this.child,
// //     this.gradientColors,
// //     this.appBar,
// //     this.resizeToAvoidBottomInset,
// //     this.floatingActionButton,
// //     this.floatingActionButtonLocation,
// //     this.bottomNavigationBar,
// //     this.extendBody = false,
// //     this.extendBodyBehindAppBar = false,
// //   });

// //   @override
// //   Widget build(BuildContext context, WidgetRef ref) {
// //     final isDarkMode = Theme.of(context).brightness == Brightness.dark;

// //     // Fallback radial gradient colors if not provided
// //     final topLeftGradientColor = gradientColors?.first ??
// //         (isDarkMode
// //             ? AppColors.primaryDark
// //             : const Color.fromARGB(255, 142, 255, 185));
// //     final topRightGradientColor = gradientColors?.last ??
// //         (isDarkMode
// //             ? AppColors.accentDark
// //             : const Color.fromARGB(255, 240, 171, 124));

// //     return Stack(
// //       children: [
// //         // Base white background
// //         Container(color: AppColors.white),

// //         // Top-left corner radial gradient
// //         Positioned(
// //           top: 0,
// //           left: 0,
// //           child: Container(
// //             width: 500,
// //             height: 500,
// //             decoration: BoxDecoration(
// //               gradient: RadialGradient(
// //                 center: Alignment.topLeft,
// //                 radius: 0.5,
// //                 colors: [
// //                   topLeftGradientColor.withAlpha(102),// //                   AppColors.transparent,
// //                 ],
// //               ),
// //             ),
// //           ),
// //         ),

// //         // Top-right corner radial gradient
// //         Positioned(
// //           top: 0,
// //           right: 0,
// //           child: Container(
// //             width: 500,
// //             height: 500,
// //             decoration: BoxDecoration(
// //               gradient: RadialGradient(
// //                 center: Alignment.topRight,
// //                 radius: 1.0,
// //                 colors: [
// //                   topRightGradientColor.withAlpha(102),// //                   AppColors.transparent,
// //                 ],
// //               ),
// //             ),
// //           ),
// //         ),

// //         // Main scaffold
// //         Scaffold(
// //           backgroundColor: AppColors.transparent,
// //           appBar: appBar,
// //           resizeToAvoidBottomInset: resizeToAvoidBottomInset,
// //           floatingActionButton: floatingActionButton,
// //           floatingActionButtonLocation: floatingActionButtonLocation,
// //           bottomNavigationBar: bottomNavigationBar != null
// //               ? Theme(
// //                   data: Theme.of(context).copyWith(
// //                     canvasColor: AppColors.transparent,
// //                     bottomNavigationBarTheme:
// //                         const BottomNavigationBarThemeData(
// //                       backgroundColor: AppColors.transparent,
// //                       elevation: 0,
// //                     ),
// //                   ),
// //                   child: bottomNavigationBar!,
// //                 )
// //               : null,
// //           extendBody: extendBody,
// //           extendBodyBehindAppBar: extendBodyBehindAppBar,
// //           body: child,
// //         ),
// //       ],
// //     );
// //   }
// // }
