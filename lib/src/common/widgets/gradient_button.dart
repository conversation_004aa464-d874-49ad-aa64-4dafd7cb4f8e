import 'package:flutter/material.dart';
import '../../core/theme/constants/constants_color.dart';

class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final bool isActive;
  final double borderRadius;

  const GradientButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
    this.height,
    this.isActive = true,
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final gradientColors = [
      AppColors.gradientButton1,
      AppColors.gradientButton2,
    ];

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              isActive ? gradientColors : [Colors.grey, Colors.grey.shade700],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          if (isActive)
            BoxShadow(
              color: Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isActive ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isActive
                ? const Color.fromARGB(255, 79, 77, 77)
                : Colors.grey.shade400,
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}
