import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A scaffold widget that provides a gradient background in top corners.
class GradientScaffold extends ConsumerWidget {
  final Widget body;
  final List<Color>? gradientColors;
  final PreferredSizeWidget? appBar;
  final bool? resizeToAvoidBottomInset;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? bottomNavigationBar;
  final bool extendBody;
  final bool extendBodyBehindAppBar;

  const GradientScaffold({
    super.key,
    required this.body,
    this.gradientColors,
    this.appBar,
    this.resizeToAvoidBottomInset,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottomNavigationBar,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Fallback radial gradient colors if not provided
    // final topLeftGradientColor = gradientColors?.first ??
    //     (isDarkMode ? const Color(0xFFEEF6DF) : const Color(0xFFEEF6DF));

    const topLeftGradientColor = AppColors.topLeftGradientColor;
    const topRightGradientColor = AppColors.topRightGradientColor;
    final brightness = Theme.of(context).brightness;
    return Stack(
      children: [
        // Base white background
        Container(color: Theme.of(context).scaffoldBackgroundColor),

        // Top-left corner radial gradient
        Positioned(
          top: 50,
          left: 0,
          child: Container(
            width: 200,
            height: 400,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.centerLeft,
                radius: 1.0,
                colors: [
                  topLeftGradientColor,
                  AppColors.getBackgroundColor(brightness),
                ],
              ),
            ),
          ),
        ),

        // Top-right corner radial gradient
        Positioned(
          top: 00,
          right: 0,
          child: Container(
            width: 200,
            height: 400,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.0,
                colors: [
                  topRightGradientColor.withValues(alpha: 0.4),
                  AppColors.getBackgroundColor(brightness),
                ],
              ),
            ),
          ),
        ),

        // Main scaffold
        Scaffold(
          backgroundColor: AppColors.transparent,
          appBar: appBar,
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
          floatingActionButton: floatingActionButton,
          floatingActionButtonLocation: floatingActionButtonLocation,
          bottomNavigationBar: bottomNavigationBar != null
              ? Theme(
                  data: Theme.of(context).copyWith(
                    canvasColor: AppColors.transparent,
                    bottomNavigationBarTheme:
                        const BottomNavigationBarThemeData(
                      backgroundColor: AppColors.transparent,
                      elevation: 0,
                    ),
                  ),
                  child: bottomNavigationBar!,
                )
              : null,
          extendBody: extendBody,
          extendBodyBehindAppBar: extendBodyBehindAppBar,
          body: body,
        ),
      ],
    );
  }
}
