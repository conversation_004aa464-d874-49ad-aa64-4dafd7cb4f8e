// import 'package:flutter/material.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart'';

// void showSnackbar(context, message) {
//   final snackbar = SnackBar(
//     duration: const Duration(seconds: 1),
//     content: Container(
//       alignment: Alignment.centerLeft,
//       child: Text(
//         message,
//         style: const TextStyle(
//           fontSize: 16,
//         ),
//       ),
//     ),
//     action: SnackBarAction(
//       label: 'ok',
//       onPressed: () {},
//     ),
//   );
//   ScaffoldMessenger.of(context).showSnackBar(snackbar);
// }

// void showSuccessSnackbar(context, snackMessage) {
//   final snackbar = SnackBar(
//     backgroundColor: const Color.fromARGB(255, 94, 186, 97),
//     duration: const Duration(seconds: 1),
//     content: Container(
//       alignment: Alignment.centerLeft,
//       child: Text(
//         snackMessage,
//         style: const TextStyle(
//           fontSize: 16,
//         ),
//       ),
//     ),
//   );
//   ScaffoldMessenger.of(context).showSnackBar(snackbar);
// }

// void showFailureSnackbar(context, snackMessage) {
//   final snackbar = SnackBar(
//     duration: const Duration(seconds: 2),
//     backgroundColor: AppColors.errorColorLight,
//     content: Container(
//       alignment: Alignment.centerLeft,
//       child: Text(
//         snackMessage,
//         style: const TextStyle(fontSize: 16, color: AppColors.textPrimaryLight),
//       ),
//     ),
//   );
//   ScaffoldMessenger.of(context).showSnackBar(snackbar);
// }
