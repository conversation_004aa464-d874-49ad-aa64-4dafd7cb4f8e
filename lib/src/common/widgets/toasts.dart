import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';

//Update Colors as Required in App :

void showSuccessToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.success,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: lowModeShadow,
  );
}

void showFailureToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.error,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: lowModeShadow,
  );
}

void showWarningToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.warning,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: lowModeShadow,
  );
}

void showInfoToast(context,
    {required String title, required String description}) {
  toastification.show(
    context: context,
    type: ToastificationType.info,
    style: ToastificationStyle.flatColored,
    title: Text(title),
    description: Text(description),
    alignment: Alignment.bottomCenter,
    autoCloseDuration: const Duration(seconds: 4),
    // primaryColor: Color(0xff47c6ff),
    // backgroundColor: Color(0xfff2edff),
    // foregroundColor: Color(0xff000000),
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: lowModeShadow,
  );
}
