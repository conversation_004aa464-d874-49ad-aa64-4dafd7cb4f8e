// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart';

// enum ButtonState { idle, loading, success, error }

// class RoundedLoadingButton extends StatefulWidget {
//   final RoundedLoadingButtonController controller;
//   final Widget child;
//   final VoidCallback? onPressed;
//   final Color color;
//   final Color errorColor;
//   final Color successColor;
//   final double height;
//   final double width;
//   final double loaderSize;
//   final Color loaderColor;
//   final double borderRadius;
//   final Duration duration;
//   final double elevation;
//   final IconData successIcon;
//   final IconData errorIcon;
//   final Duration resetDuration;

//   const RoundedLoadingButton({
//     super.key,
//     required this.controller,
//     required this.child,
//     this.onPressed,
//     this.color = AppColors.infoColorLight,
//     this.errorColor = AppColors.errorColorLight,
//     this.successColor = AppColors.successColorLight,
//     this.height = 50.0,
//     this.width = 200.0,
//     this.loaderSize = 24.0,
//     this.loaderColor = AppColors.textPrimaryLight,
//     this.borderRadius = 35.0,
//     this.duration = const Duration(milliseconds: 500),
//     this.elevation = 2.0,
//     this.successIcon = Icons.check,
//     this.errorIcon = Icons.close,
//     this.resetDuration = const Duration(seconds: 2),
//   });

//   @override
//   State<RoundedLoadingButton> createState() => _RoundedLoadingButtonState();
// }

// class _RoundedLoadingButtonState extends State<RoundedLoadingButton>
//     with TickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _animation;
//   ButtonState _currentState = ButtonState.idle;

//   @override
//   void initState() {
//     super.initState();

//     _controller = AnimationController(
//       duration: widget.duration,
//       vsync: this,
//     );

//     _animation = Tween<double>(
//       begin: widget.width,
//       end: widget.height,
//     ).animate(CurvedAnimation(
//       parent: _controller,
//       curve: Curves.easeInOut,
//     ));

//     widget.controller._bind(this);
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   void _stopLoading() {
//     setState(() {
//       _currentState = ButtonState.idle;
//     });
//     _controller.reverse();
//   }

//   void _startLoading() {
//     if (!mounted) return;
//     setState(() {
//       _currentState = ButtonState.loading;
//     });
//     _controller.forward();
//   }

//   void _showSuccess() {
//     if (!mounted) return;
//     setState(() {
//       _currentState = ButtonState.success;
//     });
//     Timer(widget.resetDuration, _resetState);
//   }

//   void _showError() {
//     if (!mounted) return;
//     setState(() {
//       _currentState = ButtonState.error;
//     });
//     Timer(widget.resetDuration, _resetState);
//   }

//   void _resetState() {
//     if (!mounted) return; // Ensure widget is still part of the tree
//     setState(() {
//       _currentState = ButtonState.idle;
//     });
//     _controller.reverse();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return AnimatedBuilder(
//       animation: _animation,
//       builder: (context, child) {
//         return SizedBox(
//           height: widget.height,
//           width: _animation.value,
//           child: ElevatedButton(
//             style: ElevatedButton.styleFrom(
//               backgroundColor: _getBackgroundColor(),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(widget.borderRadius),
//               ),
//               elevation: widget.elevation,
//             ),
//             onPressed:
//                 widget.onPressed == null || _currentState != ButtonState.idle
//                     ? null
//                     : () {
//                         _startLoading();
//                         widget.onPressed?.call();
//                       },
//             child: _buildChild(),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildChild() {
//     if (_currentState == ButtonState.loading) {
//       return Center(
//         child: SizedBox(
//           height: widget.loaderSize, // Set explicit height
//           width: widget.loaderSize, // Set explicit width
//           child: CircularProgressIndicator(
//             color: widget.loaderColor,
//             strokeWidth: 2.0,
//           ),
//         ),
//       );
//     } else if (_currentState == ButtonState.success) {
//       return Icon(widget.successIcon, color: AppColors.textPrimaryLight);
//     } else if (_currentState == ButtonState.error) {
//       return Icon(widget.errorIcon, color: AppColors.textPrimaryLight);
//     }
//     return widget.child;
//   }

//   Color _getBackgroundColor() {
//     if (_currentState == ButtonState.success) {
//       return widget.successColor;
//     } else if (_currentState == ButtonState.error) {
//       return widget.errorColor;
//     }
//     return widget.color;
//   }
// }

// class RoundedLoadingButtonController {
//   late _RoundedLoadingButtonState _state;

//   void _bind(_RoundedLoadingButtonState state) {
//     _state = state;
//   }

//   void start() => _state._startLoading();

//   void stop() => _state._stopLoading();

//   void success() => _state._showSuccess();

//   void error() => _state._showError();

//   void reset() => _state._resetState();
// }
