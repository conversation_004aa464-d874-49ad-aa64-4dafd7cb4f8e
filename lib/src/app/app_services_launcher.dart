import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/ads/providers/ad_providers.dart';
import 'package:vocadex/src/features/ads/providers/admob_config_provider.dart';
import 'package:vocadex/src/features/notifications/services/notification_service.dart';

/// Launches and initializes essential app services
class AppServicesLauncher {
  // final AchievementManager _achievementManager = AchievementManager();
  // final MasteryLevelManager _masteryManager = MasteryLevelManager();
  // final StreakTracker _streakTracker = StreakTracker();

  /// Initialize all app services
  Future<void> initializeServices() async {
    // Wait for Firebase Auth to initialize
    await _waitForAuthInitialization();

    // Initialize achievements for the user
    // await _achievementManager.initializeAchievements();

    // // Record daily visit for streak tracking
    // await _streakTracker.recordDailyVisit();

    // // Apply mastery decay for cards not reviewed in a while
    // await _masteryManager.applyMasteryDecay();

    debugPrint('App services initialized successfully');
  }

  /// Wait for Firebase Auth to initialize
  Future<void> _waitForAuthInitialization() async {
    // Wait for Firebase Auth to initialize
    await FirebaseAuth.instance.authStateChanges().first;
  }
}

/// Provider for the app services launcher
final appServicesLauncherProvider = Provider<AppServicesLauncher>((ref) {
  return AppServicesLauncher();
});

/// Provider that initializes app services when the app starts
final initializeAppServicesProvider = FutureProvider<void>((ref) async {
  final launcher = ref.read(appServicesLauncherProvider);

  // Initialize notification service
  try {
    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.initialize();
    debugPrint('Notification service initialized');
  } catch (e) {
    debugPrint('Error initializing notification service: $e');
  }

  // Initialize AdMob config
  try {
    final admobConfigService = ref.read(admobConfigServiceProvider);
    await admobConfigService.initializeAdMobConfig();
    debugPrint('AdMob config initialized');
  } catch (e) {
    debugPrint('Error initializing AdMob config: $e');
  }

  // Initialize ad service
  try {
    final adService = ref.read(adServiceProvider);
    await adService.initialize();
    debugPrint('Ad service initialized');
  } catch (e) {
    debugPrint('Error initializing ad service: $e');
  }

  return await launcher.initializeServices();
});

/// Widget that initializes app services when inserted in the widget tree
class AppServicesInitializer extends ConsumerWidget {
  final Widget child;

  const AppServicesInitializer({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Initialize services when widget is built
    ref.watch(initializeAppServicesProvider);

    // Return child widget
    return child;
  }
}
