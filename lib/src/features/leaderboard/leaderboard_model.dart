import 'package:cloud_firestore/cloud_firestore.dart';

/// Represents a user entry in the leaderboard
class LeaderboardEntry {
  final String userId;
  final String displayName;
  final String? imageUrl;
  final int totalPoints;
  final int quizzesTaken;
  final DateTime lastUpdated;
  final int rank; // Current rank on the leaderboard

  LeaderboardEntry({
    required this.userId,
    required this.displayName,
    this.imageUrl,
    required this.totalPoints,
    required this.quizzesTaken,
    required this.lastUpdated,
    required this.rank,
  });

  /// Create a [LeaderboardEntry] from a Firestore document
  factory LeaderboardEntry.fromFirestore(DocumentSnapshot doc, int rank) {
    final data = doc.data() as Map<String, dynamic>;

    return LeaderboardEntry(
      userId: doc.id,
      displayName: data['displayName'] ?? 'Anonymous',
      imageUrl: data['imageUrl'],
      totalPoints: data['totalPoints'] ?? 0,
      quizzesTaken: data['quizzesTaken'] ?? 0,
      lastUpdated: data['lastUpdated'] != null
          ? (data['lastUpdated'] as Timestamp).toDate()
          : DateTime.now(),
      rank: rank,
    );
  }

  /// Convert this entry to a Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'displayName': displayName,
      'imageUrl': imageUrl,
      'totalPoints': totalPoints,
      'quizzesTaken': quizzesTaken,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }
}

/// Different time periods for the leaderboard
enum LeaderboardPeriod {
  daily,
  weekly,
  monthly,
  allTime,
}

extension LeaderboardPeriodExtension on LeaderboardPeriod {
  String get displayName {
    switch (this) {
      case LeaderboardPeriod.daily:
        return 'Today';
      case LeaderboardPeriod.weekly:
        return 'This Week';
      case LeaderboardPeriod.monthly:
        return 'This Month';
      case LeaderboardPeriod.allTime:
        return 'All Time';
    }
  }

  String get collectionName {
    switch (this) {
      case LeaderboardPeriod.daily:
        return 'leaderboard_daily';
      case LeaderboardPeriod.weekly:
        return 'leaderboard_weekly';
      case LeaderboardPeriod.monthly:
        return 'leaderboard_monthly';
      case LeaderboardPeriod.allTime:
        return 'leaderboard_all_time';
    }
  }
}
