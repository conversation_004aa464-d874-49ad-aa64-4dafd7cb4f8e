import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/leaderboard/leaderboard_model.dart';
import 'package:vocadex/src/services/leaderboard_service.dart';

/// Provider for the leaderboard service
final leaderboardServiceProvider = Provider<LeaderboardService>((ref) {
  return LeaderboardService();
});

/// Provider for the selected leaderboard period
final selectedPeriodProvider = StateProvider<LeaderboardPeriod>((ref) {
  return LeaderboardPeriod.weekly; // Default to weekly
});

/// Provider for the leaderboard entries based on the selected period
final leaderboardProvider = FutureProvider<List<LeaderboardEntry>>((ref) async {
  final leaderboardService = ref.watch(leaderboardServiceProvider);
  final selectedPeriod = ref.watch(selectedPeriodProvider);

  // Check if leaderboards need to be reset (should ideally be done server-side)
  await leaderboardService.resetDailyLeaderboardIfNeeded();
  await leaderboardService.resetWeeklyLeaderboardIfNeeded();
  await leaderboardService.resetMonthlyLeaderboardIfNeeded();

  return leaderboardService.getLeaderboard(selectedPeriod);
});

/// Provider for the current user's ranking
final userRankingProvider = FutureProvider<LeaderboardEntry?>((ref) async {
  final leaderboardService = ref.watch(leaderboardServiceProvider);
  final selectedPeriod = ref.watch(selectedPeriodProvider);

  return leaderboardService.getCurrentUserRanking(selectedPeriod);
});
