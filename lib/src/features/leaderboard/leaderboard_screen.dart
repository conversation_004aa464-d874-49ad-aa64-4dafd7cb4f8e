import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/leaderboard/leaderboard_model.dart';
import 'package:vocadex/src/features/leaderboard/leaderboard_providers.dart';
import 'package:vocadex/src/features/user/models/user_model.dart';
import 'package:vocadex/src/features/stats_screen/user_stats_screen.dart';

class LeaderboardScreen extends ConsumerWidget {
  final bool showAppBar;

  const LeaderboardScreen({
    Key? key,
    this.showAppBar = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPeriod = ref.watch(selectedPeriodProvider);
    final leaderboardAsync = ref.watch(leaderboardProvider);
    final userRankingAsync = ref.watch(userRankingProvider);
    final userAsync = ref.watch(currentUserProvider);

    return showAppBar
        ? Scaffold(
            appBar: AppBar(
              title: const Text('Leaderboard'),
              centerTitle: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () => Navigator.of(context).pop(),
              ),
              backgroundColor: AppColors.transparent,
              elevation: 0,
            ),
            body: _buildLeaderboardContent(context, ref, selectedPeriod,
                leaderboardAsync, userRankingAsync, userAsync),
          )
        : _buildLeaderboardContent(
            context, ref, selectedPeriod, leaderboardAsync, userRankingAsync, userAsync);
  }

  Widget _buildLeaderboardContent(
    BuildContext context,
    WidgetRef ref,
    LeaderboardPeriod selectedPeriod,
    AsyncValue<List<LeaderboardEntry>> leaderboardAsync,
    AsyncValue<LeaderboardEntry?> userRankingAsync,
    AsyncValue<UserModel?> userAsync,
  ) {
    return Container(
      color: AppColors.getBackgroundColor(Theme.of(context).brightness),
      child: Column(
        children: [
          // User profile section
          _buildUserProfileSection(context, userAsync, userRankingAsync),

          // Top 3 podium
          _buildTopThreePodium(context, leaderboardAsync),

          // Leaderboard list
          Expanded(
            child: leaderboardAsync.when(
              data: (entries) {
                if (entries.isEmpty) {
                  return const Center(
                    child: Text(
                      'No entries found for this period',
                      style: TextStyle(fontSize: 16),
                    ),
                  );
                }

                return _buildLeaderboardList(context, entries, ref);
              },
              error: (error, stackTrace) => Center(
                child: Text('Error: ${error.toString()}'),
              ),
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildTopThreePodium(
    BuildContext context,
    AsyncValue<List<LeaderboardEntry>> leaderboardAsync,
  ) {
    return leaderboardAsync.when(
      data: (entries) {
        if (entries.isEmpty) {
          return const SizedBox(height: 200);
        }

        // Get top 3 users or as many as available
        final List<LeaderboardEntry> topEntries = [];
        for (int i = 0; i < 3 && i < entries.length; i++) {
          topEntries.add(entries[i]);
        }
        
        // Fill with placeholders if needed
        while (topEntries.length < 3) {
          topEntries.add(LeaderboardEntry(
            userId: '',
            displayName: 'User',
            imageUrl: null,
            totalPoints: 0,
            quizzesTaken: 0,
            lastUpdated: DateTime.now(),
            rank: topEntries.length + 1,
          ));
        }

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Third Place (Left)
              Expanded(
                child: _buildPodiumItem(context, topEntries[2], 3),
              ),

              // First Place (Center)
              Expanded(
                child: _buildPodiumItem(context, topEntries[0], 1),
              ),

              // Second Place (Right)
              Expanded(
                child: _buildPodiumItem(context, topEntries[1], 2),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (_, __) => const SizedBox(height: 200),
    );
  }

  Widget _buildPodiumItem(BuildContext context, LeaderboardEntry entry, int position) {
    // Colors for each position
    final baseColors = {
      1: AppColors.topRightGradientColor, // First place (gold/orange)
      2: AppColors.getSecondaryColor(Theme.of(context).brightness), // Second place (silver)
      3: AppColors.getWarningColor(Theme.of(context).brightness), // Third place (bronze)
    };

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Position number
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: position == 1 
                ? AppColors.topRightGradientColor 
                : position == 2 
                    ? AppColors.secondaryLight 
                    : AppColors.warningLight,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              position.toString(),
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        
        // User avatar
        CircleAvatar(
          radius: 30,
          backgroundColor: AppColors.white,
          child: entry.imageUrl != null
              ? ClipOval(
                  child: Image.network(
                    entry.imageUrl!,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (_, __, ___) => Text(
                      entry.displayName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        color: baseColors[position],
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ),
                )
              : Text(
                  entry.displayName.substring(0, 1).toUpperCase(),
                  style: TextStyle(
                    color: baseColors[position],
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
        ),
        const SizedBox(height: 8),
        
        // User name
        Text(
          entry.displayName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
        
        // Podium
        Container(
          width: 60,
          height: position == 1 ? 100 : position == 2 ? 80 : 60,
          decoration: BoxDecoration(
            color: baseColors[position]!.withAlpha(76),borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserProfileSection(
    BuildContext context,
    AsyncValue<UserModel?> userAsync,
    AsyncValue<LeaderboardEntry?> userRankingAsync,
  ) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(178),borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(13),blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // User profile image
          userAsync.when(
            data: (user) => CircleAvatar(
              radius: 36,
              backgroundColor: AppColors.topRightGradientColor.withAlpha(51),child: user?.imageUrl != null && user!.imageUrl!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(36),
                      child: Image.network(
                        user.imageUrl!,
                        width: 72,
                        height: 72,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.person,
                          size: 36,
                          color: AppColors.topRightGradientColor,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: 36,
                      color: AppColors.topRightGradientColor,
                    ),
            ),
            loading: () => const CircleAvatar(
              radius: 36,
              child: CircularProgressIndicator(),
            ),
            error: (_, __) => CircleAvatar(
              radius: 36,
              backgroundColor: AppColors.topRightGradientColor.withAlpha(51),child: Icon(
                Icons.person,
                size: 36,
                color: AppColors.topRightGradientColor,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // User stats
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User name
                userAsync.when(
                  data: (user) => Text(
                    '${user?.firstName ?? 'User'} ${user?.lastName ?? ''}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  loading: () => const Text('Loading...'),
                  error: (_, __) => const Text('User'),
                ),

                const SizedBox(height: 8),

                // Stats grid
                userRankingAsync.when(
                  data: (userEntry) {
                    if (userEntry == null) {
                      return const Text('No leaderboard data available');
                    }

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildStatItem('Points', userEntry.totalPoints.toString()),
                        _buildStatItem('Level', _getLevel(userEntry.totalPoints)),
                        _buildStatItem('Rank', '#${userEntry.rank}'),
                      ],
                    );
                  },
                  loading: () => const LinearProgressIndicator(),
                  error: (_, __) => const Text('Error loading statistics'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppColors.grey.withAlpha(178),fontSize: 12,
          ),
        ),
      ],
    );
  }





  String _getLevel(int points) {
    if (points >= 1000) return 'Gold';
    if (points >= 500) return 'Silver';
    if (points >= 200) return 'Bronze';
    return 'Starter';
  }

  Widget _buildLeaderboardList(
    BuildContext context,
    List<LeaderboardEntry> entries,
    WidgetRef ref,
  ) {
    // Get the current user ID for highlighting
    final leaderboardService = ref.read(leaderboardServiceProvider);
    final currentUserId = leaderboardService.userId;

    // Skip the top 3 entries that are shown in the podium
    final remainingEntries = entries.where((e) => e.rank > 3).toList();

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: remainingEntries.length,
      itemBuilder: (context, index) {
        final entry = remainingEntries[index];
        final isCurrentUser = entry.userId == currentUserId;
        final rank = index + 4; // Start from rank 4

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: isCurrentUser
                ? AppColors.topRightGradientColor.withAlpha(26): AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withAlpha(13),blurRadius: 3,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Rank number
              SizedBox(
                width: 30,
                child: Text(
                  '${rank < 10 ? "0$rank" : rank}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: _getRankColor(rank),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // User name and crown icon for high ranks
              Expanded(
                child: Row(
                  children: [
                    Text(
                      entry.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 4),
                    if (rank <= 10)
                      Icon(
                        rank <= 3 ? Icons.workspace_premium : Icons.emoji_events,
                        color: _getCrownColor(rank),
                        size: 16,
                      ),
                  ],
                ),
              ),

              // Points
              Text(
                '${entry.totalPoints} points',
                style: TextStyle(
                  color: isCurrentUser ? AppColors.warningLight : AppColors.black.withAlpha(138),fontWeight: isCurrentUser ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getRankColor(int rank) {
    // Use hardcoded colors for now to avoid context issues
    if (rank <= 10) return AppColors.topRightGradientColor;
    if (rank <= 20) return AppColors.primaryLight;
    if (rank <= 30) return AppColors.secondaryLight;
    return AppColors.black;
  }

  Color _getCrownColor(int rank) {
    // Use hardcoded colors for now to avoid context issues
    if (rank <= 3) return AppColors.topRightGradientColor; // Gold
    if (rank <= 6) return AppColors.secondaryLight; // Silver
    return AppColors.warningLight; // Bronze
  }
}
