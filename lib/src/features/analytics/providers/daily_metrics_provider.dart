import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Provider for tracking daily metrics
final dailyMetricsProvider = StateNotifierProvider<DailyMetricsNotifier, DailyMetrics>((ref) {
  return DailyMetricsNotifier();
});

/// Daily metrics data model
class DailyMetrics {
  final int wordsAdded;
  final int quizzesAttempted;
  final int quizzesCompleted;
  final int sessionCount;
  final DateTime lastUpdated;

  const DailyMetrics({
    this.wordsAdded = 0,
    this.quizzesAttempted = 0,
    this.quizzesCompleted = 0,
    this.sessionCount = 0,
    required this.lastUpdated,
  });

  DailyMetrics copyWith({
    int? wordsAdded,
    int? quizzesAttempted,
    int? quizzesCompleted,
    int? sessionCount,
    DateTime? lastUpdated,
  }) {
    return DailyMetrics(
      wordsAdded: wordsAdded ?? this.wordsAdded,
      quizzesAttempted: quizzesAttempted ?? this.quizzesAttempted,
      quizzesCompleted: quizzesCompleted ?? this.quizzesCompleted,
      sessionCount: sessionCount ?? this.sessionCount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'words_added': wordsAdded,
      'quizzes_attempted': quizzesAttempted,
      'quizzes_completed': quizzesCompleted,
      'session_count': sessionCount,
      'last_updated': Timestamp.fromDate(lastUpdated),
      'date': _getDateString(lastUpdated),
    };
  }

  factory DailyMetrics.fromMap(Map<String, dynamic> map) {
    return DailyMetrics(
      wordsAdded: map['words_added'] ?? 0,
      quizzesAttempted: map['quizzes_attempted'] ?? 0,
      quizzesCompleted: map['quizzes_completed'] ?? 0,
      sessionCount: map['session_count'] ?? 0,
      lastUpdated: (map['last_updated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  static String _getDateString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// Notifier for managing daily metrics
class DailyMetricsNotifier extends StateNotifier<DailyMetrics> {
  DailyMetricsNotifier() : super(DailyMetrics(lastUpdated: DateTime.now())) {
    _loadTodaysMetrics();
  }

  final FirebaseService _firebaseService = FirebaseService();

  /// Load today's metrics from Firestore
  Future<void> _loadTodaysMetrics() async {
    try {
      final today = DateTime.now();
      final dateString = DailyMetrics._getDateString(today);
      
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_firebaseService.userId)
          .collection('daily_metrics')
          .doc(dateString)
          .get();

      if (doc.exists) {
        state = DailyMetrics.fromMap(doc.data()!);
      } else {
        // Create today's document
        state = DailyMetrics(lastUpdated: today);
        await _saveTodaysMetrics();
      }
    } catch (e) {
      print('Error loading daily metrics: $e');
    }
  }

  /// Save current metrics to Firestore
  Future<void> _saveTodaysMetrics() async {
    try {
      final dateString = DailyMetrics._getDateString(state.lastUpdated);
      
      await FirebaseFirestore.instance
          .collection('users')
          .doc(_firebaseService.userId)
          .collection('daily_metrics')
          .doc(dateString)
          .set(state.toMap(), SetOptions(merge: true));
    } catch (e) {
      print('Error saving daily metrics: $e');
    }
  }

  /// Check if we need to reset metrics for a new day
  void _checkAndResetForNewDay() {
    final now = DateTime.now();
    final lastUpdate = state.lastUpdated;
    
    // If it's a new day, reset the metrics
    if (now.day != lastUpdate.day || 
        now.month != lastUpdate.month || 
        now.year != lastUpdate.year) {
      state = DailyMetrics(lastUpdated: now);
      _saveTodaysMetrics();
    }
  }

  /// Increment words added count
  Future<void> incrementWordsAdded() async {
    _checkAndResetForNewDay();
    state = state.copyWith(
      wordsAdded: state.wordsAdded + 1,
      lastUpdated: DateTime.now(),
    );
    await _saveTodaysMetrics();
  }

  /// Increment quizzes attempted count
  Future<void> incrementQuizzesAttempted() async {
    _checkAndResetForNewDay();
    state = state.copyWith(
      quizzesAttempted: state.quizzesAttempted + 1,
      lastUpdated: DateTime.now(),
    );
    await _saveTodaysMetrics();
  }

  /// Increment quizzes completed count
  Future<void> incrementQuizzesCompleted() async {
    _checkAndResetForNewDay();
    state = state.copyWith(
      quizzesCompleted: state.quizzesCompleted + 1,
      lastUpdated: DateTime.now(),
    );
    await _saveTodaysMetrics();
  }

  /// Increment session count
  Future<void> incrementSessionCount() async {
    _checkAndResetForNewDay();
    state = state.copyWith(
      sessionCount: state.sessionCount + 1,
      lastUpdated: DateTime.now(),
    );
    await _saveTodaysMetrics();
  }

  /// Get metrics for a specific date
  Future<DailyMetrics?> getMetricsForDate(DateTime date) async {
    try {
      final dateString = DailyMetrics._getDateString(date);
      
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_firebaseService.userId)
          .collection('daily_metrics')
          .doc(dateString)
          .get();

      if (doc.exists) {
        return DailyMetrics.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      print('Error getting metrics for date: $e');
      return null;
    }
  }

  /// Get metrics for the last N days
  Future<List<DailyMetrics>> getMetricsForLastDays(int days) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days - 1));
      
      final query = await FirebaseFirestore.instance
          .collection('users')
          .doc(_firebaseService.userId)
          .collection('daily_metrics')
          .where('last_updated', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('last_updated', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('last_updated', descending: false)
          .get();

      return query.docs
          .map((doc) => DailyMetrics.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error getting metrics for last days: $e');
      return [];
    }
  }
}

/// Provider for weekly metrics summary
final weeklyMetricsProvider = FutureProvider<Map<String, int>>((ref) async {
  final notifier = ref.read(dailyMetricsProvider.notifier);
  final weeklyMetrics = await notifier.getMetricsForLastDays(7);
  
  int totalWords = 0;
  int totalQuizzes = 0;
  int totalSessions = 0;
  
  for (final metric in weeklyMetrics) {
    totalWords += metric.wordsAdded;
    totalQuizzes += metric.quizzesCompleted;
    totalSessions += metric.sessionCount;
  }
  
  return {
    'total_words': totalWords,
    'total_quizzes': totalQuizzes,
    'total_sessions': totalSessions,
    'active_days': weeklyMetrics.where((m) => m.wordsAdded > 0 || m.quizzesCompleted > 0).length,
  };
});

/// Provider for monthly metrics summary
final monthlyMetricsProvider = FutureProvider<Map<String, int>>((ref) async {
  final notifier = ref.read(dailyMetricsProvider.notifier);
  final monthlyMetrics = await notifier.getMetricsForLastDays(30);
  
  int totalWords = 0;
  int totalQuizzes = 0;
  int totalSessions = 0;
  
  for (final metric in monthlyMetrics) {
    totalWords += metric.wordsAdded;
    totalQuizzes += metric.quizzesCompleted;
    totalSessions += metric.sessionCount;
  }
  
  return {
    'total_words': totalWords,
    'total_quizzes': totalQuizzes,
    'total_sessions': totalSessions,
    'active_days': monthlyMetrics.where((m) => m.wordsAdded > 0 || m.quizzesCompleted > 0).length,
  };
});
