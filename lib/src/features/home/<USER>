// import 'package:vocadex/src/features/user/provider/userdata_provider.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/features/achievements/achievement_manager.dart';
// import 'package:vocadex/src/features/achievements/achievement_model.dart';
// import 'package:vocadex/src/features/achievements/achievement_screen.dart';
// import 'package:vocadex/src/features/achievements/achievement_widget.dart';
// import 'package:vocadex/src/features/masterylevel/mastery_level_manager.dart';
// import 'package:vocadex/src/features/masterylevel/streak_provider.dart';
// import 'package:vocadex/src/features/masterylevel/streak_tracker.dart';
// import 'package:vocadex/src/features/masterylevel/points_manager.dart';
// import 'package:vocadex/src/features/user/models/user_model.dart';
// import 'package:vocadex/src/features/user/provider/userdata_provider.dart';
// import 'package:vocadex/src/router/app_router.dart';
// import 'package:vocadex/src/services/firebase_service.dart';
// import 'package:vocadex/src/theme/theme_provider.dart';
// import 'package:vocadex/src/theme/theme_switcher.dart';
// import 'package:go_router/go_router.dart';

// final userData = ref.watch(userDataProvider);

// Widget _buildProfileHeader(
//   BuildContext context,
//   UserModel? userData,
//   bool isDarkMode,
// ) {
//   return Card(
//     elevation: 2,
//     shape: RoundedRectangleBorder(
//       borderRadius: BorderRadius.circular(16),
//     ),
//     child: Padding(
//       padding: const EdgeInsets.all(16.0),
//       child: Row(
//         children: [
//           // Profile image
//           Container(
//             width: 80,
//             height: 80,
//             decoration: BoxDecoration(
//               shape: BoxShape.circle,
//               color: Theme.of(context).primaryColor.withAlpha(26),//               border: Border.all(
//                 color: Theme.of(context).primaryColor,
//                 width: 2,
//               ),
//             ),
//             clipBehavior: Clip.antiAlias,
//             child: Center(
//               child: userData?.imageUrl != null
//                   ? ClipOval(
//                       child: Image.network(
//                         userData!.imageUrl!,
//                         width: 80,
//                         height: 80,
//                         fit: BoxFit.cover,
//                       ),
//                     )
//                   : Text(
//                       userData?.name.isNotEmpty == true
//                           ? userData!.name[0].toUpperCase()
//                           : '?',
//                       style: TextStyle(
//                         fontSize: 32,
//                         fontWeight: FontWeight.bold,
//                         color: Theme.of(context).primaryColor,
//                       ),
//                     ),
//             ),
//           ),
//           const SizedBox(width: 16),

//           // User info
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   userData?.name ?? 'User',
//                   style: const TextStyle(
//                     fontSize: 20,
//                     fontWeight: FontWeight.bold,
//                   ),
//                   maxLines: 1,
//                   overflow: TextOverflow.ellipsis,
//                 ),
//                 const SizedBox(height: 4),
//                 Text(
//                   userData?.email ?? 'No email',
//                   style: TextStyle(
//                     fontSize: 14,
//                     color: AppColors.grey600,
//                   ),
//                   maxLines: 1,
//                   overflow: TextOverflow.ellipsis,
//                 ),
//                 const SizedBox(height: 8),
//                 Row(
//                   children: [
//                     Icon(
//                       userData?.isAnonymous == true
//                           ? Icons.person_outline
//                           : Icons.verified_user,
//                       size: 14,
//                       color: userData?.isAnonymous == true
//                           ? AppColors.grey600
//                           : AppColors.green,
//                     ),
//                     const SizedBox(width: 4),
//                     Text(
//                       userData?.isAnonymous == true
//                           ? 'Guest User'
//                           : 'Registered User',
//                       style: TextStyle(
//                         fontSize: 12,
//                         color: userData?.isAnonymous == true
//                             ? AppColors.grey600
//                             : AppColors.green,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     ),
//   );
// }
