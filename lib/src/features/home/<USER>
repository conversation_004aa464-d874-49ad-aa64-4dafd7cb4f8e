// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// /// Provider for the total number of vocabulary cards
// final totalCardsProvider = FutureProvider<int>((ref) async {
//   final firebaseService = FirebaseService();
//   try {
//     final cards = await firebaseService.fetchVocabulary();
//     return cards.length;
//   } catch (e) {
//     debugPrint('Error fetching total cards: $e');
//     return 0;
//   }
// });

// /// A widget that displays the total number of vocabulary cards collected
// class TotalCardsWidget extends ConsumerWidget {
//   final VoidCallback? onTap;

//   const TotalCardsWidget({
//     super.key,
//     this.onTap,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final totalCardsAsync = ref.watch(totalCardsProvider);
//     final primaryColor = Theme.of(context).primaryColor;

//     return GestureDetector(
//       onTap: onTap,
//       child: Card(
//         elevation: 4,
//         shadowColor: AppColors.black.withAlpha(51),//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(20),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Top row with count and character
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Left side - count and label
//                   Expanded(
//                     child: totalCardsAsync.when(
//                       data: (totalCards) => Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             '$totalCards',
//                             style: const TextStyle(
//                               fontSize: 48,
//                               fontWeight: FontWeight.bold,
//                               color: AppColors.black,
//                               height: 1,
//                             ),
//                           ),
//                           const SizedBox(height: 4),
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8, vertical: 4),
//                             decoration: BoxDecoration(
//                               color: Colors.lightGreen.withAlpha(51),//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                             child: const Text(
//                               'Words collected',
//                               style: TextStyle(
//                                 fontSize: 14,
//                                 color: AppColors.green,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                       loading: () => Row(
//                         children: [
//                           SizedBox(
//                             width: 48,
//                             height: 48,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 3,
//                               valueColor:
//                                   AlwaysStoppedAnimation<Color>(primaryColor),
//                             ),
//                           ),
//                           const SizedBox(width: 16),
//                           const Text(
//                             'Loading collection...',
//                             style: TextStyle(
//                               fontSize: 16,
//                               fontWeight: FontWeight.w500,
//                             ),
//                           ),
//                         ],
//                       ),
//                       error: (error, stack) => Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           const Text(
//                             '0',
//                             style: TextStyle(
//                               fontSize: 48,
//                               fontWeight: FontWeight.bold,
//                               color: AppColors.black,
//                               height: 1,
//                             ),
//                           ),
//                           const SizedBox(height: 4),
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8, vertical: 4),
//                             decoration: BoxDecoration(
//                               color: AppColors.grey.withAlpha(51),//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                             child: const Text(
//                               'Words collected',
//                               style: TextStyle(
//                                 fontSize: 14,
//                                 color: AppColors.grey,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),

//                   // Right side - Character
//                   _buildCharacterSection(context, totalCardsAsync),
//                 ],
//               ),

//               const SizedBox(height: 16),

//               // Bottom row with buttons
//               Row(
//                 children: [
//                   ElevatedButton.icon(
//                     onPressed: onTap,
//                     icon: const Icon(Icons.menu_book, size: 16),
//                     label: const Text('View Collection'),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: primaryColor,
//                       foregroundColor: AppColors.white,
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 12, vertical: 8),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                     ),
//                   ),
//                   const SizedBox(width: 8),
//                   TextButton(
//                     onPressed: () {
//                       // Show suggested words
//                     },
//                     child: const Text('Suggested Words'),
//                     style: TextButton.styleFrom(
//                       foregroundColor: primaryColor,
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 12, vertical: 8),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildCharacterSection(
//       BuildContext context, AsyncValue<int> totalCardsAsync) {
//     bool hasCards = totalCardsAsync.maybeWhen(
//       data: (count) => count > 0,
//       orElse: () => false,
//     );

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         // Speech bubble (only shown if there are cards)
//         if (hasCards)
//           Container(
//             margin: const EdgeInsets.only(bottom: 4),
//             padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//             decoration: BoxDecoration(
//               color: AppColors.white,
//               borderRadius: BorderRadius.circular(12),
//               boxShadow: [
//                 BoxShadow(
//                   color: AppColors.black.withAlpha(26),//                   blurRadius: 4,
//                   offset: const Offset(0, 2),
//                 ),
//               ],
//               border: Border.all(color: AppColors.grey),
//             ),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Icon(
//                   Icons.favorite,
//                   color: AppColors.red[300],
//                   size: 12,
//                 ),
//                 const SizedBox(width: 4),
//                 const Text(
//                   'Growing!',
//                   style: TextStyle(
//                     fontSize: 10,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               ],
//             ),
//           ),

//         // Character
//         SizedBox(
//           height: 70,
//           width: 70,
//           child: Container(
//             height: 100,
//             width: 100,
//             child: totalCardsAsync.when(
//               data: (count) =>
//                   count > 0 ? _buildChippyRegular() : _buildChippyStunned(),
//               loading: () => _buildChippyRegular(),
//               error: (_, __) => _buildChippyStunned(),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   // Function to build the Chippy character in regular state
//   Widget _buildChippyRegular() {
//     try {
//       return SvgPicture.asset(
//         'lib/assets/images/chippy_regular.svg',
//         fit: BoxFit.cover,
//       );
//     } catch (e) {
//       // Fallback if SVG loading fails
//       return Container(
//         child: const Center(
//           child: Text(
//             ':)',
//             style: TextStyle(color: AppColors.white, fontSize: 30),
//           ),
//         ),
//       );
//     }
//   }

//   // Function to build the Chippy character in stunned state
//   Widget _buildChippyStunned() {
//     try {
//       return SvgPicture.asset(
//         'lib/assets/images/chippy_stunned.svg',
//         fit: BoxFit.cover,
//       );
//     } catch (e) {
//       // Fallback if SVG loading fails
//       return Container(
//         child: const Center(
//           child: Text(
//             ':o',
//             style: TextStyle(color: AppColors.white, fontSize: 30),
//           ),
//         ),
//       );
//     }
//   }
// }

// /// A more compact version of the total cards widget
// class CompactTotalCardsWidget extends ConsumerWidget {
//   final VoidCallback? onTap;

//   const CompactTotalCardsWidget({
//     super.key,
//     this.onTap,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final totalCardsAsync = ref.watch(totalCardsProvider);

//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: InkWell(
//         onTap: onTap,
//         borderRadius: BorderRadius.circular(12),
//         child: Padding(
//           padding: const EdgeInsets.all(12.0),
//           child: Row(
//             children: [
//               Container(
//                 width: 48,
//                 height: 48,
//                 decoration: BoxDecoration(
//                   color: Theme.of(context).primaryColor.withAlpha(26),//                   shape: BoxShape.circle,
//                 ),
//                 child: Icon(
//                   Icons.menu_book,
//                   color: Theme.of(context).primaryColor,
//                 ),
//               ),
//               const SizedBox(width: 12),
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'Vocabulary Collection',
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 4),
//                     totalCardsAsync.when(
//                       data: (totalCards) => Text(
//                         '$totalCards cards collected',
//                         style: TextStyle(
//                           color: AppColors.grey600,
//                           fontSize: 12,
//                         ),
//                       ),
//                       loading: () => Text(
//                         'Loading...',
//                         style: TextStyle(
//                           color: AppColors.grey600,
//                           fontSize: 12,
//                         ),
//                       ),
//                       error: (error, stack) => Text(
//                         '0 cards collected',
//                         style: TextStyle(
//                           color: AppColors.grey600,
//                           fontSize: 12,
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               const Icon(
//                 Icons.arrow_forward_ios,
//                 size: 16,
//                 color: AppColors.grey,
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
