import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/manual_entry_dialog.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_capture/services/ocr_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// A dialog for capturing vocabulary from images
/// This version uses the ManualEntryDialog for text editing
class ImageCaptureDialog extends ConsumerStatefulWidget {
  /// The image source
  final ImageSource source;

  /// Constructor
  const ImageCaptureDialog({
    super.key,
    required this.source,
  });

  @override
  ConsumerState<ImageCaptureDialog> createState() => _ImageCaptureDialogState();
}

class _ImageCaptureDialogState extends ConsumerState<ImageCaptureDialog> {
  final OcrService _ocrService = OcrService();
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Start the capture process automatically
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _captureImage();
    });
  }

  /// Capture an image and process it
  Future<void> _captureImage() async {
    try {
      setState(() {
        _errorMessage = null;
      });

      // Update the capture source
      ref.read(captureSourceProvider.notifier).state =
          widget.source == ImageSource.camera
              ? CaptureSource.camera
              : CaptureSource.gallery;

      // Capture and process the image
      final ocrText = await _ocrService.captureImage(widget.source, ref);

      if (ocrText == null || ocrText.trim().isEmpty) {
        setState(() {
          _errorMessage = 'No text was detected in the image.';
        });
        return;
      }

      // Process the OCR text to clean it up
      final processedText = _ocrService.processText(ocrText);

      if (mounted) {
        // Show the manual entry dialog with the extracted text
        final result = await showManualEntryDialog(
          context,
          initialText: processedText,
          captureSource: widget.source == ImageSource.camera
              ? 'Camera capture'
              : 'Image extraction',
        );

        if (mounted) {
          if (result != null) {
            // Return the result to the caller
            Navigator.pop(context, result);
          } else {
            // User canceled, close this dialog too
            Navigator.pop(context);
          }
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error processing image: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show processing UI until we can launch the manual entry dialog
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: _buildProcessingSection(),
    );
  }

  Widget _buildProcessingSection() {
    final theme = Theme.of(context);

    if (_errorMessage != null) {
      return Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.source == ImageSource.camera
                      ? 'Camera Capture'
                      : 'Image Capture',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Error content
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppColors.getFailureColor(Theme.of(context).brightness)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color:
                        AppColors.getFailureColor(Theme.of(context).brightness)
                            .withAlpha(76)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline,
                      color: AppColors.getFailureColor(
                          Theme.of(context).brightness),
                      size: 48),
                  const SizedBox(height: 16),
                  Text(
                    'Error Processing Image',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.getFailureColor(
                          Theme.of(context).brightness),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _errorMessage!,
                    style: TextStyle(
                        color: AppColors.getFailureColor(
                            Theme.of(context).brightness)),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _captureImage,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Try Again'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.getFailureColor(
                              Theme.of(context).brightness)
                          .withAlpha(51),
                      foregroundColor: AppColors.getFailureColor(
                          Theme.of(context).brightness),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    // Show loading indicator
    return Container(
      width: 280,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon and loading indicator
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withAlpha(76),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                widget.source == ImageSource.camera
                    ? Icons.camera_alt
                    : Icons.image,
                size: 36,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(height: 32),

          // Loading indicator
          const CircularProgressIndicator(),
          const SizedBox(height: 24),

          // Loading text
          Text(
            'Extracting text from image...',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.getTextColor(Theme.of(context).brightness),
            ),
          ),

          // Cancel button
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(
                  color: AppColors.getTextColor(Theme.of(context).brightness)),
            ),
          ),
        ],
      ),
    );
  }
}

/// Show the image capture dialog
Future<VocabCard?> showImageCaptureDialog(
    BuildContext context, ImageSource source) {
  return showDialog<VocabCard>(
    context: context,
    builder: (context) => ImageCaptureDialog(source: source),
  );
}
