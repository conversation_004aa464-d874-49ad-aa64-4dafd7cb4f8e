import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';

import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/features/vocab_capture/services/vocab_save_service.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/vocab_capture_entry.dart';
import 'package:vocadex/src/features/vocab_capture/utils/allocation_utils.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
// Import for the manual entry dialog
// import 'package:vocadex/src/features/vocab_capture/widgets/manual_entry_dialog.dart';

// Import for the image capture dialog
// import 'package:vocadex/src/features/vocab_capture/widgets/image_capture_dialog.dart';

/// A dialog for selecting how to capture vocabulary (manual, camera, gallery)
class CaptureOptionsDialog extends ConsumerWidget {
  /// Constructor
  const CaptureOptionsDialog({super.key});

  /// Handle manual entry option
  void _handleManualEntry(BuildContext context, WidgetRef ref) async {
    // Close the dialog
    Navigator.pop(context);

    // Show manual entry dialog
    final VocabCard? result = await showManualEntryDialog(context);

    if (result != null && context.mounted) {
      // Save the vocabulary
      await _saveVocabulary(context, result, ref);
    }
  }

  /// Handle image capture option
  void _handleImageCapture(
    BuildContext context,
    WidgetRef ref,
    ImageSource source,
  ) async {
    // Close the dialog
    Navigator.pop(context);

    // Show image capture dialog
    final VocabCard? result = await showImageCaptureDialog(context, source);

    if (result != null && context.mounted) {
      // Save the vocabulary
      await _saveVocabulary(context, result, ref);
    }
  }

  /// Save vocabulary card to database
  Future<void> _saveVocabulary(
      BuildContext context, VocabCard card, WidgetRef ref) async {
    // Check if user can add more cards (for free users)
    final allocationUtils = AllocationUtils();
    final canAddCard = await allocationUtils.canAddCard(ref);

    if (!canAddCard) {
      if (context.mounted) {
        allocationUtils.showUpgradeDialog(context);
      }
      return;
    }

    // Update state to saving
    ref.read(captureStateProvider.notifier).state = CaptureState.saving;

    // Save the vocabulary
    final vocabSaveService = VocabSaveService();
    final result = await vocabSaveService.saveVocabulary(context, card, ref);

    if (result['success'] && context.mounted) {
      // Show success message
      showSuccessToast(
        context,
        title: 'Success',
        description: 'Vocabulary saved successfully!',
      );

      // Check if there are completed goals
      if ((result['completedGoals'] as List).isNotEmpty) {
        // Show achievement dialog or notification
        // This is handled by the saveVocabulary method
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: AppColors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            const Text(
              'Add New Vocabulary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),

            // Option buttons in a row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Manual Entry option
                Expanded(
                  child: _buildOptionButton(
                    context,
                    'Type',
                    Icons.edit,
                    'Manual entry',
                    () => _handleManualEntry(context, ref),
                  ),
                ),

                const SizedBox(width: 24),

                // Camera option
                Expanded(
                  child: _buildOptionButton(
                    context,
                    'Gallery',
                    Icons.camera_alt,
                    'Select Image',
                    () =>
                        _handleImageCapture(context, ref, ImageSource.gallery),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 50),

            // Cancel button
            IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(
                  Icons.highlight_off,
                  color: AppColors.getTextColor(Theme.of(context).brightness),
                  size: 62,
                )),
            // TextButton(
            //   onPressed: () => Navigator.pop(context),
            //   child: const Text('Cancel'),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionButton(
    BuildContext context,
    String label,
    IconData icon,
    String description,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 6),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.light ? AppColors.white : AppColors.backgroundDark,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: Theme.of(context).brightness == Brightness.light ? AppColors.primaryLight : AppColors.primaryDark),
            const SizedBox(height: 6),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).brightness == Brightness.light ? AppColors.textLight : AppColors.textDark,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the capture options dialog
void showCaptureOptions(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => const CaptureOptionsDialog(),
  );
}
