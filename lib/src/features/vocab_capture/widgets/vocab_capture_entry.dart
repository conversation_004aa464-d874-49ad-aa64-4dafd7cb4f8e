import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/capture_options_dialog.dart';

import 'package:image_picker/image_picker.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/bottom_navbar/bottom_navbar.dart';

// Export all the updated components
export 'capture_options_dialog.dart';
export 'manual_entry_dialog.dart';
export 'image_capture_dialog.dart';

// Import the dialog implementations
import 'package:vocadex/src/features/vocab_capture/widgets/manual_entry_dialog.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/image_capture_dialog.dart';

/// Shows vocabulary capture options by opening the AddButton menu
void showVocabEntryOptions(BuildContext context) {
  // Use a ProviderScope and get the ref to update the provider
  final container = ProviderScope.containerOf(context);
  container.read(addButtonExpandedProvider.notifier).state = true;

  // Comment out the old dialog approach
  // showCaptureOptions(context);
}

/// Shows manual entry dialog directly
Future<VocabCard?> showManualVocabEntry(
  BuildContext context, {
  bool isOnboarding = false,
}) {
  return showManualEntryDialog(
    context,
    isOnboarding: isOnboarding,
  );
}

/// Shows camera capture dialog directly
Future<VocabCard?> showCameraCaptureDialog(BuildContext context) {
  return showImageCaptureDialog(context, ImageSource.camera);
}

/// Shows gallery capture dialog directly
Future<VocabCard?> showGalleryCaptureDialog(BuildContext context) {
  return showImageCaptureDialog(context, ImageSource.gallery);
}
