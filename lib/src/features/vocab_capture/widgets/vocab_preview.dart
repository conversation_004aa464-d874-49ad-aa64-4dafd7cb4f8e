import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_capture/services/vocab_save_service.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/capture_success_dialog.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

// Enhancements to the VocabPreviewCarousel class in vocab_preview.dart

class VocabPreviewCarousel extends ConsumerStatefulWidget {
  /// List of vocabulary cards to display
  final List<VocabCard> cards;

  /// Optional captured text to display
  final String? capturedText;

  /// Callback when dialog is closed/canceled
  final VoidCallback onClose;

  /// Callback after successful save
  final Function(VocabCard) onSaveSuccess;

  /// Constructor
  const VocabPreviewCarousel({
    super.key,
    required this.cards,
    this.capturedText,
    required this.onClose,
    required this.onSaveSuccess,
  });

  @override
  ConsumerState<VocabPreviewCarousel> createState() =>
      _VocabPreviewCarouselState();
}

class _VocabPreviewCarouselState extends ConsumerState<VocabPreviewCarousel> {
  /// Selected card index
  int _selectedCardIndex = 0;

  /// Page controller for the carousel
  late PageController _pageController;

  /// Service for saving vocabulary
  final VocabSaveService _saveService = VocabSaveService();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);

    // Make sure the first card is the selected one in the provider
    if (widget.cards.isNotEmpty) {
      ref.read(generatedVocabProvider.notifier).state = widget.cards[0];
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Save the currently selected vocabulary card
  Future<void> _saveVocabulary() async {
    if (widget.cards.isEmpty || _selectedCardIndex >= widget.cards.length) {
      return;
    }

    // Get the selected card
    final selectedCard = widget.cards[_selectedCardIndex];

    // Save the card to the provider to ensure it's available
    ref.read(generatedVocabProvider.notifier).state = selectedCard;

    // Set state to saving
    ref.read(captureStateProvider.notifier).state = CaptureState.saving;

    // Call the save service
    final result =
        await _saveService.saveVocabulary(context, selectedCard, ref);

    if (result['success'] && mounted) {
      // Show success dialog
      showCaptureSuccessDialog(
        context,
        selectedCard.word,
        onDismiss: () {
          // Return the saved card via callback
          widget.onSaveSuccess(selectedCard);
          ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
        },
      );
      ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
    } else if (mounted) {
      // Show error
      showFailureToast(
        context,
        title: 'Error',
        description: result['message'] ?? 'Failed to save vocabulary',
      );
      // Reset state
      ref.read(captureStateProvider.notifier).state = CaptureState.generated;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we should use carousel (only for multiple cards)
    final bool useCarousel = widget.cards.length > 1;
    final theme = Theme.of(context);

    // Get the current card to display
    final VocabCard currentCard = widget.cards.isNotEmpty
        ? widget.cards[_selectedCardIndex]
        : widget.cards.first;

    return Column(
      children: [
        // Content area - either carousel or single card
        Expanded(
          child: useCarousel
              ? _buildCarousel()
              : SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  physics: const BouncingScrollPhysics(),
                  child: VocabularyCard(card: currentCard),
                ),
        ),

        // Carousel indicators if multiple cards
        if (useCarousel)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(widget.cards.length, (index) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _selectedCardIndex == index
                        ? theme.primaryColor
                        : AppColors.grey.withAlpha(76),
                  ),
                );
              }),
            ),
          ),

        // Action buttons - Save and Cancel
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Cancel button
              Expanded(
                flex: 1,
                child: OutlinedButton.icon(
                  onPressed: widget.onClose,
                  icon: const Icon(Icons.close),
                  label: const Text('Cancel'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    side: BorderSide(color: AppColors.grey),
                    minimumSize: const Size(0, 48), // Fixed minimum size
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Save button
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed:
                      ref.watch(captureStateProvider) == CaptureState.saving
                          ? null
                          : _saveVocabulary,
                  icon: const Icon(Icons.save),
                  label: ref.watch(captureStateProvider) == CaptureState.saving
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.getButtonForegroundColor(
                                        Theme.of(context).brightness)),
                              ),
                            ),
                            const SizedBox(width: 8),
                            const Text('Saving'),
                          ],
                        )
                      : Text(useCarousel ? 'Save Selected' : 'Save'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.primaryColor,
                    foregroundColor: AppColors.getButtonForegroundColor(
                        Theme.of(context).brightness),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    minimumSize: const Size(0, 48), // Fixed minimum size
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCarousel() {
    return PageView.builder(
      controller: _pageController,
      itemCount: widget.cards.length,
      onPageChanged: (index) {
        setState(() {
          _selectedCardIndex = index;
        });
        ref.read(generatedVocabProvider.notifier).state = widget.cards[index];
      },
      itemBuilder: (context, index) {
        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
          physics: const BouncingScrollPhysics(),
          child: VocabularyCard(card: widget.cards[index]),
        );
      },
    );
  }
}
