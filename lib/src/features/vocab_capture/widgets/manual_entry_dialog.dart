import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_capture/utils/allocation_utils.dart';

import 'package:vocadex/src/features/vocab_capture/widgets/vocab_preview.dart';
import 'package:vocadex/src/features/onboarding/widgets/onboarding_vocab_preview.dart';
import 'package:vocadex/src/features/onboarding/services/onboarding_allocation_utils.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A dialog for manually entering vocabulary
class ManualEntryDialog extends ConsumerStatefulWidget {
  /// Initial text to populate the text field
  final String? initialText;

  /// Source description (e.g., "Camera capture", "Manual entry")
  final String? captureSource;

  /// Whether this dialog is being shown during onboarding
  final bool isOnboarding;

  /// Constructor
  const ManualEntryDialog({
    super.key,
    this.initialText,
    this.captureSource,
    this.isOnboarding = false,
  });

  @override
  ConsumerState<ManualEntryDialog> createState() => _ManualEntryDialogState();
}

class _ManualEntryDialogState extends ConsumerState<ManualEntryDialog> {
  /// Text controller for the text field
  late final TextEditingController _textController;
  final VocabGenerationService _generationService = VocabGenerationService();
  final FocusNode _focusNode = FocusNode();

  /// Whether we are showing the preview
  bool _showPreview = false;

  /// List of generated vocabulary cards (for multiple meanings)
  List<VocabCard> _generatedCards = [];

  /// Whether the no cards error was shown
  bool _noCardsErrorShown = false;

  @override
  void initState() {
    super.initState();

    _textController = TextEditingController(text: widget.initialText);

    // Schedule state reset after the current build phase is complete
    Future.microtask(() {
      if (mounted) {
        _generationService.fullReset(ref);
      }
    });

    // Set focus on the text field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load user info here instead of in initState
    // This is safe because didChangeDependencies is called after initState
    // and the widget is fully initialized at this point
    _loadUserInfo();
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();

    // We cannot use Future.microtask here because ref will be disposed
    // and we don't need to reset state on disposal since we'll reset on creation

    super.dispose();
  }

  /// Load user information (premium status, remaining cards)
  Future<void> _loadUserInfo() async {
    if (widget.isOnboarding) {
      // Use onboarding allocation utils if in onboarding mode
      await OnboardingAllocationUtils().loadUserInfo(ref);
    } else {
      // Use regular allocation utils otherwise
      await AllocationUtils().loadUserInfo(ref);
    }
  }

  Future<void> _handleSubmit() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      // Show error for empty text
      ref.read(captureErrorProvider.notifier).state =
          'Please enter a word or phrase.';
      return;
    }

    // Check card allocation for free users (but not in onboarding mode)
    final isPremium = ref.watch(subscriptionStateProvider);
    final remainingCards = ref.watch(remainingCardsProvider);

    if (!isPremium && remainingCards <= 0 && !widget.isOnboarding) {
      // Show no cards remaining error directly in the dialog
      setState(() {
        _noCardsErrorShown = true;
      });
      ref.read(captureErrorProvider.notifier).state =
          'No cards remaining today. Come back tomorrow or upgrade to Premium.';
      return;
    }

    // Generate vocabulary
    setState(() {
      _showPreview = false;
      _noCardsErrorShown = false;
      ref.read(captureStateProvider.notifier).state = CaptureState.generating;
    });

    // Store the text in the provider
    ref.read(captureTextProvider.notifier).state = text;

    // Process word definitions - this will handle invalid words and suggestions
    final definitions =
        await _generationService.processWordForDefinitions(text, ref);

    // Check for word suggestions (if we're in needsConfirmation state)
    if (ref.read(captureStateProvider) == CaptureState.needsConfirmation) {
      // The UI will display suggestions in the build method
      return;
    }

    // If we have definitions, proceed with card generation
    if (definitions.isNotEmpty) {
      // Generate vocabulary cards with multiple meanings
      final cards = await _generationService
          .generateVocabularyWithMultipleMeanings(text, ref);

      if (cards.isNotEmpty) {
        setState(() {
          _generatedCards = cards;
          _showPreview = true;
        });

        // Store the first card in the provider
        ref.read(generatedVocabProvider.notifier).state = cards[0];

        // Set state to generated
        ref.read(captureStateProvider.notifier).state = CaptureState.generated;
      }
    }
  }

  /// Handle the selection of a suggested word
  Future<void> _handleSuggestionSelected(String suggestedWord) async {
    // Update the text field with the suggested word
    _textController.text = suggestedWord;

    // Generate a card for the confirmed word
    setState(() {
      // Clear any suggestions first
      ref.read(wordSuggestionsProvider.notifier).state = [];
      ref.read(captureErrorProvider.notifier).state = null;
      ref.read(captureStateProvider.notifier).state = CaptureState.generating;
    });

    final card =
        await _generationService.processSuggestedWord(suggestedWord, ref);

    if (card != null) {
      // Get multiple meanings for the confirmed word
      final cards = await _generationService
          .generateVocabularyWithMultipleMeanings(suggestedWord, ref);

      if (cards.isNotEmpty) {
        setState(() {
          _generatedCards = cards;
          _showPreview = true;
        });

        // Store the first card in the provider
        ref.read(generatedVocabProvider.notifier).state = cards[0];

        // Set state to generated
        ref.read(captureStateProvider.notifier).state = CaptureState.generated;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final captureState = ref.watch(captureStateProvider);
    final errorMessage = ref.watch(captureErrorProvider);
    final isPremium = ref.watch(subscriptionStateProvider);
    final remainingCards = ref.watch(remainingCardsProvider);
    final suggestions = ref.watch(wordSuggestionsProvider);

    return Dialog(
      backgroundColor: _showPreview ? AppColors.transparent : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      clipBehavior: Clip.antiAlias,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: _showPreview
            ? widget.isOnboarding
                ? OnboardingVocabPreview(
                    cards: _generatedCards,
                    capturedText: _textController.text,
                    onClose: () => Navigator.of(context).pop(),
                    onSaveSuccess: (savedCard) {
                      Navigator.pop(context, savedCard);
                    },
                  )
                : VocabPreviewCarousel(
                    cards: _generatedCards,
                    capturedText: _textController.text,
                    onClose: () => Navigator.of(context).pop(),
                    onSaveSuccess: (savedCard) {
                      Navigator.pop(context, savedCard);
                    },
                  )
            : SingleChildScrollView(
                child: _buildEntryForm(isPremium, remainingCards, captureState,
                    errorMessage, suggestions),
              ),
      ),
    );
  }

  Widget _buildEntryForm(
    bool isPremium,
    int remainingCards,
    CaptureState captureState,
    String? errorMessage,
    List<String> suggestions,
  ) {
    final theme = Theme.of(context);
    final isLoading = captureState == CaptureState.generating;
    final needsConfirmation = captureState == CaptureState.needsConfirmation;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Dialog header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.captureSource != null ? 'Edit Word' : 'Add New Word',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 20),
                constraints: const BoxConstraints(),
                padding: EdgeInsets.zero,
                onPressed: () {
                  // Use Future.microtask to avoid modifying state during build
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Centered, underlined text field with larger font
          Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: TextField(
              controller: _textController,
              focusNode: _focusNode,
              textCapitalization: TextCapitalization.sentences,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintText: 'type word',
                hintStyle: TextStyle(
                  color: AppColors.grey.withAlpha(153),
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                ),
                border: const UnderlineInputBorder(
                  borderSide: BorderSide(
                    width: 2,
                  ),
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.grey,
                    width: 2,
                  ),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: theme.colorScheme.primary,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 8),
                isDense: false,
                filled: false,
              ),
              enabled: !isLoading && !needsConfirmation,
              textInputAction: TextInputAction.go,
              onChanged: (value) {
                if (errorMessage != null) {
                  ref.read(captureErrorProvider.notifier).state = null;
                  setState(() {
                    _noCardsErrorShown = false;
                  });
                }
                setState(() {});
              },
              onEditingComplete: _handleSubmit,
            ),
          ),

          // Error message
          if (errorMessage != null &&
              errorMessage.isNotEmpty &&
              !needsConfirmation &&
              !isLoading)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
              decoration: BoxDecoration(
                color: _noCardsErrorShown
                    ? AppColors.getWarningColor(Theme.of(context).brightness)
                        .withAlpha(26)
                    : AppColors.getFailureColor(Theme.of(context).brightness)
                        .withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    _noCardsErrorShown
                        ? Icons.info_outline
                        : Icons.error_outline,
                    color: _noCardsErrorShown
                        ? AppColors.getWarningColor(
                            Theme.of(context).brightness)
                        : AppColors.getFailureColor(
                            Theme.of(context).brightness),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      errorMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: _noCardsErrorShown
                            ? AppColors.getWarningColor(
                                Theme.of(context).brightness)
                            : AppColors.getFailureColor(
                                Theme.of(context).brightness),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Word suggestions UI
          if (needsConfirmation && suggestions.isNotEmpty && !isLoading)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              decoration: BoxDecoration(
                color: AppColors.getInfoColor(Theme.of(context).brightness)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppColors.getInfoColor(
                            Theme.of(context).brightness),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Did you mean one of these?',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.getInfoColor(
                                Theme.of(context).brightness),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Suggestions list
                  ...suggestions.map(
                    (suggestion) => Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: SizedBox(
                        width: double.maxFinite,
                        child: ElevatedButton(
                          onPressed: isLoading
                              ? null
                              : () => _handleSuggestionSelected(suggestion),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.getBackgroundColor(
                                Theme.of(context).brightness),
                            foregroundColor: AppColors.getInfoColor(
                                Theme.of(context).brightness),
                            elevation: 0,
                            side: BorderSide(
                                color: AppColors.getInfoColor(
                                        Theme.of(context).brightness)
                                    .withAlpha(128)),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 10),
                            minimumSize: const Size(0, 36),
                            disabledBackgroundColor:
                                AppColors.getBackgroundColor(
                                    Theme.of(context).brightness),
                            disabledForegroundColor: AppColors.getTextColor(
                                    Theme.of(context).brightness)
                                .withAlpha(128),
                          ),
                          child: Text(suggestion),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // No, try again button
                  TextButton(
                    onPressed: isLoading
                        ? null
                        : () {
                            // Use Future.microtask to safely update state
                            Future.microtask(() {
                              // Only update state if the widget is still mounted
                              if (mounted) {
                                // Clear suggestions and go back to input state
                                ref
                                    .read(wordSuggestionsProvider.notifier)
                                    .state = [];
                                ref.read(captureStateProvider.notifier).state =
                                    CaptureState.initial;
                                ref.read(captureErrorProvider.notifier).state =
                                    null;
                              }
                            });
                          },
                    style: TextButton.styleFrom(
                      foregroundColor:
                          AppColors.getTextColor(Theme.of(context).brightness),
                      disabledForegroundColor: AppColors.grey,
                    ),
                    child: const Text('No, try again'),
                  ),
                ],
              ),
            ),

          // Loading indicator (shown during generation)
          if (isLoading)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 28),
              child: Column(
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(theme.primaryColor),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Generating vocabulary card...',
                    style: TextStyle(
                      color:
                          AppColors.getTextColor(Theme.of(context).brightness),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 24),

          // Premium badge or remaining cards display
          if (!_noCardsErrorShown &&
              !isLoading &&
              !needsConfirmation &&
              !isPremium &&
              remainingCards > 0)
            Text(
              '$remainingCards cards remaining today',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.getTextColor(Theme.of(context).brightness)
                    .withAlpha(153),
              ),
            ),

          const SizedBox(height: 16),

          // Generate button (hidden when showing suggestions)
          if (!needsConfirmation)
            SizedBox(
              width: 220, // Fixed width button for clean look
              child: ElevatedButton(
                onPressed: isLoading || _textController.text.isEmpty
                    ? null
                    : _handleSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: AppColors.getButtonForegroundColor(
                      Theme.of(context).brightness),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  disabledBackgroundColor: AppColors.grey.withAlpha(76),
                  disabledForegroundColor: AppColors.grey.withAlpha(128),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30), // Pill shape
                  ),
                  elevation: 0,
                  minimumSize: const Size(0, 48), // Ensure valid constraints
                ),
                child: isLoading
                    ? null
                    : const Text(
                        'Generate',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),

          // Retry button for image capture
          if (widget.captureSource != null &&
                  widget.captureSource!.contains("camera") ||
              widget.captureSource != null &&
                  widget.captureSource!.contains("image"))
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: TextButton(
                onPressed: () {
                  // Return null to signal retry
                  Navigator.pop(context, null);
                },
                child: const Text('Try Another Image'),
              ),
            ),

          // Add upgrade button if no cards remaining
          if (_noCardsErrorShown)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  // Show paywall
                  ShowPaywall().presentPaywall();
                },
                icon: const Icon(Icons.star, size: 16),
                label: const Text('Upgrade to Premium'),
                style: OutlinedButton.styleFrom(
                  foregroundColor:
                      AppColors.getWarningColor(Theme.of(context).brightness),
                  side: BorderSide(
                      color: AppColors.getWarningColor(
                              Theme.of(context).brightness)
                          .withAlpha(128)),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  minimumSize: const Size(0, 36), // Ensure valid constraints
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Show the manual entry dialog
Future<VocabCard?> showManualEntryDialog(
  BuildContext context, {
  String? initialText,
  String? captureSource,
  bool isOnboarding = false,
}) {
  // We'll reset state in the dialog's initState instead
  return showDialog<VocabCard>(
    context: context,
    barrierDismissible: false,
    builder: (context) => ManualEntryDialog(
      initialText: initialText,
      captureSource: captureSource,
      isOnboarding: isOnboarding,
    ),
  );
}
