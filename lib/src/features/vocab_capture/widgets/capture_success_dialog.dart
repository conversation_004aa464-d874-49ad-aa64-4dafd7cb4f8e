import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A dialog shown after successfully saving a vocabulary card
class CaptureSuccessDialog extends ConsumerWidget {
  /// The word that was saved
  final String word;

  /// Optional callback when the dialog is dismissed
  final VoidCallback? onDismiss;

  /// Constructor
  const CaptureSuccessDialog({
    super.key,
    required this.word,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final completedGoals = ref.watch(completedGoalsProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Success icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.light
                    ? AppColors.successLight.withAlpha(51)
                    : AppColors.successDark.withAlpha(51),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check_circle,
                size: 60,
                color: Theme.of(context).brightness == Brightness.light
                    ? AppColors.successLight.withAlpha(178)
                    : AppColors.successDark.withAlpha(178),
              ),
            ),
            const SizedBox(height: 16),

            // Success message
            Text(
              'Vocabulary Saved!',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).brightness == Brightness.light
                    ? AppColors.successLight.withAlpha(204)
                    : AppColors.successDark.withAlpha(204),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '"$word" has been added to your vocabulary.',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),

            // Weekly goal achievements
            if (completedGoals.isNotEmpty) ...[
              const SizedBox(height: 24),
              Text(
                'Weekly Goals Completed!',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color:
                      AppColors.getWarningColor(Theme.of(context).brightness),
                ),
              ),
              const SizedBox(height: 12),
              ...completedGoals.map((goal) => _buildAchievement(context, goal)),
            ],

            // Action buttons
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // Call onDismiss callback
                    onDismiss?.call();
                  },
                  icon: const Icon(Icons.check),
                  label: const Text('Great!'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // Reset the capture state
                    _resetCaptureState(ref);
                    // Navigate back to add another word
                    onDismiss?.call();
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Another'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: AppColors.getButtonForegroundColor(
                        Theme.of(context).brightness),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build an achievement item
  Widget _buildAchievement(BuildContext context, GoalCompletion goal) {
    final brightness = Theme.of(context).brightness;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: brightness == Brightness.light
            ? AppColors.warningLight.withAlpha(51)
            : AppColors.warningDark.withAlpha(51),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: brightness == Brightness.light
                ? AppColors.warningLight.withAlpha(102)
                : AppColors.warningDark.withAlpha(102)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.emoji_events,
            color: brightness == Brightness.light
                ? AppColors.warningLight.withAlpha(178)
                : AppColors.warningDark.withAlpha(178),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Completed weekly goal for ${goal.type}s',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.warningLight.withAlpha(230),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: AppColors.warningLight.withAlpha(230),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '+${goal.points} pts',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: brightness == Brightness.light
                    ? AppColors.warningLight.withAlpha(230)
                    : AppColors.warningDark.withAlpha(230),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Reset the capture state to initial
  void _resetCaptureState(WidgetRef ref) {
    ref.read(captureStateProvider.notifier).state = CaptureState.initial;
    ref.read(captureErrorProvider.notifier).state = null;
    ref.read(generatedVocabProvider.notifier).state = null;
    ref.read(completedGoalsProvider.notifier).state = [];
  }
}

/// Show a success dialog for vocabulary capture
void showCaptureSuccessDialog(BuildContext context, String word,
    {VoidCallback? onDismiss}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => CaptureSuccessDialog(
      word: word,
      onDismiss: onDismiss,
    ),
  );
}
