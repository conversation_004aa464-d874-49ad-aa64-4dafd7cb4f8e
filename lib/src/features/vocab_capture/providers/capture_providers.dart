import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// The current state of the vocabulary generation process
enum CaptureState {
  /// Initial state before capture begins
  initial,

  /// Currently generating vocabulary data
  generating,

  /// Vocabulary was successfully generated
  generated,

  /// Currently saving vocabulary to database
  saving,

  /// Vocabulary was successfully saved
  saved,

  /// An error occurred during the process
  error,

  /// State for when we need user confirmation for suggested words
  needsConfirmation,
}

/// Current capture source
enum CaptureSource {
  /// Manual entry (typing)
  manual,

  /// Camera capture
  camera,

  /// Gallery image
  gallery,
}

/// Provider for the current capture state
final captureStateProvider =
    StateProvider<CaptureState>((ref) => CaptureState.initial);

/// Provider for the current capture source
final captureSourceProvider =
    StateProvider<CaptureSource>((ref) => CaptureSource.manual);

/// Provider for the generated vocabulary card
final generatedVocabProvider = StateProvider<VocabCard?>((ref) => null);

/// Provider for the error message (if any)
final captureErrorProvider = StateProvider<String?>((ref) => null);

/// Provider for the text content being captured
final captureTextProvider = StateProvider<String>((ref) => '');

/// Provider for keeping track of card allocation
final remainingCardsProvider = StateProvider<int>((ref) => 5);

/// Provider for tracking premium status

/// Provider for tracking if card is flipped (in preview)
final isCardFlippedProvider = StateProvider<bool>((ref) => false);

/// Record of a goal completion (for displaying achievements)
class GoalCompletion {
  final String type;
  final int points;

  GoalCompletion({required this.type, required this.points});
}

/// Provider for completed goals (for achievements)
final completedGoalsProvider = StateProvider<List<GoalCompletion>>((ref) => []);
