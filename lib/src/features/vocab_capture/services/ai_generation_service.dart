import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_capture/services/audio_generation_service.dart';
import 'dart:convert';
import 'package:firebase_vertexai/firebase_vertexai.dart';

// Add provider for word suggestions
final wordSuggestionsProvider = StateProvider<List<String>>((ref) => []);
final originalWordProvider = StateProvider<String>((ref) => '');

class VocabGenerationService {
  final FirebaseVertexAI _vertexAI = FirebaseVertexAI.instance;
  final AudioGenerationService _audioService = AudioGenerationService();

  // Clean the AI response to extract only the JSON part
  String _cleanJsonResponse(String response) {
    final startIndex = response.indexOf('{');
    final endIndex = response.lastIndexOf('}') + 1;
    if (startIndex == -1 || endIndex == -1) {
      throw const FormatException(
          'Invalid response format: No JSON object found');
    }
    return response.substring(startIndex, endIndex);
  }

  // Check if a word exists and get similar suggestions
  Future<Map<String, dynamic>> checkWordAndGetSuggestions(String word) async {
    try {
      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Analyze the word "$word" and determine if it's a valid English word. If not, find similar valid English words.

Instructions:
1. First, check if the input is a valid English word
2. If it's not valid, analyze it for:
   - Common misspellings (e.g., "recieve" → "receive")
   - Missing letters (e.g., "elphant" → "elephant")
   - Extra letters (e.g., "beautifull" → "beautiful")
   - Swapped letters (e.g., "freind" → "friend")
   - Phonetic similarity (e.g., "nife" → "knife")

Return a JSON object in this format:
For invalid words WITH suggestions:
{
  "isValid": false,
  "suggestions": ["suggestion1", "suggestion2", "suggestion3"],
  "message": "Did you mean one of these?"
}

For valid words:
{
  "isValid": true,
  "suggestions": [],
  "message": ""
}

For invalid words with NO possible suggestions:
{
  "isValid": false,
  "suggestions": [],
  "message": "This word does not exist in English. Please check your spelling."
}

Example responses:
For "beutiful":
{
  "isValid": false,
  "suggestions": ["beautiful"],
  "message": "Did you mean one of these?"
}

For "pigon":
{
  "isValid": false,
  "suggestions": ["pigeon"],
  "message": "Did you mean one of these?"
}

For "elefant":
{
  "isValid": false,
  "suggestions": ["elephant"],
  "message": "Did you mean one of these?"
}

For "cat":
{
  "isValid": true,
  "suggestions": [],
  "message": ""
}

For "xyzabc":
{
  "isValid": false,
  "suggestions": [],
  "message": "This word does not exist in English. Please check your spelling."
}

Important:
- Always suggest corrections for common misspellings
- Only suggest real, common English words
- Maximum 3 suggestions, ordered by relevance
- Don't suggest anything for random strings or non-English text

Provide only the JSON response, no additional text.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      String cleanedResponse = _cleanJsonResponse(buffer.toString());
      Map<String, dynamic> jsonResponse = jsonDecode(cleanedResponse);

      // Ensure we have the required fields
      if (!jsonResponse.containsKey('isValid') ||
          !jsonResponse.containsKey('suggestions') ||
          !jsonResponse.containsKey('message')) {
        return {
          "isValid": false,
          "suggestions": [],
          "message": "Error checking word validity"
        };
      }

      return jsonResponse;
    } catch (e) {
      debugPrint('Error in checkWordAndGetSuggestions: $e');
      return {
        "isValid": false,
        "suggestions": [],
        "message": "Error checking word validity"
      };
    }
  }

  // Generate VocabCard using Vertex AI
  Future<VocabCard?> generateVocabCard(String word,
      {bool isConfirmedWord = false}) async {
    if (!isConfirmedWord) {
      // First check if the word is valid
      final wordCheck = await checkWordAndGetSuggestions(word);
      if (!wordCheck['isValid']) {
        throw FormatException(wordCheck['message'] ?? 'Not a valid word');
      }
    }

    try {
      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Create a JSON object for the word "$word" with the following structure:
{
  "definition": "<clear and concise definition at B2 level maximum>",
  "examples": ["<natural example sentence>"],
  "pronunciation": "<phonetic pronunciation>",
  "type": ["<part of speech>"],
  "level": "<CEFR Level from A1, A2, B1, B2, C1, or C2>",
  "frequency": <word frequency as an integer from 1 (extremely rare) to 5 (extremely common)>
}

Important guidelines:
1. Use simple, everyday language (B2 level maximum)
2. Avoid academic or overly formal language - don't use "one" as a pronoun
3. No adult content (18+) or inappropriate content
4. If it's a verb form (like "ate"), include the base form (e.g., "past tense of eat")
5. ONLY process valid English words - if not valid, respond with invalid word format
6. Do NOT try to guess or correct misspelled words
7. Use natural, conversational language for examples
8. Frequency must be an integer from 1 (extremely rare) to 5 (extremely common)

If the input is not a valid word or phrase in English, or is inappropriate, respond with:
{
  "definition": "not a valid word",
  "examples": [],
  "pronunciation": "",
  "type": [],
  "level": "",
  "frequency": 1
}
Provide only the JSON object, no additional text or formatting.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      // Clean and parse the response
      String cleanedResponse = _cleanJsonResponse(buffer.toString());
      Map<String, dynamic> jsonResponse;
      try {
        jsonResponse = jsonDecode(cleanedResponse);
      } catch (e) {
        throw FormatException(
            'Invalid JSON format in response: $cleanedResponse');
      }

      // Check if the response indicates an invalid word
      if (jsonResponse['definition'] == 'not a valid word' ||
          (jsonResponse['examples'] as List).isEmpty ||
          jsonResponse['pronunciation'].isEmpty ||
          (jsonResponse['type'] as List).isEmpty ||
          jsonResponse['level'].isEmpty) {
        // Return null instead of throwing an exception
        return null;
      }

      // Create VocabCard with validated data
      return VocabCard(
        id: '', // ID to be set when saving to Firebase
        word: word,
        definition: jsonResponse['definition'] as String,
        examples: List<String>.from(jsonResponse['examples']),
        type: List<String>.from(jsonResponse['type']),
        pronunciation: jsonResponse['pronunciation'] as String,
        level: jsonResponse['level'] as String,
        frequency: jsonResponse['frequency'] is int
            ? jsonResponse['frequency'] as int
            : 1,
        color: 'Red', // Default color for new cards
        masteryLevel: 1, // Default mastery level
      );
    } catch (e) {
      throw Exception('Failed to generate vocabulary card: $e');
    }
  }

  // Generate VocabCard with multiple meanings
  Future<List<VocabCard>> generateVocabularyWithMultipleMeanings(
      String word, WidgetRef ref) async {
    try {
      // Update state to generating
      ref.read(captureStateProvider.notifier).state = CaptureState.generating;

      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Create a JSON object for the word "$word" with potential multiple meanings.

Multiword (polysemy) rules:
1. ONLY mark as multiword if the meanings are COMPLETELY DIFFERENT in their core concept, not just different usages/contexts.
2. Examples of TRUE multiword cases:
   - "bank" (financial institution vs. river edge)
   - "counter" (counting device vs. shop surface)
   - "bat" (animal vs. sports equipment)
3. Examples that are NOT multiword:
   - "beautiful" (flower vs. soul) - same concept of attractiveness
   - "retrieving" (from memory vs. from storage) - same concept of getting back
   - "running" (sport vs. managing) - extensions of same movement concept

Parts of speech rules:
- ONLY use these 8 basic parts of speech: noun, pronoun, verb, adjective, adverb, preposition, conjunction, interjection
- DO NOT use subcategories or detailed forms

JSON format:
{
  "primaryDefinition": {
    "definition": "<clear and concise primary definition at B2 level maximum>",
    "examples": ["<natural example sentence>"],
    "pronunciation": "<phonetic pronunciation>",
    "type": ["<one of the 8 basic parts of speech>"],
    "level": "<CEFR Level from A1, A2, B1, B2, C1, or C2>",
    "frequency": <integer from 1-5>
  },
  "shouldShowAsMultiword": true/false,
  "alternativeDefinitions": [
    {
      "definition": "<completely different meaning>",
      "examples": ["<example for different meaning>"],
      "type": ["<one of the 8 basic parts of speech>"]
    }
  ]
}

IMPORTANT:
1. Use simple, everyday language (B2 level maximum)
2. Don't use "one" as a pronoun in definitions
3. Only mark as multiword if meanings are truly distinct concepts
4. Keep parts of speech basic and simple
5. No adult content (18+) or inappropriate content

If not a valid English word/phrase, respond with:
{
  "primaryDefinition": {
    "definition": "not a valid word",
    "examples": [],
    "pronunciation": "",
    "type": [],
    "level": "",
    "frequency": 1
  },
  "shouldShowAsMultiword": false,
  "alternativeDefinitions": []
}
Provide only the JSON object, no additional text or formatting.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      // Clean and parse the response
      String cleanedResponse = _cleanJsonResponse(buffer.toString());
      Map<String, dynamic> jsonResponse;
      try {
        jsonResponse = jsonDecode(cleanedResponse);
      } catch (e) {
        throw FormatException(
            'Invalid JSON format in response: $cleanedResponse');
      }

      final primaryDef =
          jsonResponse['primaryDefinition'] as Map<String, dynamic>;

      // Check if the response indicates an invalid word
      if (primaryDef['definition'] == 'not a valid word' ||
          (primaryDef['examples'] as List).isEmpty ||
          primaryDef['pronunciation'].isEmpty ||
          (primaryDef['type'] as List).isEmpty ||
          primaryDef['level'].isEmpty) {
        ref.read(captureErrorProvider.notifier).state =
            'Not a valid word or phrase';
        ref.read(captureStateProvider.notifier).state = CaptureState.error;
        return [];
      }

      // For testing purposes, force multiword to be true for common words with multiple meanings
      // In production, you would remove this and rely on the AI's determination
      bool isMultiWord = jsonResponse['shouldShowAsMultiword'] == true;
      final List<String> knownMultiMeaningWords = [
        'bear',
        'bank',
        'bat',
        'book',
        'spring',
        'run',
        'light',
        'match',
        'pen',
        'rock'
      ];

      if (knownMultiMeaningWords.contains(word.toLowerCase())) {
        isMultiWord = true;
      }

      // Get alternative definitions
      final List<Map<String, dynamic>> altDefs = [];
      if (jsonResponse['alternativeDefinitions'] != null &&
          (isMultiWord ||
              knownMultiMeaningWords.contains(word.toLowerCase()))) {
        final altDefsList = jsonResponse['alternativeDefinitions'] as List;
        for (var def in altDefsList) {
          if (def is Map<String, dynamic>) {
            altDefs.add(def);
          }
        }
      }

      // Get primary examples
      List<String> primaryExamples = List<String>.from(primaryDef['examples']);

      // Create primary VocabCard
      final VocabCard primaryCard = VocabCard(
        id: '', // ID to be set when saving to Firebase
        word: word,
        definition: primaryDef['definition'] as String,
        examples: primaryExamples,
        type: List<String>.from(primaryDef['type']),
        pronunciation: primaryDef['pronunciation'] as String,
        level: primaryDef['level'] as String,
        frequency:
            primaryDef['frequency'] is int ? primaryDef['frequency'] as int : 1,
        color: 'Red', // Default color for new cards
        masteryLevel: 1, // Default mastery level
        multipleDefinitions: isMultiWord ? altDefs : null,
        hasMultipleMeanings: isMultiWord,
      );

      // If there are alternative definitions, create a list of cards
      List<VocabCard> allCards = [primaryCard];

      if ((isMultiWord ||
              knownMultiMeaningWords.contains(word.toLowerCase())) &&
          altDefs.isNotEmpty) {
        for (var i = 0; i < altDefs.length; i++) {
          final altDef = altDefs[i];

          // Only add valid alternative definitions
          if (altDef['definition'] != null && altDef['definition'].isNotEmpty) {
            // Get examples
            List<String> altExamples =
                List<String>.from(altDef['examples'] ?? []);
            if (altExamples.isEmpty) {
              // Add placeholder example if none exist
              altExamples = ["Example for ${word} not available."];
            }

            final alternativeCard = VocabCard(
              id: '', // ID to be set when saving to Firebase
              word: word,
              definition: altDef['definition'] as String,
              examples: altExamples,
              type:
                  List<String>.from(altDef['type'] ?? [primaryCard.type.first]),
              pronunciation: primaryCard.pronunciation,
              level: primaryCard.level,
              frequency: primaryCard.frequency,
              color: 'Red', // Default color for new cards
              masteryLevel: 1, // Default mastery level
              hasMultipleMeanings: true,
            );

            allCards.add(alternativeCard);
          }
        }
      }

      // If we know it should have multiple meanings but AI didn't provide them
      if (knownMultiMeaningWords.contains(word.toLowerCase()) &&
          allCards.length < 2) {
        // Create a hardcoded alternate definition for testing
        final Map<String, dynamic> fallbackDef =
            generateFallbackDefinition(word);

        if (fallbackDef.isNotEmpty) {
          final alternativeCard = VocabCard(
            id: '',
            word: word,
            definition: fallbackDef['definition'] as String,
            examples: List<String>.from(fallbackDef['examples']),
            type: List<String>.from(fallbackDef['type']),
            pronunciation: primaryCard.pronunciation,
            level: primaryCard.level,
            frequency: primaryCard.frequency,
            color: 'Red',
            masteryLevel: 1,
            hasMultipleMeanings: true,
          );

          allCards.add(alternativeCard);

          // Also update the primary card to indicate multiple meanings
          allCards[0] = allCards[0].copyWith(
            hasMultipleMeanings: true,
            multipleDefinitions: [fallbackDef],
          );
        }
      }

      // Update state to generated
      ref.read(captureStateProvider.notifier).state = CaptureState.generated;
      return allCards;
    } catch (e) {
      // Handle errors
      debugPrint('Error generating vocabulary: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error generating vocabulary: $e';
      ref.read(captureStateProvider.notifier).state = CaptureState.error;
      return [];
    }
  }

// Helper method to generate fallback definitions for known words
  Map<String, dynamic> generateFallbackDefinition(String word) {
    final lowerWord = word.toLowerCase();

    switch (lowerWord) {
      case 'bear':
        return {
          'definition':
              'To carry or support weight, pressure, or responsibility',
          'examples': [
            'The bridge cannot bear such heavy loads',
            'She couldn\'t bear the thought of leaving him'
          ],
          'type': ['verb'],
        };
      case 'bank':
        return {
          'definition': 'The land alongside or sloping down to a river or lake',
          'examples': [
            'He sat on the river bank watching the boats',
            'They walked along the grassy bank'
          ],
          'type': ['noun'],
        };
      case 'bat':
        return {
          'definition':
              'A specially shaped piece of wood used for hitting the ball in various games',
          'examples': [
            'He swung the bat and hit a home run',
            'Cricket bats are flat on one side'
          ],
          'type': ['noun'],
        };
      case 'book':
        return {
          'definition':
              'To arrange for someone to have a seat on a plane or for a room in a hotel',
          'examples': [
            'We booked our tickets last week',
            'Have you booked a table at the restaurant?'
          ],
          'type': ['verb'],
        };
      case 'spring':
        return {
          'definition':
              'A coiled metal wire that returns to its original shape after being compressed or stretched',
          'examples': [
            'The springs in the mattress were uncomfortable',
            'The toy works by using a spring mechanism'
          ],
          'type': ['noun'],
        };
      default:
        return {};
    }
  }

  // Get multiple definitions for a word to let the user choose
  Future<List<Map<String, dynamic>>> getWordDefinitions(String word) async {
    try {
      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Find the different meanings of the word "$word" and create a JSON array with the following structure:
[
  {
    "definition": "<simple, easy-to-understand definition at B2 level max>",
    "type": "<one of the 8 basic parts of speech: noun, pronoun, verb, adjective, adverb, preposition, conjunction, interjection>",
    "example": "<simple, natural example sentence>"
  }
]

Important guidelines:
1. Use simple, everyday language (B2 level maximum)
2. Avoid academic or overly formal language
3. No adult content (18+) or inappropriate meanings
4. Don't include slang or offensive meanings
5. If it's a verb form (like "ate"), include the base form (e.g., "past tense of eat")
6. Only include English words - if it's in another language, return empty array []
7. ONLY use the 8 basic parts of speech - no subcategories or detailed forms
8. Only include truly different meanings, not just different contexts of the same meaning

Example response for "bank":
[
  {
    "definition": "a financial institution that people or businesses can save money in or borrow from",
    "type": "noun",
    "example": "I need to go to the bank to deposit this check."
  },
  {
    "definition": "the land along the edge of a river or lake",
    "type": "noun",
    "example": "We had a picnic on the river bank."
  }
]

If the input is not a valid word in English or is inappropriate, return an empty array: []
Provide only the JSON array, no additional text or formatting.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      // Clean and parse the response
      String cleanedResponse = buffer.toString();
      // Extract JSON array
      final startIndex = cleanedResponse.indexOf('[');
      final endIndex = cleanedResponse.lastIndexOf(']') + 1;
      if (startIndex == -1 || endIndex == -1) {
        return [];
      }
      cleanedResponse = cleanedResponse.substring(startIndex, endIndex);

      try {
        // Check if the response is an empty array
        if (cleanedResponse.trim() == '[]') {
          return [];
        }

        // Parse the JSON array
        List<dynamic> jsonResponse = jsonDecode(cleanedResponse);
        return jsonResponse
            .map((item) => item as Map<String, dynamic>)
            .toList();
      } catch (e) {
        debugPrint('Error parsing JSON: $e');
        return [];
      }
    } catch (e) {
      debugPrint('Error getting word definitions: $e');
      return [];
    }
  }

  // Generate VocabCard using the selected definition
  Future<VocabCard?> generateVocabCardFromDefinition(
      String word, Map<String, dynamic> selectedDefinition) async {
    try {
      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Create a detailed JSON object for the word "$word" with the following definition: "${selectedDefinition['definition']}" and part of speech: "${selectedDefinition['type']}".

Output JSON format:
{
  "definition": "${selectedDefinition['definition']}",
  "examples": ["${selectedDefinition['example']}"],
  "pronunciation": "<simple phonetic pronunciation>",
  "type": ["${selectedDefinition['type']}"],
  "level": "<CEFR Level from A1, A2, B1, B2, C1, or C2>",
  "frequency": "<frequency: common, uncommon, or rare>"
}

Important guidelines:
1. Use the exact definition and type provided
2. Keep language simple (B2 level maximum)
3. Provide a realistic frequency rating (common, uncommon, or rare)
4. For verbs in different forms, clarify the base form in the definition
5. Ensure pronunciation is simple and helpful
6. Don't use "one" as a pronoun in definitions
7. Use natural, conversational language for examples

Provide only the JSON object, no additional text or formatting.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      // Clean and parse the response
      String cleanedResponse = _cleanJsonResponse(buffer.toString());
      Map<String, dynamic> jsonResponse;
      try {
        jsonResponse = jsonDecode(cleanedResponse);
      } catch (e) {
        throw FormatException(
            'Invalid JSON format in response: $cleanedResponse');
      }

      // Check if we got valid data
      if (jsonResponse['definition'].toString().isEmpty ||
          (jsonResponse['examples'] as List).isEmpty ||
          jsonResponse['pronunciation'].toString().isEmpty ||
          (jsonResponse['type'] as List).isEmpty ||
          jsonResponse['level'].toString().isEmpty) {
        return null;
      }

      // Create VocabCard with validated data
      return VocabCard(
        id: '', // ID to be set when saving to Firebase
        word: word,
        definition: jsonResponse['definition'] as String,
        examples: List<String>.from(jsonResponse['examples']),
        type: List<String>.from(jsonResponse['type']),
        pronunciation: jsonResponse['pronunciation'] as String,
        level: jsonResponse['level'] as String,
        frequency: jsonResponse['frequency'] is int
            ? jsonResponse['frequency'] as int
            : 1,
        color: 'Red', // Default color for new cards
        masteryLevel: 1, // Default mastery level
      );
    } catch (e) {
      debugPrint('Failed to generate vocabulary card: $e');
      return null;
    }
  }

  // Process word for definitions selection
  Future<List<Map<String, dynamic>>> processWordForDefinitions(
      String word, WidgetRef ref) async {
    try {
      ref.read(captureStateProvider.notifier).state = CaptureState.generating;
      ref.read(captureTextProvider.notifier).state = word.trim();
      ref.read(originalWordProvider.notifier).state = word.trim();

      // First check if the word is valid
      final wordCheck = await checkWordAndGetSuggestions(word.trim());

      if (!wordCheck['isValid']) {
        List<String> suggestions =
            List<String>.from(wordCheck['suggestions'] ?? []);

        if (suggestions.isNotEmpty) {
          // Store suggestions and show them to user
          ref.read(wordSuggestionsProvider.notifier).state = suggestions;
          ref.read(captureErrorProvider.notifier).state =
              '${wordCheck['message']}\n${suggestions.join(", ")}';
          ref.read(captureStateProvider.notifier).state =
              CaptureState.needsConfirmation;
        } else {
          ref.read(wordSuggestionsProvider.notifier).state = [];
          ref.read(captureErrorProvider.notifier).state =
              'This word does not exist in English. Please check your spelling.';
          ref.read(captureStateProvider.notifier).state = CaptureState.error;
        }
        return [];
      }

      // Clear any existing suggestions since word is valid
      ref.read(wordSuggestionsProvider.notifier).state = [];

      // Get multiple definitions to let user choose
      final definitions = await getWordDefinitions(word.trim());

      if (definitions.isEmpty) {
        ref.read(captureErrorProvider.notifier).state =
            'Could not find valid definitions for this word or phrase';
        ref.read(captureStateProvider.notifier).state = CaptureState.error;
      } else {
        ref.read(captureStateProvider.notifier).state = CaptureState.generated;
      }

      return definitions;
    } catch (e) {
      debugPrint('Error processing word: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error processing word: $e';
      ref.read(captureStateProvider.notifier).state = CaptureState.error;
      return [];
    }
  }

  /// Process OCR text for vocabulary generation
  Future<VocabCard?> processOcrText(String ocrText, WidgetRef ref) async {
    // Clean up OCR text
    final processedText = _cleanOcrText(ocrText);

    // Update the text provider
    ref.read(captureTextProvider.notifier).state = processedText;
    ref.read(originalWordProvider.notifier).state = processedText;

    // First check if the word is valid
    final wordCheck = await checkWordAndGetSuggestions(processedText);

    if (!wordCheck['isValid']) {
      List<String> suggestions =
          List<String>.from(wordCheck['suggestions'] ?? []);

      if (suggestions.isNotEmpty) {
        // Store suggestions and show them to user
        ref.read(wordSuggestionsProvider.notifier).state = suggestions;
        ref.read(captureErrorProvider.notifier).state =
            '${wordCheck['message']}\n${suggestions.join(", ")}';
        ref.read(captureStateProvider.notifier).state =
            CaptureState.needsConfirmation;
      } else {
        ref.read(wordSuggestionsProvider.notifier).state = [];
        ref.read(captureErrorProvider.notifier).state =
            'This word does not exist in English. Please check your spelling.';
        ref.read(captureStateProvider.notifier).state = CaptureState.error;
      }
      return null;
    }

    // Clear any existing suggestions since word is valid
    ref.read(wordSuggestionsProvider.notifier).state = [];

    // Generate vocabulary
    return await generateVocabCard(processedText, isConfirmedWord: true);
  }

  /// Clean up OCR text (remove extra whitespace, line breaks, etc.)
  String _cleanOcrText(String text) {
    // Remove extra whitespace and line breaks
    String cleaned =
        text.trim().replaceAll(RegExp(r'\s+'), ' ').replaceAll('\n', ' ');

    // If text is too long, truncate to likely just be a single word/phrase
    if (cleaned.length > 50) {
      // Try to find a good breakpoint
      int breakPoint = cleaned.indexOf(' ', 20);
      if (breakPoint > 0 && breakPoint < 50) {
        cleaned = cleaned.substring(0, breakPoint);
      } else {
        // Default to first 50 chars
        cleaned = cleaned.substring(0, 50);
      }
    }

    return cleaned;
  }

  /// Reset the generation state completely (safely outside build cycle)
  void fullReset(WidgetRef ref) {
    // Use microtask to safely update state outside of build cycle
    Future.microtask(() {
      try {
        // These operations might fail if ref is no longer valid
        ref.read(captureStateProvider.notifier).state = CaptureState.initial;
        ref.read(captureErrorProvider.notifier).state = null;
        ref.read(generatedVocabProvider.notifier).state = null;
        ref.read(isCardFlippedProvider.notifier).state = false;
        ref.read(wordSuggestionsProvider.notifier).state = [];
        ref.read(originalWordProvider.notifier).state = '';
      } catch (e) {
        // If ref is already disposed, just ignore the error
        debugPrint('Error resetting state: $e');
      }
    });
  }

  /// Reset the generation state
  void resetState(WidgetRef ref) {
    // Use microtask to safely update state outside of build cycle
    Future.microtask(() {
      try {
        // These operations might fail if ref is no longer valid
        ref.read(captureStateProvider.notifier).state = CaptureState.initial;
        ref.read(captureErrorProvider.notifier).state = null;
        ref.read(generatedVocabProvider.notifier).state = null;
        ref.read(isCardFlippedProvider.notifier).state = false;
      } catch (e) {
        // If ref is already disposed, just ignore the error
        debugPrint('Error resetting state: $e');
      }
    });
  }

  // Process user's confirmation of a suggested word
  Future<VocabCard?> processSuggestedWord(
      String suggestedWord, WidgetRef ref) async {
    try {
      // Clear suggestions and errors immediately
      ref.read(captureStateProvider.notifier).state = CaptureState.generating;
      ref.read(wordSuggestionsProvider.notifier).state = [];
      ref.read(captureErrorProvider.notifier).state = null;

      // Update the text in the provider
      ref.read(captureTextProvider.notifier).state = suggestedWord;

      // Generate vocabulary for the confirmed word - skip validity check
      final card =
          await generateVocabCard(suggestedWord, isConfirmedWord: true);

      if (card != null) {
        ref.read(captureStateProvider.notifier).state = CaptureState.generated;
        return card;
      } else {
        ref.read(captureStateProvider.notifier).state = CaptureState.error;
        ref.read(captureErrorProvider.notifier).state =
            'Could not generate card for the suggested word';
        return null;
      }
    } catch (e) {
      debugPrint('Error processing suggested word: $e');
      ref.read(captureStateProvider.notifier).state = CaptureState.error;
      ref.read(captureErrorProvider.notifier).state =
          'Error processing suggested word: $e';
      return null;
    }
  }
}

/// Provider for the vocabulary generation service
final vocabGenerationServiceProvider = Provider<VocabGenerationService>((ref) {
  return VocabGenerationService();
});
