import 'dart:io';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// Service to generate and store pronunciation audio for vocabulary words
class AudioGenerationService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  
  /// Generate and store pronunciation audio for a word
  /// Returns the download URL of the stored audio file
  Future<String?> generateAndStoreAudio(String word) async {
    try {
      // First check if audio already exists in Firebase Storage
      final sanitizedWord = _sanitizeWord(word);
      final storageRef = _storage.ref().child('pronunciations/$sanitizedWord.mp3');
      
      try {
        // Try to get the download URL
        final downloadUrl = await storageRef.getDownloadURL();
        debugPrint('Audio already exists for word: $word');
        return downloadUrl;
      } catch (e) {
        // Audio doesn't exist, generate and upload it
        debugPrint('Generating new audio for word: $word');
        
        // Get audio data using fallback method
        final audioData = await _getTTSAudioFallback(word);
        if (audioData == null) return null;
        
        // Create a temporary file to store the audio
        final tempFile = await _saveTempAudioFile(audioData);
        if (tempFile == null) return null;
        
        // Upload to Firebase Storage
        return await _uploadAudioFile(tempFile, sanitizedWord);
      }
    } catch (e) {
      debugPrint('Error in generateAndStoreAudio: $e');
      return null;
    }
  }
  
  /// Sanitize word for use in storage paths
  String _sanitizeWord(String word) {
    return word.toLowerCase().trim().replaceAll(RegExp(r'[^a-z0-9]'), '_');
  }
  
  /// Get TTS audio using a fallback method
  Future<Uint8List?> _getTTSAudioFallback(String word) async {
    try {
      // Use Google Translate TTS API as a fallback
      final url = Uri.parse(
        'https://translate.google.com/translate_tts?ie=UTF-8&q=${Uri.encodeComponent(word)}&tl=en-GB&client=tw-ob'
      );
      
      final response = await http.get(url, headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      });
      
      if (response.statusCode == 200) {
        return response.bodyBytes;
      }
      return null;
    } catch (e) {
      debugPrint('Error in TTS fallback: $e');
      return null;
    }
  }
  
  /// Save audio data to a temporary file
  Future<File?> _saveTempAudioFile(Uint8List audioData) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final uuid = const Uuid().v4();
      final tempFile = File('${tempDir.path}/$uuid.mp3');
      
      await tempFile.writeAsBytes(audioData);
      return tempFile;
    } catch (e) {
      debugPrint('Error saving temp audio file: $e');
      return null;
    }
  }
  
  /// Upload audio file to Firebase Storage
  Future<String?> _uploadAudioFile(File file, String sanitizedWord) async {
    try {
      final storageRef = _storage.ref().child('pronunciations/$sanitizedWord.mp3');
      
      // Upload the file
      await storageRef.putFile(file);
      
      // Get the download URL
      final downloadUrl = await storageRef.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading audio file: $e');
      return null;
    }
  }
}

/// Provider for the audio generation service
final audioGenerationServiceProvider = Provider<AudioGenerationService>((ref) {
  return AudioGenerationService();
});
