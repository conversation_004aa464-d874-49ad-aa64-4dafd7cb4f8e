import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';
import 'package:vocadex/src/features/vocab_quiz/services/quiz_content_generation_service.dart';
import 'package:vocadex/src/features/vocab_quiz/services/quiz_content_storage_service.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/services/global_vocabulary_service.dart';

/// Result of centralized vocabulary creation
class VocabularyCreationResult {
  final bool success;
  final String message;
  final List<VocabCard> availableMeanings;
  final bool foundInGlobal;
  final bool isPremium;
  final int? remainingCards;

  VocabularyCreationResult({
    required this.success,
    required this.message,
    this.availableMeanings = const [],
    this.foundInGlobal = false,
    required this.isPremium,
    this.remainingCards,
  });
}

/// Centralized service that orchestrates vocabulary creation workflow
/// Coordinates between global repository, AI generation, and user collections
class CentralizedVocabularyService {
  final GlobalVocabularyService _globalService = GlobalVocabularyService();
  final VocabGenerationService _aiService = VocabGenerationService();
  final FirebaseService _firebaseService = FirebaseService();
  final QuizContentGenerationService _quizGenerationService =
      QuizContentGenerationService();
  final QuizContentStorageService _quizStorageService =
      QuizContentStorageService();

  /// Main method to create vocabulary with centralized workflow
  /// 1. Search global repository first
  /// 2. If found, return available meanings
  /// 3. If not found, generate with AI and save to global repository
  /// 4. Return available meanings for user selection
  Future<VocabularyCreationResult> createVocabulary(
    String word, {
    bool isPremium = false,
    WidgetRef? ref,
  }) async {
    try {
      debugPrint('Starting centralized vocabulary creation for: $word');

      // Step 1: Search global repository
      final existingCard = await _globalService.searchGlobalVocabulary(word);

      if (existingCard != null) {
        debugPrint('Word found in global repository: $word');

        // Get all meanings for this word
        final meanings = await _globalService.getWordMeanings(word);

        return VocabularyCreationResult(
          success: true,
          message: 'Word found in global repository',
          availableMeanings: meanings,
          foundInGlobal: true,
          isPremium: isPremium,
        );
      }

      debugPrint(
          'Word not found in global repository, generating with AI: $word');

      // Step 2: Generate with AI since not found in global repository
      final generatedCards = await _generateVocabularyWithAI(word, ref);

      if (generatedCards.isEmpty) {
        return VocabularyCreationResult(
          success: false,
          message: 'Failed to generate vocabulary card',
          isPremium: isPremium,
        );
      }

      // Step 3: Save all generated meanings to global repository
      debugPrint(
          'Saving ${generatedCards.length} meanings to global repository');
      await _saveToGlobalRepository(generatedCards);

      return VocabularyCreationResult(
        success: true,
        message: 'New vocabulary generated and saved to global repository',
        availableMeanings: generatedCards,
        foundInGlobal: false,
        isPremium: isPremium,
      );
    } catch (e) {
      debugPrint('Error in centralized vocabulary creation: $e');
      return VocabularyCreationResult(
        success: false,
        message: 'Error creating vocabulary: $e',
        isPremium: isPremium,
      );
    }
  }

  /// Save selected meanings to user's personal collection
  Future<Map<String, dynamic>> saveSelectedMeaningsToUser(
    List<VocabCard> selectedMeanings,
    BuildContext context,
    WidgetRef ref,
  ) async {
    try {
      debugPrint(
          'Saving ${selectedMeanings.length} selected meanings to user collection');

      final results = <String, dynamic>{
        'success': true,
        'savedCards': <VocabCard>[],
        'errors': <String>[],
      };

      for (final meaning in selectedMeanings) {
        try {
          // Convert to user card and save
          final userCard = meaning.toUserCard();
          await _firebaseService.addVocabCard(userCard);

          results['savedCards'].add(userCard);
          debugPrint(
              'Successfully saved meaning to user collection: ${meaning.word}');
        } catch (e) {
          debugPrint('Error saving meaning to user collection: $e');
          results['errors']
              .add('Failed to save meaning: ${meaning.definition}');
        }
      }

      // User stats are automatically updated by addVocabCard method

      return results;
    } catch (e) {
      debugPrint('Error saving selected meanings to user: $e');
      return {
        'success': false,
        'message': 'Error saving to user collection: $e',
      };
    }
  }

  /// Check if word already exists in user's collection
  Future<bool> wordExistsInUserCollection(String word) async {
    try {
      final userCards = await _firebaseService.fetchVocabulary();
      return userCards.any((card) => card.matchesWord(word));
    } catch (e) {
      debugPrint('Error checking word existence in user collection: $e');
      return false;
    }
  }

  /// Generate vocabulary using AI services
  Future<List<VocabCard>> _generateVocabularyWithAI(
    String word,
    WidgetRef? ref,
  ) async {
    try {
      // Try to generate with multiple meanings first
      if (ref != null) {
        final multipleCards =
            await _aiService.generateVocabularyWithMultipleMeanings(word, ref);
        if (multipleCards.isNotEmpty) {
          return multipleCards;
        }
      }

      // Fallback to single card generation
      final singleCard = await _aiService.generateVocabCard(word);
      if (singleCard != null) {
        return [singleCard];
      }

      return [];
    } catch (e) {
      debugPrint('Error generating vocabulary with AI: $e');
      return [];
    }
  }

  /// Save generated cards to global repository and generate quiz content
  Future<void> _saveToGlobalRepository(List<VocabCard> cards) async {
    for (final card in cards) {
      try {
        // Save vocabulary card to global repository
        await _globalService.saveToGlobalRepository(card);
        debugPrint(
            '✅ Saved vocabulary card to global repository: ${card.word}');

        // Generate and save quiz content for the card
        await _generateAndSaveQuizContent(card);
      } catch (e) {
        debugPrint('Error saving card to global repository: ${card.word} - $e');
        // Continue with other cards even if one fails
      }
    }
  }

  /// Generate and save quiz content for a vocabulary card
  Future<void> _generateAndSaveQuizContent(VocabCard card) async {
    try {
      debugPrint('🎯 Starting quiz content generation for: ${card.word}');

      // Generate quiz content using AI
      final quizContent =
          await _quizGenerationService.generateQuizContentWithRetry(card);

      if (quizContent == null) {
        debugPrint('⚠️ Failed to generate quiz content for: ${card.word}');
        return;
      }

      // Save quiz content to global database
      final success = await _quizStorageService.saveQuizContent(quizContent);

      if (success) {
        debugPrint(
            '✅ Successfully saved quiz content for: ${card.word} (${quizContent.totalQuestions} questions)');
      } else {
        debugPrint('❌ Failed to save quiz content for: ${card.word}');
      }
    } catch (e) {
      debugPrint('❌ Error generating/saving quiz content for ${card.word}: $e');
      // Don't throw - we don't want quiz content generation failure to block vocabulary creation
    }
  }

  /// Get vocabulary suggestions from global repository
  /// Useful for autocomplete or word suggestions
  Future<List<VocabCard>> getVocabularySuggestions({
    String? startsWith,
    String? level,
    List<String>? types,
    int limit = 20,
  }) async {
    try {
      // For now, return popular words as suggestions
      // This can be enhanced with more sophisticated filtering
      return await _globalService.getPopularWords(limit: limit);
    } catch (e) {
      debugPrint('Error getting vocabulary suggestions: $e');
      return [];
    }
  }

  /// Get statistics about global repository usage
  Future<Map<String, dynamic>> getGlobalRepositoryStats() async {
    try {
      final popularWords = await _globalService.getPopularWords(limit: 10);
      final recentWords = await _globalService.getRecentWords(limit: 10);

      return {
        'totalWords': popularWords.length, // This is approximate
        'popularWords': popularWords
            .map((card) => {
                  'word': card.word,
                  'level': card.level,
                  'type': card.type,
                })
            .toList(),
        'recentWords': recentWords
            .map((card) => {
                  'word': card.word,
                  'level': card.level,
                  'type': card.type,
                })
            .toList(),
      };
    } catch (e) {
      debugPrint('Error getting global repository stats: $e');
      return {
        'totalWords': 0,
        'popularWords': [],
        'recentWords': [],
      };
    }
  }

  /// Batch check multiple words against global repository
  /// Useful for optimizing multiple word operations
  Future<Map<String, bool>> batchCheckWordsInGlobal(List<String> words) async {
    try {
      final results = await _globalService.batchSearchGlobalVocabulary(words);
      return results.map((word, card) => MapEntry(word, card != null));
    } catch (e) {
      debugPrint('Error in batch check words: $e');
      return {};
    }
  }
}
