// // import 'dart:io';
// // import 'package:flutter/material.dart';
// // import 'package:image_picker/image_picker.dart';
// // import 'package:image_cropper/image_cropper.dart';
// // import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
// // import 'package:flutter_riverpod/flutter_riverpod.dart';
// // import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
// // import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';

// // /// Service class for OCR-based vocabulary capture
// // class OcrService {
// //   final ImagePicker _imagePicker = ImagePicker();
// //   final VocabGenerationService _generationService = VocabGenerationService();

// //   /// Capture an image from camera or gallery and process it for OCR
// //   Future<String?> captureImage(
// //     ImageSource source,
// //     WidgetRef ref,
// //   ) async {
// //     // Update the capture source
// //     ref.read(captureSourceProvider.notifier).state =
// //         source == ImageSource.camera
// //             ? CaptureSource.camera
// //             : CaptureSource.gallery;

// //     try {
// //       // Pick image
// //       final XFile? image = await _imagePicker.pickImage(source: source);
// //       if (image == null) return null;

// //       File imageFile = File(image.path);

// //       // Crop image
// //       final croppedFile = await cropImage(imageFile);
// //       if (croppedFile == null) return null;

// //       // Perform OCR
// //       final text = await performOcr(croppedFile);

// //       return text;
// //     } catch (e) {
// //       debugPrint('Error capturing image: $e');
// //       ref.read(captureErrorProvider.notifier).state =
// //           'Error capturing image: $e';
// //       return null;
// //     }
// //   }

// //   /// Crop the image using image_cropper
// //   Future<File?> cropImage(File imageFile) async {
// //     try {
// //       final croppedFile = await ImageCropper().cropImage(
// //         sourcePath: imageFile.path,
// //         uiSettings: [
// //           AndroidUiSettings(
// //             toolbarTitle: 'Crop Text Area',
// //             toolbarColor: AppColors.blue,
// //             toolbarWidgetColor: AppColors.white,
// //             activeControlsWidgetColor: AppColors.blue,
// //             lockAspectRatio: false,
// //           ),
// //           IOSUiSettings(
// //             title: 'Crop Text Area',
// //           ),
// //         ],
// //       );
// //       return croppedFile != null ? File(croppedFile.path) : null;
// //     } catch (e) {
// //       debugPrint('Error cropping image: $e');
// //       return null;
// //     }
// //   }

// //   /// Perform OCR on the given image file
// //   Future<String> performOcr(File imageFile) async {
// //     final inputImage = InputImage.fromFile(imageFile);
// //     final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);

// //     try {
// //       final RecognizedText recognizedText =
// //           await textRecognizer.processImage(inputImage);
// //       return recognizedText.text;
// //     } catch (e) {
// //       debugPrint('Error during OCR: $e');
// //       return '';
// //     } finally {
// //       await textRecognizer.close();
// //     }
// //   }

// //   /// Process captured image and generate vocabulary
// //   Future<bool> processCapturedImage(
// //     ImageSource source,
// //     BuildContext context,
// //     WidgetRef ref,
// //   ) async {
// //     try {
// //       // Capture and process image
// //       final ocrText = await captureImage(source, ref);

// //       if (ocrText == null || ocrText.trim().isEmpty) {
// //         ref.read(captureErrorProvider.notifier).state =
// //             'No text was detected in the image.';
// //         return false;
// //       }

// //       // Use the extracted text to generate vocabulary
// //       final vocabCard = await _generationService.processOcrText(ocrText, ref);

// //       if (vocabCard != null) {
// //         // Store the generated card
// //         ref.read(generatedVocabProvider.notifier).state = vocabCard;
// //         return true;
// //       }

// //       return false;
// //     } catch (e) {
// //       debugPrint('Error processing captured image: $e');
// //       ref.read(captureErrorProvider.notifier).state =
// //           'Error processing image: $e';
// //       return false;
// //     }
// //   }

// //   /// Reset the OCR state
// //   void resetState(WidgetRef ref) {
// //     ref.read(captureStateProvider.notifier).state = CaptureState.initial;
// //     ref.read(captureErrorProvider.notifier).state = null;
// //   }
// // }

// // /// Provider for the OCR service
// // final ocrServiceProvider = Provider<OcrService>((ref) {
// //   return OcrService();
// // });

// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:image_cropper/image_cropper.dart';
// import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';

// /// Service class for OCR-based vocabulary capture
// class OcrService {
//   final ImagePicker _imagePicker = ImagePicker();

//   /// Capture an image from camera or gallery
//   Future<String?> captureImage(
//     ImageSource source,
//     WidgetRef ref,
//   ) async {
//     // Update the capture source
//     ref.read(captureSourceProvider.notifier).state =
//         source == ImageSource.camera
//             ? CaptureSource.camera
//             : CaptureSource.gallery;

//     try {
//       // Update state to show we're capturing
//       ref.read(captureStateProvider.notifier).state = CaptureState.initial;

//       // Pick image
//       final XFile? image = await _imagePicker.pickImage(
//         source: source,
//         imageQuality: 80, // Reduce image size while maintaining quality
//       );
//       if (image == null) return null;

//       File imageFile = File(image.path);

//       // Crop image
//       final croppedFile = await cropImage(imageFile);
//       if (croppedFile == null) return null;

//       // Perform OCR
//       final text = await performOcr(croppedFile);
//       return text;
//     } catch (e) {
//       debugPrint('Error capturing image: $e');
//       ref.read(captureErrorProvider.notifier).state =
//           'Error capturing image: $e';
//       return null;
//     }
//   }

//   /// Crop the image using image_cropper
//   Future<File?> cropImage(File imageFile) async {
//     try {
//       final croppedFile = await ImageCropper().cropImage(
//         sourcePath: imageFile.path,
//         aspectRatio:
//             const CropAspectRatio(ratioX: 3, ratioY: 1), // Better for text
//         uiSettings: [
//           AndroidUiSettings(
//             toolbarTitle: 'Crop Text Area',
//             toolbarColor: AppColors.blue,
//             toolbarWidgetColor: AppColors.white,
//             activeControlsWidgetColor: AppColors.blue,
//             lockAspectRatio: false,
//           ),
//           IOSUiSettings(
//             title: 'Crop Text Area',
//             aspectRatioPickerButtonHidden: true,
//             rotateButtonsHidden: true,
//             resetButtonHidden: false,
//             aspectRatioLockEnabled: false,
//           ),
//         ],
//       );
//       return croppedFile != null ? File(croppedFile.path) : null;
//     } catch (e) {
//       debugPrint('Error cropping image: $e');
//       return null;
//     }
//   }

//   /// Perform OCR on the given image file
//   Future<String> performOcr(File imageFile) async {
//     final inputImage = InputImage.fromFile(imageFile);
//     final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);

//     try {
//       final RecognizedText recognizedText =
//           await textRecognizer.processImage(inputImage);
//       return recognizedText.text;
//     } catch (e) {
//       debugPrint('Error during OCR: $e');
//       return '';
//     } finally {
//       await textRecognizer.close();
//     }
//   }

//   /// Process and clean up OCR text
//   String processText(String ocrText) {
//     if (ocrText.isEmpty) return '';

//     // Remove extra whitespace and line breaks
//     String processed = ocrText.trim().replaceAll(RegExp(r'\s+'), ' ');

//     // Try to extract the most likely word/phrase
//     // Look for short, clear text - probably what the user intended to capture
//     final words = processed.split(' ');

//     // If it's just one or two words (likely a vocabulary term), use as is
//     if (words.length <= 2) {
//       return processed;
//     }

//     // For longer text, try to identify the most prominent word
//     // This is simplified - in real app, you might use font size detection or position

//     // Option 1: Take the first word if it seems significant (not a preposition, article)
//     final smallWords = [
//       'the',
//       'a',
//       'an',
//       'in',
//       'on',
//       'at',
//       'to',
//       'of',
//       'for',
//       'with'
//     ];
//     if (!smallWords.contains(words[0].toLowerCase())) {
//       return words[0];
//     }

//     // Option 2: Look for the longest word as a fallback (might be the main term)
//     String longestWord = '';
//     for (final word in words) {
//       if (word.length > longestWord.length) {
//         longestWord = word;
//       }
//     }

//     // If we found a meaningful longest word, use it
//     if (longestWord.length > 3) {
//       return longestWord;
//     }

//     // If all else fails, just return the first 1-2 words
//     return words.length > 1 ? '${words[0]} ${words[1]}' : words[0];
//   }

//   /// Reset the OCR state
//   void resetState(WidgetRef ref) {
//     ref.read(captureStateProvider.notifier).state = CaptureState.initial;
//     ref.read(captureErrorProvider.notifier).state = null;
//   }
// }

// /// Provider for the OCR service
// final ocrServiceProvider = Provider<OcrService>((ref) {
//   return OcrService();
// });

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';

/// Service class for OCR-based vocabulary capture
class OcrService {
  final ImagePicker _imagePicker = ImagePicker();
  final VocabGenerationService _generationService = VocabGenerationService();

  /// Capture an image from camera or gallery and process it for OCR
  Future<String?> captureImage(
    ImageSource source,
    WidgetRef ref,
  ) async {
    // Update the capture source
    ref.read(captureSourceProvider.notifier).state =
        source == ImageSource.camera
            ? CaptureSource.camera
            : CaptureSource.gallery;

    try {
      // Pick image
      final XFile? image = await _imagePicker.pickImage(source: source);
      if (image == null) return null;

      File imageFile = File(image.path);

      // Crop image - always start with the full image
      final croppedFile = await cropImage(imageFile);
      if (croppedFile == null) return null;

      // Perform OCR
      final text = await performOcr(croppedFile);

      return text;
    } catch (e) {
      debugPrint('Error capturing image: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error capturing image: $e';
      return null;
    }
  }

  /// Crop the image using image_cropper
  Future<File?> cropImage(File imageFile) async {
    try {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        // Reset the crop area each time by not providing initialCropAspectRatio or similar parameters
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop Text Area',
            toolbarColor: AppColors.primaryLight,
            toolbarWidgetColor: AppColors.white,
            activeControlsWidgetColor: AppColors.primaryLight,
            lockAspectRatio: false,
            initAspectRatio: CropAspectRatioPreset
                .original, // Start with original image ratio
            dimmedLayerColor: AppColors.black
                .withAlpha(128),// Darken the area outside of crop
          ),
          IOSUiSettings(
            title: 'Crop Text Area',
            resetAspectRatioEnabled: true,
            aspectRatioPickerButtonHidden: false,
            aspectRatioLockEnabled: false,
          ),
        ],
      );
      return croppedFile != null ? File(croppedFile.path) : null;
    } catch (e) {
      debugPrint('Error cropping image: $e');
      return null;
    }
  }

  /// Perform OCR on the given image file
  Future<String> performOcr(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);
    final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);

    try {
      final RecognizedText recognizedText =
          await textRecognizer.processImage(inputImage);
      return recognizedText.text;
    } catch (e) {
      debugPrint('Error during OCR: $e');
      return '';
    } finally {
      await textRecognizer.close();
    }
  }

  /// Process text from OCR to clean it up
  String processText(String text) {
    // Remove extra whitespace and line breaks
    String cleaned =
        text.trim().replaceAll(RegExp(r'\s+'), ' ').replaceAll('\n', ' ');

    // If text is too long, truncate to likely just be a single word/phrase
    if (cleaned.length > 50) {
      // Try to find a good breakpoint
      int breakPoint = cleaned.indexOf(' ', 20);
      if (breakPoint > 0 && breakPoint < 50) {
        cleaned = cleaned.substring(0, breakPoint);
      } else {
        // Default to first 50 chars
        cleaned = cleaned.substring(0, 50);
      }
    }

    return cleaned;
  }

  /// Process captured image and generate vocabulary
  Future<bool> processCapturedImage(
    ImageSource source,
    BuildContext context,
    WidgetRef ref,
  ) async {
    try {
      // Capture and process image
      final ocrText = await captureImage(source, ref);

      if (ocrText == null || ocrText.trim().isEmpty) {
        ref.read(captureErrorProvider.notifier).state =
            'No text was detected in the image.';
        return false;
      }

      // Use the extracted text to generate vocabulary
      final vocabCard = await _generationService.processOcrText(ocrText, ref);

      if (vocabCard != null) {
        // Store the generated card
        ref.read(generatedVocabProvider.notifier).state = vocabCard;
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error processing captured image: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error processing image: $e';
      return false;
    }
  }

  /// Reset the OCR state
  void resetState(WidgetRef ref) {
    ref.read(captureStateProvider.notifier).state = CaptureState.initial;
    ref.read(captureErrorProvider.notifier).state = null;
  }
}

/// Provider for the OCR service
final ocrServiceProvider = Provider<OcrService>((ref) {
  return OcrService();
});
