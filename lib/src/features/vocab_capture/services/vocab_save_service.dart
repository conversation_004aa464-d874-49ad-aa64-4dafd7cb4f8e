import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/achievements/points/points_manager.dart';
import 'package:vocadex/src/features/dashboard/widgets/total_cards.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';

import 'package:vocadex/src/features/weekly_target/weekly_goals_manager.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_capture/utils/allocation_utils.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/features/weekly_target/weekly_targets_widget.dart';
import 'package:vocadex/src/features/home/<USER>';
import 'package:vocadex/src/features/analytics/providers/daily_metrics_provider.dart';

/// Service for saving vocabulary cards to the database
class VocabSaveService {
  final FirebaseService _firebaseService = FirebaseService();
  final WeeklyGoalsManager _goalsManager = WeeklyGoalsManager();
  final PointsManager _pointsManager = PointsManager();

  /// Save a vocabulary card to the database
  /// Returns a success flag and a map with additional information
  Future<Map<String, dynamic>> saveVocabulary(
    BuildContext context,
    VocabCard vocabCard,
    WidgetRef ref,
  ) async {
    try {
      // Update state to saving
      ref.read(captureStateProvider.notifier).state = CaptureState.saving;

      // Check if the word already exists in the collection
      final existingCards = await _firebaseService.fetchVocabulary();
      final wordExists = existingCards.any((existingCard) =>
          existingCard.word.toLowerCase() == vocabCard.word.toLowerCase());

      if (wordExists) {
        // Show toast that the word already exists
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Already in Your Deck',
            description: 'This word is already in your vocabulary deck!',
          );
        }

        ref.read(captureStateProvider.notifier).state = CaptureState.error;
        return {
          'success': false,
          'message': 'Word already exists in your deck.',
          'isPremium': ref.watch(subscriptionStateProvider),
        };
      }

      // Check card allocation limits first
      final allocUtil = AllocationUtils();
      final canAdd = await allocUtil.canAddCard(ref);
      final isPremium = ref.watch(subscriptionStateProvider);

      if (!canAdd && !isPremium) {
        // Show upgrade dialog for free users who've reached their limit
        allocUtil.showUpgradeDialog(context);

        ref.read(captureStateProvider.notifier).state = CaptureState.error;
        return {
          'success': false,
          'message': 'No remaining free cards for today.',
          'isPremium': isPremium,
        };
      }

      // Save the vocabulary card
      await _firebaseService.addVocabCard(vocabCard);

      // Update the card allocation for free users
      if (!isPremium) {
        await _firebaseService.updateCardAllocation(1);

        // Update the remaining cards count in the provider
        final details = await _firebaseService.getUserAllocationDetails();
        final limit = details['allocation_limit'] as int;
        final used = details['allocation_used_today'] as int;
        ref.read(remainingCardsProvider.notifier).state = limit - used;
      }

      // Update weekly goals for each type in the card
      await _goalsManager.updateWeeklyGoalsForNewCard(vocabCard);

      // Check if this card completes any weekly goals
      final goalCompletion =
          await _goalsManager.checkGoalCompletion(vocabCard.type);

      // Award points for completing weekly goals
      List<GoalCompletion> completedGoals = [];
      if (goalCompletion['hasCompletedGoals']) {
        final completedTypes = goalCompletion['completedTypes'] as List<String>;

        for (final type in completedTypes) {
          await _pointsManager.awardWeeklyGoalPoints(type);
          completedGoals.add(GoalCompletion(type: type, points: 50));

          // Show achievement toast if context is still available
          if (context.mounted) {
            showSuccessToast(
              context,
              title: 'Success',
              description: 'Weekly goal completed: $type! +50 points',
            );
          }
        }

        // Update the completed goals provider
        ref.read(completedGoalsProvider.notifier).state = completedGoals;
      }

      // IMPORTANT: Refresh the providers for weekly targets and total cards
      // This will trigger UI updates in the home screen
      ref.refresh(weeklyGoalsInfoProvider);
      ref.refresh(totalCardsProvider);

      // Track analytics for word addition
      try {
        final dailyMetrics = ref.read(dailyMetricsProvider);
        await AnalyticsService.instance.trackWordAdded(
          vocabCard,
          isPremium: isPremium,
          isFromGlobalRepo: false, // This is direct word addition
          dailyWordsCount: dailyMetrics.wordsAdded + 1,
        );

        // Update daily metrics
        await ref.read(dailyMetricsProvider.notifier).incrementWordsAdded();

        // Track daily active user
        await AnalyticsService.instance.track('Daily Active User - Word Added');
      } catch (e) {
        debugPrint('Analytics tracking error: $e');
      }

      // Update state to saved
      ref.read(captureStateProvider.notifier).state = CaptureState.saved;

      // Show success message
      if (context.mounted) {
        if (isPremium) {
          showSuccessToast(
            context,
            title: 'Success',
            description: 'Vocabulary saved successfully!',
          );
        } else {
          final remaining = ref.read(remainingCardsProvider);
          showSuccessToast(
            context,
            title: 'Success',
            description:
                'Vocabulary saved! You have $remaining cards remaining today.',
          );
        }
      }

      // Return success result with additional information
      return {
        'success': true,
        'message': 'Vocabulary saved successfully!',
        'vocabCard': vocabCard,
        'isPremium': isPremium,
        'remainingCards': ref.read(remainingCardsProvider),
        'completedGoals': completedGoals,
      };
    } catch (e) {
      // Handle errors
      debugPrint('Error saving vocabulary: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error saving vocabulary: $e';
      ref.read(captureStateProvider.notifier).state = CaptureState.error;

      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }

      return {
        'success': false,
        'message': 'Error: $e',
        'isPremium': ref.watch(subscriptionStateProvider),
      };
    }
  }

  /// Reset the save state
  void resetState(WidgetRef ref) {
    ref.read(captureStateProvider.notifier).state = CaptureState.initial;
    ref.read(captureErrorProvider.notifier).state = null;
    ref.read(completedGoalsProvider.notifier).state = [];
  }
}

/// Provider for the vocabulary save service
final vocabSaveServiceProvider = Provider<VocabSaveService>((ref) {
  return VocabSaveService();
});
