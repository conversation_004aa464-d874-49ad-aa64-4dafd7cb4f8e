import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/ads/models/ad_trigger.dart';
import 'package:vocadex/src/features/ads/providers/ad_providers.dart';
import 'package:vocadex/src/features/dashboard/presentation/dashboard_screen.dart'; // For blankCardsProvider
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/features/achievements/points/points_manager.dart';

import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';
import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';
import 'package:vocadex/src/features/vocab_capture/services/centralized_vocabulary_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/weekly_target/weekly_goals_manager.dart';
import 'package:vocadex/src/services/firebase_service.dart';

class VocabCardCreationService {
  final FirebaseService _firebaseService = FirebaseService();
  final VocabGenerationService _aiService = VocabGenerationService();
  final WeeklyGoalsManager _goalsManager = WeeklyGoalsManager();
  final PointsManager _pointsManager = PointsManager();
  final CentralizedVocabularyService _centralizedService =
      CentralizedVocabularyService();

  /// Generate and save a new vocabulary card
  /// Returns a Map with 'success' and other relevant information
  Future<Map<String, dynamic>> createAndSaveVocabCard(
      BuildContext context, String text,
      [WidgetRef? ref]) async {
    try {
      // Check if user can add more cards today
      final canAdd = await _firebaseService.canAddMoreCards();

      // Get premium status - prefer using the ref if provided
      bool isPremium = false;
      if (ref != null) {
        isPremium = ref.read(subscriptionStateProvider);
      } else {
        // Fallback to direct Purchases check if no ref is provided
        try {
          final customerInfo = await Purchases.getCustomerInfo();
          isPremium = customerInfo.entitlements.active.containsKey('premium');
        } catch (e) {
          debugPrint('Error checking premium status: $e');
        }
      }

      if (!canAdd && !isPremium) {
        // Show ad before upgrade dialog for daily word limit
        if (context.mounted && ref != null) {
          await _showDailyLimitAdAndDialog(context, ref);
        } else if (context.mounted) {
          _showUpgradeDialog(context);
        }

        return {
          'success': false,
          'message': 'No remaining free cards for today.',
          'isPremium': false,
        };
      }

      // Generate the vocab card
      final vocabCard = await _aiService.generateVocabCard(text.trim());

      if (vocabCard == null) {
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Invalid Input',
            description: 'Not a valid word or phrase',
          );
        }
        return {
          'success': false,
          'message': 'Not a valid word or phrase',
          'isPremium': isPremium,
        };
      }

      // Save the vocab card
      await _firebaseService.addVocabCard(vocabCard);

      // Update the card allocation for free users
      if (!isPremium) {
        await _firebaseService.updateCardAllocation(1);
      }

      // Trigger dashboard refresh to update resource values if ref is provided
      if (ref != null) {
        try {
          // Force refresh the dashboard to update the blank cards counter
          ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
          // Explicitly refresh the blank cards provider
          final newValue = await ref.refresh(blankCardsProvider.future);
          debugPrint('Refreshed blank cards after creation: $newValue');
        } catch (e) {
          debugPrint('Error refreshing dashboard: $e');
        }
      }

      // Update weekly goals for each type in the card
      await _goalsManager.updateWeeklyGoalsForNewCard(vocabCard);

      // Check if this card completes any weekly goals
      final goalCompletion =
          await _goalsManager.checkGoalCompletion(vocabCard.type);

      // Award points for completing weekly goals
      if (goalCompletion['hasCompletedGoals']) {
        final completedTypes = goalCompletion['completedTypes'] as List<String>;

        for (final type in completedTypes) {
          await _pointsManager.awardWeeklyGoalPoints(type);

          // Show achievement toast if context is still available
          if (context.mounted) {
            showSuccessToast(context,
                title: 'Weekly Goal Completed',
                description: 'You completed the goal for $type! +50 points');
          }
        }
      }

      // Get remaining cards for today
      final details = await _firebaseService.getUserAllocationDetails();
      final remainingCards = isPremium
          ? -1
          : (details['allocation_limit'] as int) -
              (details['allocation_used_today'] as int);

      // Show success message
      if (context.mounted) {
        if (isPremium) {
          showSuccessToast(
            context,
            title: 'Success',
            description: 'Vocabulary saved successfully!',
          );
        } else {
          showSuccessToast(
            context,
            title: 'Success',
            description:
                'Vocabulary saved! You have $remainingCards cards remaining today.',
          );
        }
      }

      // Show ad after 3rd word for free users
      if (!isPremium && ref != null && context.mounted) {
        final wordsAddedToday =
            (details['allocation_limit'] as int) - remainingCards;
        if (wordsAddedToday == 3) {
          // Add a small delay for better UX
          Future.delayed(const Duration(milliseconds: 1000), () async {
            if (context.mounted) {
              await _showWordCountAdIfEligible(context, ref);
            }
          });
        }
      }

      // Return success result with card info
      return {
        'success': true,
        'message': 'Vocabulary saved successfully!',
        'vocabCard': vocabCard,
        'isPremium': isPremium,
        'remainingCards': remainingCards,
        'completedGoals': goalCompletion['completedTypes'] ?? [],
        'progressedGoals': goalCompletion['progressedTypes'] ?? [],
      };
    } catch (e) {
      debugPrint('Error saving vocabulary: $e');
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }
      // Get premium status for the error response
      bool isPremium = false;
      if (ref != null) {
        isPremium = ref.read(subscriptionStateProvider);
      } else {
        try {
          final customerInfo = await Purchases.getCustomerInfo();
          isPremium = customerInfo.entitlements.active.containsKey('premium');
        } catch (e) {
          debugPrint('Error checking premium status: $e');
        }
      }

      return {
        'success': false,
        'message': 'Error: $e',
        'isPremium': isPremium,
      };
    }
  }

  /// Create vocabulary using centralized system (global repository + user collection)
  /// This method implements the two-tier vocabulary system
  /// Returns available meanings for user selection
  Future<Map<String, dynamic>> createVocabularyWithCentralizedSystem(
    BuildContext context,
    String text,
    WidgetRef ref,
  ) async {
    try {
      debugPrint('Creating vocabulary with centralized system: $text');

      // Check if user can add more cards today
      final canAdd = await _firebaseService.canAddMoreCards();
      final isPremium = ref.read(subscriptionStateProvider);

      if (!canAdd && !isPremium) {
        // Show ad before upgrade dialog for daily word limit
        if (context.mounted) {
          await _showDailyLimitAdAndDialog(context, ref);
        }

        return {
          'success': false,
          'message': 'No remaining free cards for today.',
          'isPremium': isPremium,
          'requiresSelection': false,
        };
      }

      // Check if word already exists in user's collection
      final wordExistsInUser =
          await _centralizedService.wordExistsInUserCollection(text.trim());
      if (wordExistsInUser) {
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Already in Your Deck',
            description: 'This word is already in your vocabulary deck!',
          );
        }

        return {
          'success': false,
          'message': 'Word already exists in your deck.',
          'isPremium': isPremium,
          'requiresSelection': false,
        };
      }

      // Use centralized vocabulary service to create vocabulary
      final result = await _centralizedService.createVocabulary(
        text.trim(),
        isPremium: isPremium,
        ref: ref,
      );

      if (!result.success) {
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Invalid Input',
            description: result.message,
          );
        }

        return {
          'success': false,
          'message': result.message,
          'isPremium': isPremium,
          'requiresSelection': false,
        };
      }

      // Return available meanings for user selection
      return {
        'success': true,
        'message': result.message,
        'availableMeanings': result.availableMeanings,
        'foundInGlobal': result.foundInGlobal,
        'isPremium': isPremium,
        'requiresSelection': result.availableMeanings.length > 1,
      };
    } catch (e) {
      debugPrint('Error in centralized vocabulary creation: $e');
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }

      return {
        'success': false,
        'message': 'Error: $e',
        'isPremium': ref.read(subscriptionStateProvider),
        'requiresSelection': false,
      };
    }
  }

  /// Save selected meanings from centralized vocabulary creation to user collection
  Future<Map<String, dynamic>> saveSelectedMeaningsToUser(
    List<VocabCard> selectedMeanings,
    BuildContext context,
    WidgetRef ref,
  ) async {
    try {
      debugPrint(
          'Saving ${selectedMeanings.length} selected meanings to user collection');

      final isPremium = ref.read(subscriptionStateProvider);

      // Save selected meanings to user collection
      final saveResult = await _centralizedService.saveSelectedMeaningsToUser(
        selectedMeanings,
        context,
        ref,
      );

      if (!saveResult['success']) {
        return {
          'success': false,
          'message': saveResult['message'] ?? 'Failed to save vocabulary',
          'isPremium': isPremium,
        };
      }

      final savedCards = saveResult['savedCards'] as List<VocabCard>;

      // Update card allocation for free users
      if (!isPremium) {
        await _firebaseService.updateCardAllocation(savedCards.length);
      }

      // Update weekly goals for each saved card
      for (final card in savedCards) {
        await _goalsManager.updateWeeklyGoalsForNewCard(card);
      }

      // Check for completed goals
      final completedGoals = <String>[];
      for (final card in savedCards) {
        final goalCompletion =
            await _goalsManager.checkGoalCompletion(card.type);
        if (goalCompletion['hasCompletedGoals']) {
          final completedTypes =
              goalCompletion['completedTypes'] as List<String>;
          completedGoals.addAll(completedTypes);
        }
      }

      // Award points for completed goals
      for (final type in completedGoals.toSet()) {
        await _pointsManager.awardWeeklyGoalPoints(type);
        if (context.mounted) {
          showSuccessToast(
            context,
            title: 'Weekly Goal Completed',
            description: 'You completed the goal for $type! +50 points',
          );
        }
      }

      // Refresh dashboard
      try {
        ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
        final newValue = await ref.refresh(blankCardsProvider.future);
        debugPrint(
            'Refreshed blank cards after centralized creation: $newValue');
      } catch (e) {
        debugPrint('Error refreshing dashboard: $e');
      }

      return {
        'success': true,
        'message': 'Vocabulary saved successfully!',
        'savedCards': savedCards,
        'isPremium': isPremium,
        'completedGoals': completedGoals.toSet().toList(),
      };
    } catch (e) {
      debugPrint('Error saving selected meanings: $e');
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }

      return {
        'success': false,
        'message': 'Error: $e',
        'isPremium': ref.read(subscriptionStateProvider),
      };
    }
  }

  /// Show a summary of the weekly goals progress
  Future<void> showWeeklyGoalsProgress(BuildContext context) async {
    final goalsInfo = await _goalsManager.getWeeklyGoalsInfo();

    if (!context.mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppColors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (_, controller) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.light
                ? AppColors.white
                : AppColors.backgroundDark,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Weekly Goals Progress',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),

              // Week range and days remaining
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColors.infoLight,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today,
                        color: AppColors.infoLight, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Week: ${goalsInfo['weekRange']}',
                      style: TextStyle(
                        color: AppColors.infoDark,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${goalsInfo['remainingDays']} days left',
                      style: TextStyle(
                        color: AppColors.infoDark,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Goal description
              const Text(
                'Capture 5 cards of each word type every week',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),

              const SizedBox(height: 24),

              // Progress bars
              Expanded(
                child: ListView(
                  controller: controller,
                  children: _buildProgressBars(goalsInfo),
                ),
              ),

              const SizedBox(height: 16),

              // Overall progress summary
              _buildOverallProgress(goalsInfo),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildProgressBars(Map<String, dynamic> goalsInfo) {
    final currentProgress = goalsInfo['currentProgress'] as Map<String, int>;
    final targetPerType = goalsInfo['targetPerType'] as int;

    // Word types with their display names and icons
    final wordTypes = [
      // {
      //   'key': 'verb',
      //   'name': 'Verbs',
      //   'icon': Icons.directions_run,
      //   'color': AppColors.blue
      // },
      // {
      //   'key': 'noun',
      //   'name': 'Nouns',
      //   'icon': Icons.apple,
      //   'color': AppColors.red
      // },
      // {
      //   'key': 'adjective',
      //   'name': 'Adjectives',
      //   'icon': Icons.thermostat,
      //   'color': AppColors.orange
      // },
      // {
      //   'key': 'adverb',
      //   'name': 'Adverbs',
      //   'icon': Icons.timer,
      //   'color': AppColors.purple
      // },
      // {
      //   'key': 'preposition',
      //   'name': 'Prepositions',
      //   'icon': Icons.arrow_forward,
      //   'color': AppColors.green
      // },
      // {
      //   'key': 'pronoun',
      //   'name': 'Pronouns',
      //   'icon': Icons.person,
      //   'color': Colors.teal
      // },
      // {
      //   'key': 'conjunction',
      //   'name': 'Conjunctions',
      //   'icon': Icons.link,
      //   'color': Colors.amber
      // },
      // {
      //   'key': 'interjection',
      //   'name': 'Interjections',
      //   'icon': Icons.sentiment_satisfied_alt,
      //   'color': Colors.pink
      // },
    ];

    return wordTypes.map((type) {
      final typeKey = type['key'] as String;
      final current = currentProgress[typeKey] ?? 0;
      final progress = current / targetPerType;
      final isComplete = current >= targetPerType;

      return Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  type['icon'] as IconData,
                  color: type['color'] as Color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  type['name'] as String,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  '$current/$targetPerType',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color:
                        isComplete ? AppColors.green : type['color'] as Color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Stack(
              children: [
                // Background progress bar
                Container(
                  height: 12,
                  decoration: BoxDecoration(
                    color: (type['color'] as Color).withAlpha(26),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                // Foreground progress bar
                Container(
                  height: 12,
                  width: progress > 1 ? double.infinity : (progress * 300),
                  decoration: BoxDecoration(
                    color:
                        isComplete ? AppColors.green : type['color'] as Color,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                // Completed badge
                if (isComplete)
                  Positioned(
                    right: 8,
                    top: 0,
                    bottom: 0,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: AppColors.white,
                          size: 10,
                        ),
                        const SizedBox(width: 2),
                        Container(
                          padding: const EdgeInsets.only(right: 2),
                          child: const Text(
                            'COMPLETE',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildOverallProgress(Map<String, dynamic> goalsInfo) {
    final currentProgress = goalsInfo['currentProgress'] as Map<String, int>;
    final targetPerType = goalsInfo['targetPerType'] as int;

    // Calculate overall progress
    int totalCaptured = 0;
    int totalTypes = 0;

    currentProgress.forEach((type, count) {
      totalCaptured += count > targetPerType ? targetPerType : count;
      totalTypes++;
    });

    // Ensure we're tracking at least the 8 main types
    if (totalTypes < 8) totalTypes = 8;

    final totalTarget = totalTypes * targetPerType;
    final overallProgress = totalCaptured / totalTarget;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.infoLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Overall Progress',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$totalCaptured/$totalTarget words captured',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${(overallProgress * 100).toStringAsFixed(0)}%',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: overallProgress,
            backgroundColor: AppColors.getBackgroundColor(Brightness.light),
            valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.getInfoColor(Brightness.light)),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ),
    );
  }

  /// Show ad after user has added 3 words
  Future<void> _showWordCountAdIfEligible(
      BuildContext context, WidgetRef ref) async {
    final adService = ref.read(adServiceProvider);
    final isPremium = ref.read(subscriptionStateProvider);

    await adService.showAdIfEligible(
      AdTrigger.wordCountMilestone,
      isPremium,
      onAdShown: () {
        debugPrint('Word count ad shown after 3rd word');
      },
      onAdClosed: () {
        debugPrint('Word count ad closed');
      },
      onAdFailed: () {
        debugPrint('Word count ad failed to show');
      },
      onSkipped: () {
        debugPrint('Word count ad skipped');
      },
    );
  }

  /// Show ad before upgrade dialog for daily word limit
  Future<void> _showDailyLimitAdAndDialog(
      BuildContext context, WidgetRef ref) async {
    final adService = ref.read(adServiceProvider);
    final isPremium = ref.read(subscriptionStateProvider);

    await adService.showAdIfEligible(
      AdTrigger.dailyWordLimit,
      isPremium,
      onAdShown: () {
        debugPrint('Daily word limit ad shown');
      },
      onAdClosed: () {
        debugPrint('Daily word limit ad closed');
        // Show upgrade dialog after ad is closed
        if (context.mounted) {
          _showUpgradeDialog(context);
        }
      },
      onAdFailed: () {
        debugPrint('Daily word limit ad failed to show');
        // Show upgrade dialog if ad fails
        if (context.mounted) {
          _showUpgradeDialog(context);
        }
      },
      onSkipped: () {
        debugPrint('Daily word limit ad skipped');
        // Show upgrade dialog if ad is skipped
        if (context.mounted) {
          _showUpgradeDialog(context);
        }
      },
    );
  }

  /// Show premium upgrade dialog
  void _showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Out of Free Cards'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.lock_clock,
              size: 64,
              color: Colors.amber,
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ve used all your free cards for today.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, dynamic>>(
              future: _firebaseService.getUserAllocationDetails(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final details = snapshot.data!;
                  final lastReset =
                      details['last_allocation_reset'] as DateTime;
                  final nextReset = DateTime(
                    lastReset.year,
                    lastReset.month,
                    lastReset.day + 1,
                  );
                  final timeRemaining = nextReset.difference(DateTime.now());

                  final hours = timeRemaining.inHours;
                  final minutes = timeRemaining.inMinutes % 60;

                  return Text(
                    'Next reset in: ${hours}h ${minutes}m',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  );
                }
                return const Text('Checking next reset time...');
              },
            ),
            const SizedBox(height: 24),
            const Text(
              'Upgrade to Premium for unlimited cards and more features!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Later'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: AppColors.white,
            ),
            onPressed: () {
              Navigator.pop(context);

              _navigateToPremiumScreen(context);
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  /// Navigate to premium upgrade screen
  void _navigateToPremiumScreen(BuildContext context) {
    // Use the ShowPaywall class to present the paywall
    final showPaywall = ShowPaywall();
    showPaywall.presentPaywall();
  }

  /// Show the remaining card allocation to the user
  Future<void> showRemainingCardAllocation(BuildContext context,
      [WidgetRef? ref]) async {
    // Get premium status - prefer using the ref if provided
    bool isPremium = false;
    if (ref != null) {
      isPremium = ref.read(subscriptionStateProvider);
    } else {
      // Fallback to direct Purchases check if no ref is provided
      try {
        final customerInfo = await Purchases.getCustomerInfo();
        isPremium = customerInfo.entitlements.active.containsKey('premium');
      } catch (e) {
        debugPrint('Error checking premium status: $e');
      }
    }

    if (isPremium) {
      return; // No need to show allocation for premium users
    }

    final details = await _firebaseService.getUserAllocationDetails();
    final used = details['allocation_used_today'] as int;
    final limit = details['allocation_limit'] as int;
    final remaining = limit - used;

    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Daily Card Allocation'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              Text(
                'You have $remaining/$limit free cards remaining today.',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              LinearProgressIndicator(
                value: used / limit,
                backgroundColor: AppColors.grey,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
                minHeight: 10,
              ),
              const SizedBox(height: 24),
              const Text(
                'Upgrade to Premium for unlimited cards!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: AppColors.white,
              ),
              onPressed: () {
                Navigator.pop(context);
                _navigateToPremiumScreen(context);
              },
              child: const Text('Upgrade Now'),
            ),
          ],
        ),
      );
    }
  }
}
