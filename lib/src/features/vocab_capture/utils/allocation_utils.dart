import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Utility class for handling card allocation limits for free users
class AllocationUtils {
  final FirebaseService _firebaseService = FirebaseService();

  /// Constants
  static const int freeUserDailyCards = 5;

  /// Load user information and allocation data
  Future<void> loadUserInfo(WidgetRef ref) async {
    try {
      // Check premium status
      final isPremium = ref.watch(subscriptionStateProvider);

      // Get allocation details for free users
      if (!isPremium) {
        final details = await _firebaseService.getUserAllocationDetails();
        final limit = details['allocation_limit'] as int;
        final used = details['allocation_used_today'] as int;
        ref.read(remainingCardsProvider.notifier).state = limit - used;
      }
    } catch (e) {
      debugPrint('Error loading user info: $e');
    }
  }

  /// Check if the user can add more cards
  Future<bool> canAddCard(WidgetRef ref) async {
    // Premium users can always add cards
    final isPremium = ref.read(subscriptionStateProvider);
    if (isPremium) return true;

    try {
      return await _firebaseService.canAddMoreCards();
    } catch (e) {
      debugPrint('Error checking card allocation: $e');
      return false;
    }
  }

  /// Get information about the next card allocation reset
  Future<Map<String, dynamic>> getNextResetInfo() async {
    try {
      final details = await _firebaseService.getUserAllocationDetails();
      final lastReset = details['last_allocation_reset'] as DateTime;

      // Calculate when the next reset will happen (next day at midnight)
      final now = DateTime.now();
      final nextResetDate = DateTime(
        now.year,
        now.month,
        now.day + 1,
      );

      // Calculate time remaining until next reset
      final duration = nextResetDate.difference(now);

      return {
        'nextResetDate': nextResetDate,
        'hoursRemaining': duration.inHours,
        'minutesRemaining': duration.inMinutes % 60,
      };
    } catch (e) {
      debugPrint('Error getting next reset info: $e');

      // Return a default value if there's an error
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);

      return {
        'nextResetDate': tomorrow,
        'hoursRemaining': 24,
        'minutesRemaining': 0,
      };
    }
  }

  /// Show premium upgrade dialog when user is out of cards
  void showUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Out of Free Cards'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.lock_clock,
              size: 64,
              color: Colors.amber,
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ve used all your free cards for today.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            FutureBuilder<Map<String, dynamic>>(
              future: getNextResetInfo(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final resetInfo = snapshot.data!;
                  return Text(
                    'Next reset in: ${resetInfo['hoursRemaining']}h ${resetInfo['minutesRemaining']}m',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                }
                return const Text('Checking next reset time...');
              },
            ),
            const SizedBox(height: 16),
            const Text(
              'Upgrade to Premium for unlimited cards and more features!',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Later'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: AppColors.getButtonForegroundColor(
                  Theme.of(context).brightness),
            ),
            onPressed: () {
              Navigator.pop(context);
              _navigateToPremiumScreen(context);
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  /// Show the remaining card allocation to the user
  void showRemainingCardAllocation(BuildContext context, WidgetRef ref) {
    final isPremium = ref.read(subscriptionStateProvider);
    if (isPremium) return; // No need for premium users

    final remaining = ref.read(remainingCardsProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Daily Card Allocation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            Text(
              'You have $remaining/$freeUserDailyCards free cards remaining today.',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: (freeUserDailyCards - remaining) / freeUserDailyCards,
              backgroundColor: AppColors.grey,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
              minHeight: 10,
            ),
            const SizedBox(height: 24),
            const Text(
              'Upgrade to Premium for unlimited cards!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: AppColors.getButtonForegroundColor(
                  Theme.of(context).brightness),
            ),
            onPressed: () {
              Navigator.pop(context);
              _navigateToPremiumScreen(context);
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  /// Navigate to premium upgrade screen
  void _navigateToPremiumScreen(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Subscription'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.star,
              size: 64,
              color: Colors.amber,
            ),
            SizedBox(height: 16),
            Text(
              'Get unlimited vocabulary cards and premium features!',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              '• Unlimited vocabulary cards\n'
              '• Custom quizzes with your selected words\n'
              '• Advanced analytics and progress tracking\n'
              '• No ads and premium themes',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Later'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: AppColors.getButtonForegroundColor(
                  Theme.of(context).brightness),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Subscribe Now'),
          ),
        ],
      ),
    );
  }
}
