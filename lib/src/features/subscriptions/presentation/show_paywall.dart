import 'package:flutter/material.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';

class ShowPaywall {
  Future<void> presentPaywall() async {
    final paywallResult =
        await RevenueCatUI.presentPaywall(displayCloseButton: true);
    debugPrint('Paywall result: $paywallResult');
  }

  Future<void> presentPaywallIfNeeded() async {
    final paywallResult = await RevenueCatUI.presentPaywallIfNeeded('premium',
        displayCloseButton: true); //pro / premium etc. entitlement name
    debugPrint('Paywall result: $paywallResult');
  }
}



//How to use

// final isPremium = ref.watch(subscriptionStateProvider); will give true or false