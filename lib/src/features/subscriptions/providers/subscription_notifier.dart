// subscription_notifier.dart
import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_notifier.g.dart';

@Riverpod(keepAlive: true)
class SubscriptionState extends _$SubscriptionState {
  late final void Function(CustomerInfo) _listener;

  @override
  bool build() {
    state = false;

    _listener = (customerInfo) => _updateState(customerInfo);
    Purchases.addCustomerInfoUpdateListener(_listener);
    _init();

    ref.onDispose(() {
      Purchases.removeCustomerInfoUpdateListener(_listener);
    });

    return state;
  }

  Future<void> _init() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      _updateState(customerInfo);
    } catch (e) {
      debugPrint('Error initializing subscription status: $e');
    }
  }

  void _updateState(CustomerInfo customerInfo) {
    state = customerInfo.entitlements.active.containsKey('premium');
  }

  Future<void> refresh() async {
    try {
      final info = await Purchases.getCustomerInfo();
      _updateState(info);
    } catch (e) {
      debugPrint('Error refreshing subscription status: $e');
    }
  }
}
