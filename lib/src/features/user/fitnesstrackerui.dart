// import 'dart:math' as math;

// import 'package:flutter/material.dart';
// // import 'package:rive/rive.dart' hide image;
// import 'package:syncfusion_flutter_gauges/gauges.dart';

// class FitnessTrackerScreen extends StatefulWidget {
//   const FitnessTrackerScreen({super.key});

//   @override
//   State<FitnessTrackerScreen> createState() => _FitnessTrackerScreenState();
// }

// class _FitnessTrackerScreenState extends State<FitnessTrackerScreen>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 2),
//     )..forward();
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.grey[50],
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             children: [
//               // Date navigation
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   IconButton(
//                     icon: const Icon(Icons.chevron_left, color: AppColors.black),
//                     onPressed: () {},
//                   ),
//                   const Expanded(
//                     child: Center(
//                       child: Text(
//                         "21 - 28\nAug",
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.w600,
//                         ),
//                       ),
//                     ),
//                   ),
//                   IconButton(
//                     icon: const Icon(Icons.chevron_right, color: AppColors.black),
//                     onPressed: () {},
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 20),

//               // Main calories circular progress
//               AnimatedBuilder(
//                 animation: _animationController,
//                 builder: (context, child) {
//                   return Row(
//                     children: [
//                       // Left side - calories burned
//                       Expanded(
//                         child: Column(
//                           children: [
//                             const Text(
//                               "520",
//                               style: TextStyle(
//                                 fontSize: 22,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             Text(
//                               "cal burn",
//                               style: TextStyle(
//                                 fontSize: 14,
//                                 color: AppColors.grey600,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),

//                       // Center - main progress
//                       SizedBox(
//                         width: 150,
//                         height: 150,
//                         child: Stack(
//                           alignment: Alignment.center,
//                           children: [
//                             SizedBox(
//                               width: 150,
//                               height: 150,
//                               child: CustomPaint(
//                                 painter: CircularProgressPainter(
//                                   progress: _animationController.value * 0.98,
//                                   color: AppColors.green[300]!,
//                                   backgroundColor: AppColors.grey300!,
//                                   strokeWidth: 12,
//                                 ),
//                               ),
//                             ),
//                             Column(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 const Text(
//                                   "Cal",
//                                   style: TextStyle(
//                                     fontSize: 14,
//                                     color: AppColors.grey,
//                                   ),
//                                 ),
//                                 const Text(
//                                   "1960",
//                                   style: TextStyle(
//                                     fontSize: 28,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                                 Container(
//                                   padding: const EdgeInsets.symmetric(
//                                     horizontal: 8,
//                                     vertical: 2,
//                                   ),
//                                   decoration: BoxDecoration(
//                                     color: AppColors.green[100],
//                                     borderRadius: BorderRadius.circular(12),
//                                   ),
//                                   child: Text(
//                                     "98%",
//                                     style: TextStyle(
//                                       fontSize: 12,
//                                       fontWeight: FontWeight.bold,
//                                       color: AppColors.green[700],
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),

//                       // Right side - daily norm
//                       Expanded(
//                         child: Column(
//                           children: [
//                             const Text(
//                               "2000",
//                               style: TextStyle(
//                                 fontSize: 22,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             Text(
//                               "Daily Norm",
//                               style: TextStyle(
//                                 fontSize: 14,
//                                 color: AppColors.grey600,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   );
//                 },
//               ),

//               const SizedBox(height: 30),

//               // Nutrient progress indicators
//               Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 16),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                   children: [
//                     _buildNutrientProgress(
//                       "Proteins",
//                       0.92,
//                       "250/253",
//                       AppColors.green[300]!,
//                       _animationController,
//                     ),
//                     _buildNutrientProgress(
//                       "Carbs",
//                       1.01,
//                       "314/253",
//                       AppColors.green[300]!,
//                       _animationController,
//                     ),
//                     _buildNutrientProgress(
//                       "Fats",
//                       1.50,
//                       "445/253",
//                       AppColors.red[300]!,
//                       _animationController,
//                     ),
//                     _buildNutrientProgress(
//                       "Fiber",
//                       0.62,
//                       "130/253",
//                       Colors.amber[300]!,
//                       _animationController,
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildNutrientProgress(
//     String label,
//     double value,
//     String detail,
//     Color color,
//     Animation<double> animation,
//   ) {
//     return Expanded(
//       child: Column(
//         children: [
//           SizedBox(
//             width: 70,
//             height: 70,
//             child: AnimatedBuilder(
//               animation: animation,
//               builder: (context, child) {
//                 return CustomPaint(
//                   painter: CircularProgressPainter(
//                     progress: animation.value * value > 1
//                         ? 1
//                         : animation.value * value,
//                     color: color,
//                     backgroundColor: AppColors.grey300!,
//                     strokeWidth: 8,
//                   ),
//                 );
//               },
//             ),
//           ),
//           const SizedBox(height: 8),
//           Text(
//             "${(value * 100).toInt()}%",
//             style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
//           ),
//           const SizedBox(height: 4),
//           Text(
//             label,
//             style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
//           ),
//           const SizedBox(height: 4),
//           Text(detail, style: TextStyle(fontSize: 12, color: AppColors.grey600)),
//         ],
//       ),
//     );
//   }
// }

// class CircularProgressPainter extends CustomPainter {
//   final double progress;
//   final Color color;
//   final Color backgroundColor;
//   final double strokeWidth;

//   CircularProgressPainter({
//     required this.progress,
//     required this.color,
//     required this.backgroundColor,
//     required this.strokeWidth,
//   });

//   @override
//   void paint(Canvas canvas, Size size) {
//     final center = Offset(size.width / 2, size.height / 2);
//     final radius = math.min(size.width / 2, size.height / 2) - strokeWidth / 2;

//     // Draw background circle
//     final backgroundPaint = Paint()
//       ..color = backgroundColor
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = strokeWidth
//       ..strokeCap = StrokeCap.round;

//     canvas.drawCircle(center, radius, backgroundPaint);

//     // Draw progress arc
//     final progressPaint = Paint()
//       ..color = color
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = strokeWidth
//       ..strokeCap = StrokeCap.round;

//     canvas.drawArc(
//       Rect.fromCircle(center: center, radius: radius),
//       -math.pi / 2, // Start from top
//       progress * 2 * math.pi, // Full circle is 2*PI radians
//       false,
//       progressPaint,
//     );
//   }

//   @override
//   bool shouldRepaint(covariant CircularProgressPainter oldDelegate) {
//     return oldDelegate.progress != progress ||
//         oldDelegate.color != color ||
//         oldDelegate.backgroundColor != backgroundColor ||
//         oldDelegate.strokeWidth != strokeWidth;
//   }
// }

// class NutritionScoreGauge extends StatelessWidget {
//   final int score;
//   final String title;
//   final double size;

//   const NutritionScoreGauge({
//     Key? key,
//     required this.score,
//     this.title = 'Your Nutrition Score',
//     this.size = 400,
//   }) : super(key: key);

//   String getScoreLabel(int score) {
//     if (score < 50) return 'Poor!';
//     if (score < 70) return 'Fair!';
//     if (score < 85) return 'Good!';
//     return 'Great!';
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: size,
//       height: size * 0.9,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Text(
//             title,
//             style: TextStyle(
//               fontSize: size * 0.06,
//               fontWeight: FontWeight.bold,
//               color: AppColors.black87,
//             ),
//           ),
//           SizedBox(height: size * 0.02),
//           Expanded(
//             child: SfRadialGauge(
//               enableLoadingAnimation: true,
//               animationDuration: 1500,
//               axes: [
//                 RadialAxis(
//                   minimum: 0,
//                   maximum: 100,
//                   startAngle: 180,
//                   endAngle: 0,
//                   showLabels: false,
//                   showTicks: false,
//                   showAxisLine: false,
//                   radiusFactor: 0.9,
//                   canScaleToFit: true,
//                   // Use individual ranges instead of axisLine for more control over segments
//                   pointers: [
//                     MarkerPointer(
//                       value: score.toDouble(),
//                       markerType: MarkerType.triangle,
//                       markerHeight: size * 0.06,
//                       markerWidth: size * 0.06,
//                       color: AppColors.black,
//                       markerOffset: -size * 0.01,
//                       enableAnimation: true,
//                       animationType: AnimationType.ease,
//                     ),
//                   ],
//                   ranges: [
//                     // Poor segment (red)
//                     GaugeRange(
//                       startValue: 0,
//                       endValue: 22, // Leave small gap between segments
//                       color: const Color(0xFFF87B7B),
//                       startWidth: size * 0.09,
//                       endWidth: size * 0.09,
//                       sizeUnit: GaugeSizeUnit.logicalPixel,
//                       rangeOffset: size * 0.02,
//                       label: 'POOR',
//                       labelStyle: GaugeTextStyle(
//                         fontSize: size * 0.035,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     // Fair segment (light blue)
//                     GaugeRange(
//                       startValue: 25,
//                       endValue: 47, // Leave small gap between segments
//                       color: const Color(0xFFADD8E6),
//                       startWidth: size * 0.09,
//                       endWidth: size * 0.09,
//                       sizeUnit: GaugeSizeUnit.logicalPixel,
//                       rangeOffset: size * 0.02,
//                       label: 'FAIR',
//                       labelStyle: GaugeTextStyle(
//                         fontSize: size * 0.035,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     // Good segment (light green)
//                     GaugeRange(
//                       startValue: 50,
//                       endValue: 72, // Leave small gap between segments
//                       color: const Color(0xFFB5E7B5),
//                       startWidth: size * 0.09,
//                       endWidth: size * 0.09,
//                       sizeUnit: GaugeSizeUnit.logicalPixel,
//                       rangeOffset: size * 0.02,
//                       label: 'GOOD',
//                       labelStyle: GaugeTextStyle(
//                         fontSize: size * 0.035,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     // Great segment (light yellow)
//                     GaugeRange(
//                       startValue: 75,
//                       endValue: 100,
//                       color: const Color(0xFFFFF2AB),
//                       startWidth: size * 0.09,
//                       endWidth: size * 0.09,
//                       sizeUnit: GaugeSizeUnit.logicalPixel,
//                       rangeOffset: size * 0.02,
//                       label: 'GREAT',
//                       labelStyle: GaugeTextStyle(
//                         fontSize: size * 0.035,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                   ],
//                   annotations: [
//                     GaugeAnnotation(
//                       widget: Container(
//                         decoration: BoxDecoration(
//                           color: AppColors.white,
//                           shape: BoxShape.circle,
//                           border: Border.all(
//                             color: AppColors.grey300,
//                             width: 1.0,
//                           ),
//                         ),
//                         padding: EdgeInsets.all(size * 0.04),
//                         child: Column(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Text(
//                               score.toString(),
//                               style: TextStyle(
//                                 fontSize: size * 0.15,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             Text(
//                               getScoreLabel(score),
//                               style: TextStyle(
//                                 fontSize: size * 0.07,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       angle: 90,
//                       positionFactor: 0.5,
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
