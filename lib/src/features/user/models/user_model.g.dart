// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      auth: AuthUser.fromJson(json['auth'] as Map<String, dynamic>),
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      languageCode: json['languageCode'] as String? ?? 'en',
      themeMode: $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
          ThemeMode.system,
      notificationPreferences:
          (json['notificationPreferences'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as bool),
              ) ??
              const {'pushNotifications': true, 'emailNotifications': true},
      socialLinks: (json['socialLinks'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      imageUrl: json['imageUrl'] as String?,
      role:
          (json['role'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isDisabled: json['isDisabled'] as bool? ?? false,
      platform: json['platform'] as String?,
      onboardingAnswers: json['onboardingAnswers'] as Map<String, dynamic>?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
      diamonds: (json['diamonds'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'auth': instance.auth,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'languageCode': instance.languageCode,
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'notificationPreferences': instance.notificationPreferences,
      'socialLinks': instance.socialLinks,
      'imageUrl': instance.imageUrl,
      'role': instance.role,
      'isDisabled': instance.isDisabled,
      'platform': instance.platform,
      'onboardingAnswers': instance.onboardingAnswers,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
      'diamonds': instance.diamonds,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.system: 'system',
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
};
