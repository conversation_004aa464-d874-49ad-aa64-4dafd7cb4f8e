// // lib/features/user/data/models/user.dart
// import 'package:flutter/material.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
// import 'package:vocadex/src/features/auth/model/auth_user.dart';

// part 'user.freezed.dart';
// part 'user.g.dart';

// @freezed
// class User with _$User {
//   const factory User({
//     required AuthUser auth,
//     required String firstName,
//     required String lastName,
//     @Default('en') String languageCode,
//     @Default(ThemeMode.system) ThemeMode themeMode,
//     @Default({
//       'pushNotifications': true,
//       'emailNotifications': true,
//     })
//     Map<String, bool> notificationPreferences,
//     @Default({}) Map<String, String> socialLinks,
//     DateTime? lastUpdated,
//   }) = _User;

//   factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
// }
