// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  AuthUser get auth => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get languageCode => throw _privateConstructorUsedError;
  ThemeMode get themeMode => throw _privateConstructorUsedError;
  Map<String, bool> get notificationPreferences =>
      throw _privateConstructorUsedError;
  Map<String, String> get socialLinks => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  List<String> get role => throw _privateConstructorUsedError;
  bool get isDisabled => throw _privateConstructorUsedError;
  String? get platform => throw _privateConstructorUsedError;
  Map<String, dynamic>? get onboardingAnswers =>
      throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;
  int? get diamonds => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {AuthUser auth,
      String firstName,
      String lastName,
      String languageCode,
      ThemeMode themeMode,
      Map<String, bool> notificationPreferences,
      Map<String, String> socialLinks,
      String? imageUrl,
      List<String> role,
      bool isDisabled,
      String? platform,
      Map<String, dynamic>? onboardingAnswers,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? lastUpdated,
      int? diamonds});

  $AuthUserCopyWith<$Res> get auth;
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? languageCode = null,
    Object? themeMode = null,
    Object? notificationPreferences = null,
    Object? socialLinks = null,
    Object? imageUrl = freezed,
    Object? role = null,
    Object? isDisabled = null,
    Object? platform = freezed,
    Object? onboardingAnswers = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? lastUpdated = freezed,
    Object? diamonds = freezed,
  }) {
    return _then(_value.copyWith(
      auth: null == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as AuthUser,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as ThemeMode,
      notificationPreferences: null == notificationPreferences
          ? _value.notificationPreferences
          : notificationPreferences // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      socialLinks: null == socialLinks
          ? _value.socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isDisabled: null == isDisabled
          ? _value.isDisabled
          : isDisabled // ignore: cast_nullable_to_non_nullable
              as bool,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingAnswers: freezed == onboardingAnswers
          ? _value.onboardingAnswers
          : onboardingAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      diamonds: freezed == diamonds
          ? _value.diamonds
          : diamonds // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthUserCopyWith<$Res> get auth {
    return $AuthUserCopyWith<$Res>(_value.auth, (value) {
      return _then(_value.copyWith(auth: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AuthUser auth,
      String firstName,
      String lastName,
      String languageCode,
      ThemeMode themeMode,
      Map<String, bool> notificationPreferences,
      Map<String, String> socialLinks,
      String? imageUrl,
      List<String> role,
      bool isDisabled,
      String? platform,
      Map<String, dynamic>? onboardingAnswers,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? lastUpdated,
      int? diamonds});

  @override
  $AuthUserCopyWith<$Res> get auth;
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? auth = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? languageCode = null,
    Object? themeMode = null,
    Object? notificationPreferences = null,
    Object? socialLinks = null,
    Object? imageUrl = freezed,
    Object? role = null,
    Object? isDisabled = null,
    Object? platform = freezed,
    Object? onboardingAnswers = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? lastUpdated = freezed,
    Object? diamonds = freezed,
  }) {
    return _then(_$UserModelImpl(
      auth: null == auth
          ? _value.auth
          : auth // ignore: cast_nullable_to_non_nullable
              as AuthUser,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as ThemeMode,
      notificationPreferences: null == notificationPreferences
          ? _value._notificationPreferences
          : notificationPreferences // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      socialLinks: null == socialLinks
          ? _value._socialLinks
          : socialLinks // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value._role
          : role // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isDisabled: null == isDisabled
          ? _value.isDisabled
          : isDisabled // ignore: cast_nullable_to_non_nullable
              as bool,
      platform: freezed == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingAnswers: freezed == onboardingAnswers
          ? _value._onboardingAnswers
          : onboardingAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      diamonds: freezed == diamonds
          ? _value.diamonds
          : diamonds // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl implements _UserModel {
  const _$UserModelImpl(
      {required this.auth,
      required this.firstName,
      required this.lastName,
      this.languageCode = 'en',
      this.themeMode = ThemeMode.system,
      final Map<String, bool> notificationPreferences = const {
        'pushNotifications': true,
        'emailNotifications': true
      },
      final Map<String, String> socialLinks = const {},
      this.imageUrl,
      final List<String> role = const [],
      this.isDisabled = false,
      this.platform,
      final Map<String, dynamic>? onboardingAnswers,
      this.createdAt,
      this.updatedAt,
      this.lastUpdated,
      this.diamonds})
      : _notificationPreferences = notificationPreferences,
        _socialLinks = socialLinks,
        _role = role,
        _onboardingAnswers = onboardingAnswers;

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  final AuthUser auth;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  @JsonKey()
  final String languageCode;
  @override
  @JsonKey()
  final ThemeMode themeMode;
  final Map<String, bool> _notificationPreferences;
  @override
  @JsonKey()
  Map<String, bool> get notificationPreferences {
    if (_notificationPreferences is EqualUnmodifiableMapView)
      return _notificationPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_notificationPreferences);
  }

  final Map<String, String> _socialLinks;
  @override
  @JsonKey()
  Map<String, String> get socialLinks {
    if (_socialLinks is EqualUnmodifiableMapView) return _socialLinks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_socialLinks);
  }

  @override
  final String? imageUrl;
  final List<String> _role;
  @override
  @JsonKey()
  List<String> get role {
    if (_role is EqualUnmodifiableListView) return _role;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_role);
  }

  @override
  @JsonKey()
  final bool isDisabled;
  @override
  final String? platform;
  final Map<String, dynamic>? _onboardingAnswers;
  @override
  Map<String, dynamic>? get onboardingAnswers {
    final value = _onboardingAnswers;
    if (value == null) return null;
    if (_onboardingAnswers is EqualUnmodifiableMapView)
      return _onboardingAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? lastUpdated;
  @override
  final int? diamonds;

  @override
  String toString() {
    return 'UserModel(auth: $auth, firstName: $firstName, lastName: $lastName, languageCode: $languageCode, themeMode: $themeMode, notificationPreferences: $notificationPreferences, socialLinks: $socialLinks, imageUrl: $imageUrl, role: $role, isDisabled: $isDisabled, platform: $platform, onboardingAnswers: $onboardingAnswers, createdAt: $createdAt, updatedAt: $updatedAt, lastUpdated: $lastUpdated, diamonds: $diamonds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.auth, auth) || other.auth == auth) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.languageCode, languageCode) ||
                other.languageCode == languageCode) &&
            (identical(other.themeMode, themeMode) ||
                other.themeMode == themeMode) &&
            const DeepCollectionEquality().equals(
                other._notificationPreferences, _notificationPreferences) &&
            const DeepCollectionEquality()
                .equals(other._socialLinks, _socialLinks) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._role, _role) &&
            (identical(other.isDisabled, isDisabled) ||
                other.isDisabled == isDisabled) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            const DeepCollectionEquality()
                .equals(other._onboardingAnswers, _onboardingAnswers) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.diamonds, diamonds) ||
                other.diamonds == diamonds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      auth,
      firstName,
      lastName,
      languageCode,
      themeMode,
      const DeepCollectionEquality().hash(_notificationPreferences),
      const DeepCollectionEquality().hash(_socialLinks),
      imageUrl,
      const DeepCollectionEquality().hash(_role),
      isDisabled,
      platform,
      const DeepCollectionEquality().hash(_onboardingAnswers),
      createdAt,
      updatedAt,
      lastUpdated,
      diamonds);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel implements UserModel {
  const factory _UserModel(
      {required final AuthUser auth,
      required final String firstName,
      required final String lastName,
      final String languageCode,
      final ThemeMode themeMode,
      final Map<String, bool> notificationPreferences,
      final Map<String, String> socialLinks,
      final String? imageUrl,
      final List<String> role,
      final bool isDisabled,
      final String? platform,
      final Map<String, dynamic>? onboardingAnswers,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final DateTime? lastUpdated,
      final int? diamonds}) = _$UserModelImpl;

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  AuthUser get auth;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get languageCode;
  @override
  ThemeMode get themeMode;
  @override
  Map<String, bool> get notificationPreferences;
  @override
  Map<String, String> get socialLinks;
  @override
  String? get imageUrl;
  @override
  List<String> get role;
  @override
  bool get isDisabled;
  @override
  String? get platform;
  @override
  Map<String, dynamic>? get onboardingAnswers;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get lastUpdated;
  @override
  int? get diamonds;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
