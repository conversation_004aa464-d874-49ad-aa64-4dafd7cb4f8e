// lib/src/features/user/models/user_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vocadex/src/features/auth/model/auth_user.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required AuthUser auth,
    required String firstName,
    required String lastName,
    @Default('en') String languageCode,
    @Default(ThemeMode.system) ThemeMode themeMode,
    @Default({
      'pushNotifications': true,
      'emailNotifications': true,
    })
    Map<String, bool> notificationPreferences,
    @Default({}) Map<String, String> socialLinks,
    String? imageUrl,
    @Default([]) List<String> role,
    @Default(false) bool isDisabled,
    String? platform,
    Map<String, dynamic>? onboardingAnswers,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastUpdated,
    int? diamonds,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// Factory constructor to create a [UserModel] from a Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>?;

    if (data == null) {
      throw Exception('Document data is null');
    }

    // Extract auth user data
    final authData = data['auth'] as Map<String, dynamic>? ??
        {
          'uid': doc.id,
          'email': data['email'] ?? '',
        };

    // Create the user model
    return UserModel(
      auth: AuthUser.fromJson(authData),
      firstName:
          data['firstName'] ?? data['name']?.toString().split(' ').first ?? '',
      lastName:
          data['lastName'] ?? data['name']?.toString().split(' ').last ?? '',
      languageCode: data['languageCode'] ?? 'en',
      themeMode: _parseThemeMode(data['themeMode']),
      notificationPreferences:
          _parseNotificationPreferences(data['notificationPreferences']),
      socialLinks: _parseSocialLinks(data['socialLinks']),
      imageUrl: data['image_url'] ?? data['imageUrl'],
      role: _parseRole(data['role']),
      isDisabled: data['disabled'] ?? false,
      platform: data['platform'],
      onboardingAnswers: data['onboarding_answers'] is Map
          ? Map<String, dynamic>.from(data['onboarding_answers'])
          : null,
      createdAt: data['created_at'] != null
          ? (data['created_at'] as Timestamp).toDate()
          : null,
      updatedAt: data['updated_at'] != null
          ? (data['updated_at'] as Timestamp).toDate()
          : null,
      lastUpdated: data['lastUpdated'] != null
          ? (data['lastUpdated'] as Timestamp).toDate()
          : null,
      diamonds: data['diamonds'] as int?,
    );
  }

  /// Converts a [UserModel] to a Firestore-compatible map
  static Map<String, dynamic> toFirestore(UserModel user) {
    return {
      'auth': user.auth.toJson(),
      'firstName': user.firstName,
      'lastName': user.lastName,
      'name':
          '${user.firstName} ${user.lastName}', // For backward compatibility

      'languageCode': user.languageCode,
      'themeMode': user.themeMode.toString().split('.').last,
      'notificationPreferences': user.notificationPreferences,
      'socialLinks': user.socialLinks,
      'image_url': user.imageUrl,
      'role': user.role,
      'disabled': user.isDisabled,
      'platform': user.platform,
      'onboarding_answers': user.onboardingAnswers,
      'created_at':
          user.createdAt != null ? Timestamp.fromDate(user.createdAt!) : null,
      'updated_at':
          user.updatedAt != null ? Timestamp.fromDate(user.updatedAt!) : null,
      'lastUpdated': user.lastUpdated != null
          ? Timestamp.fromDate(user.lastUpdated!)
          : FieldValue.serverTimestamp(),
      'diamonds': user.diamonds,
    };
  }

  // Helper method to parse ThemeMode from string or null
  static ThemeMode _parseThemeMode(dynamic value) {
    if (value == null) return ThemeMode.system;

    if (value is String) {
      switch (value) {
        case 'light':
          return ThemeMode.light;
        case 'dark':
          return ThemeMode.dark;
        default:
          return ThemeMode.system;
      }
    }

    return ThemeMode.system;
  }

  // Helper method to parse notification preferences
  static Map<String, bool> _parseNotificationPreferences(dynamic value) {
    if (value == null) {
      return {
        'pushNotifications': true,
        'emailNotifications': true,
      };
    }

    if (value is Map) {
      return Map<String, bool>.from(
          value.map((key, val) => MapEntry(key.toString(), val as bool)));
    }

    return {
      'pushNotifications': true,
      'emailNotifications': true,
    };
  }

  // Helper method to parse social links
  static Map<String, String> _parseSocialLinks(dynamic value) {
    if (value == null) {
      return {};
    }

    if (value is Map) {
      return Map<String, String>.from(
          value.map((key, val) => MapEntry(key.toString(), val.toString())));
    }

    return {};
  }

  // Helper method to parse role
  static List<String> _parseRole(dynamic value) {
    if (value == null) {
      return [];
    }

    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }

    return [];
  }
}

// @freezed
// class UserModel with _$UserModel {
//   const factory User({
//     required AuthUser auth,
//     required String firstName,
//     required String lastName,
//     required bool isAnonymous,
//     @Default('en') String languageCode,
//     @Default(ThemeMode.system) ThemeMode themeMode,
//     @Default({
//       'pushNotifications': true,
//       'emailNotifications': true,
//     })
//     Map<String, bool> notificationPreferences,
//     @Default({}) Map<String, String> socialLinks,
//     String? imageUrl,
//     @Default([]) List<String> role,
//     @Default(false) bool isDisabled,
//     String? platform,
//     Map<String, dynamic>? onboardingAnswers,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//     DateTime? lastUpdated,
//   }) = _UserModel;

//   factory UserModel.fromJson(Map<String, dynamic> json) =>
//       _$UserModelFromJson(json);

//   /// Factory constructor to create a [UserModel] from a Firestore document
//   factory UserModel.fromFirestore(DocumentSnapshot doc) {
//     final data = doc.data() as Map<String, dynamic>?;

//     if (data == null) {
//       throw Exception('Document data is null');
//     }

//     // Extract auth user data
//     final authData = data['auth'] as Map<String, dynamic>? ??
//         {
//           'uid': doc.id,
//           'email': data['email'] ?? '',
//         };

//     // Create the user model
//     return UserModel(
//       auth: AuthUser.fromJson(authData),
//       firstName:
//           data['firstName'] ?? data['name']?.toString().split(' ').first ?? '',
//       lastName:
//           data['lastName'] ?? data['name']?.toString().split(' ').last ?? '',
//       isAnonymous: data['is_anonymous'] ?? false,
//       languageCode: data['languageCode'] ?? 'en',
//       themeMode: _parseThemeMode(data['themeMode']),
//       notificationPreferences:
//           _parseNotificationPreferences(data['notificationPreferences']),
//       socialLinks: _parseSocialLinks(data['socialLinks']),
//       imageUrl: data['image_url'] ?? data['imageUrl'],
//       role: _parseRole(data['role']),
//       isDisabled: data['disabled'] ?? false,
//       platform: data['platform'],
//       onboardingAnswers: data['onboarding_answers'] is Map
//           ? Map<String, dynamic>.from(data['onboarding_answers'])
//           : null,
//       createdAt: data['created_at'] != null
//           ? (data['created_at'] as Timestamp).toDate()
//           : null,
//       updatedAt: data['updated_at'] != null
//           ? (data['updated_at'] as Timestamp).toDate()
//           : null,
//       lastUpdated: data['lastUpdated'] != null
//           ? (data['lastUpdated'] as Timestamp).toDate()
//           : null,
//     );
//   }

//   /// Converts a [UserModel] to a Firestore-compatible map
//   static Map<String, dynamic> toFirestore(UserModel user) {
//     return {
//       'auth': user.auth.toJson(),
//       'firstName': user.firstName,
//       'lastName': user.lastName,
//       'name':
//           '${user.firstName} ${user.lastName}', // For backward compatibility
//       'is_anonymous': user.isAnonymous,
//       'languageCode': user.languageCode,
//       'themeMode': user.themeMode.toString().split('.').last,
//       'notificationPreferences': user.notificationPreferences,
//       'socialLinks': user.socialLinks,
//       'image_url': user.imageUrl,
//       'role': user.role,
//       'disabled': user.isDisabled,
//       'platform': user.platform,
//       'onboarding_answers': user.onboardingAnswers,
//       'created_at':
//           user.createdAt != null ? Timestamp.fromDate(user.createdAt!) : null,
//       'updated_at':
//           user.updatedAt != null ? Timestamp.fromDate(user.updatedAt!) : null,
//       'lastUpdated': user.lastUpdated != null
//           ? Timestamp.fromDate(user.lastUpdated!)
//           : FieldValue.serverTimestamp(),
//     };
//   }

//   // Helper method to parse ThemeMode from string or null
//   static ThemeMode _parseThemeMode(dynamic value) {
//     if (value == null) return ThemeMode.system;

//     if (value is String) {
//       switch (value) {
//         case 'light':
//           return ThemeMode.light;
//         case 'dark':
//           return ThemeMode.dark;
//         default:
//           return ThemeMode.system;
//       }
//     }

//     return ThemeMode.system;
//   }

//   // Helper method to parse notification preferences
//   static Map<String, bool> _parseNotificationPreferences(dynamic value) {
//     if (value == null) {
//       return {
//         'pushNotifications': true,
//         'emailNotifications': true,
//       };
//     }

//     if (value is Map) {
//       return Map<String, bool>.from(
//           value.map((key, val) => MapEntry(key.toString(), val as bool)));
//     }

//     return {
//       'pushNotifications': true,
//       'emailNotifications': true,
//     };
//   }

//   // Helper method to parse social links
//   static Map<String, String> _parseSocialLinks(dynamic value) {
//     if (value == null) {
//       return {};
//     }

//     if (value is Map) {
//       return Map<String, String>.from(
//           value.map((key, val) => MapEntry(key.toString(), val.toString())));
//     }

//     return {};
//   }

//   // Helper method to parse role
//   static List<String> _parseRole(dynamic value) {
//     if (value == null) {
//       return [];
//     }

//     if (value is List) {
//       return value.map((item) => item.toString()).toList();
//     }

//     return [];
//   }
// }
