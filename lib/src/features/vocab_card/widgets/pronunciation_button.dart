import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A button that plays pronunciation audio for a vocabulary word
class PronunciationButton extends ConsumerStatefulWidget {
  /// The word to pronounce
  final String word;

  /// Optional audio URL for the pronunciation
  final String? audioUrl;

  /// Size of the button
  final double size;

  /// Constructor
  const PronunciationButton({
    super.key,
    required this.word,
    this.audioUrl,
    this.size = 20,
  });

  @override
  ConsumerState<PronunciationButton> createState() =>
      _PronunciationButtonState();
}

class _PronunciationButtonState extends ConsumerState<PronunciationButton> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  /// Play pronunciation audio
  Future<void> _playPronunciation() async {
    if (_isPlaying) {
      await _audioPlayer.stop();
      setState(() {
        _isPlaying = false;
      });
      return;
    }

    setState(() {
      _isPlaying = true;
    });

    try {
      // If we have an audio URL, use it
      if (widget.audioUrl != null && widget.audioUrl!.isNotEmpty) {
        await _audioPlayer.play(UrlSource(widget.audioUrl!));
      } else {
        // Otherwise use Google Translate TTS as a fallback
        final url =
            'https://translate.google.com/translate_tts?ie=UTF-8&q=${Uri.encodeComponent(widget.word)}&tl=en-GB&client=tw-ob';
        await _audioPlayer.play(UrlSource(url));
      }

      // Listen for completion
      _audioPlayer.onPlayerComplete.listen((_) {
        if (mounted) {
          setState(() {
            _isPlaying = false;
          });
        }
      });
    } catch (e) {
      debugPrint('Error playing pronunciation: $e');
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _playPronunciation,
      borderRadius: BorderRadius.circular(12),
      child: Icon(
        _isPlaying ? Icons.volume_off_rounded : Icons.volume_up_rounded,
        color: AppColors.black,
        size: widget.size,
      ),
    );
  }
}

/// Provider for a shared audio player instance
final sharedAudioPlayerProvider = Provider<AudioPlayer>((ref) {
  final player = AudioPlayer();
  ref.onDispose(() => player.dispose());
  return player;
});
