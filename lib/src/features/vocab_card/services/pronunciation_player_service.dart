import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Service to play pronunciation audio for vocabulary words
class PronunciationPlayerService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  /// Play pronunciation audio from a URL
  Future<void> playPronunciation(String? audioUrl) async {
    if (audioUrl == null || audioUrl.isEmpty) {
      debugPrint('No audio URL provided for pronunciation');
      return;
    }
    
    try {
      // Set the source to the provided URL
      await _audioPlayer.play(UrlSource(audioUrl));
    } catch (e) {
      debugPrint('Error playing pronunciation audio: $e');
    }
  }
  
  /// Play pronunciation using text-to-speech
  /// This is a fallback method when no audio URL is available
  Future<void> playTextToSpeech(String word, {String language = 'en-GB'}) async {
    try {
      // Use Google Translate TTS API as a fallback
      final url = 'https://translate.google.com/translate_tts?ie=UTF-8&q=${Uri.encodeComponent(word)}&tl=$language&client=tw-ob';
      await _audioPlayer.play(UrlSource(url));
    } catch (e) {
      debugPrint('Error playing text-to-speech: $e');
    }
  }
  
  /// Stop any currently playing audio
  Future<void> stop() async {
    await _audioPlayer.stop();
  }
  
  /// Dispose the audio player
  void dispose() {
    _audioPlayer.dispose();
  }
}

/// Provider for the pronunciation player service
final pronunciationPlayerProvider = Provider<PronunciationPlayerService>((ref) {
  final service = PronunciationPlayerService();
  
  // Dispose the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});
