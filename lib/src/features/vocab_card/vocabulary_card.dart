import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/widgets/pronunciation_button.dart';
import 'package:vocadex/src/features/vocab_deck/utils/level_utils.dart';

class VocabularyCard extends ConsumerWidget {
  /// The vocabulary card to display
  final VocabCard card;

  /// Whether the card is flippable (interactive)
  final bool isFlippable;

  /// Whether to show action buttons
  final bool showActions;

  /// Callback when save is pressed (if showing actions)
  final VoidCallback? onSave;

  /// Callback when cancel is pressed (if showing actions)
  final VoidCallback? onCancel;

  /// Flag indicating if save operation is in progress
  final bool isSaving;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Constructor
  const VocabularyCard({
    super.key,
    required this.card,
    this.isFlippable = false,
    this.showActions = false,
    this.onSave,
    this.onCancel,
    this.isSaving = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Card content
        GestureDetector(
          onTap: onTap,
          child: _buildCard(context, ref),
        ),

        // Action buttons
        if (showActions) ...[
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Cancel button
                TextButton.icon(
                  onPressed: isSaving ? null : (onCancel ?? () {}),
                  icon: const Icon(Icons.close),
                  label: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),

                // Save button
                ElevatedButton.icon(
                  onPressed: isSaving ? null : (onSave ?? () {}),
                  icon: isSaving
                      ? const SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(AppColors.white),
                          ),
                        )
                      : Icon(
                          Icons.save,
                          color: AppColors.white,
                          fill: 2.0,
                        ),
                  label: Text(isSaving ? 'Saving...' : 'Save'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCard(BuildContext context, WidgetRef ref) {
    final levelColor = LevelUtils.getLevelColor(card.level);

    // Calculate mastery percentage for progress bar
    final double masteryPercentage = card.masteryLevel / 10.0;

    // Check if this is a multiword
    final bool isMultiWord = card.hasMultipleMeanings;

    return SizedBox(
      width: double.infinity,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: levelColor, width: 12),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.2),
              blurRadius: 2,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Upper part with orange background
              Container(
                color: levelColor,
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Frequency bar (signal style)
                    _FrequencyBar(filledBars: _parseFrequency(card.frequency)),
                    const SizedBox(height: 12),
                    // Word in large font (centered)
                    Center(
                      child: Text(
                        card.word.toLowerCase(),
                        style: const TextStyle(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: AppColors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Pronunciation with speaker icon (centered)
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          PronunciationButton(
                            word: card.word,
                            audioUrl: card.audioUrl,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            card.pronunciation,
                            style: const TextStyle(
                              fontSize: 24,
                              color: AppColors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          // Speaker icon button using the PronunciationButton widget
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Content area with light gray background
              Container(
                color: AppColors.white,
                child: Stack(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Part of speech and multiword indicator
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Part of speech
                              // if (card.type.isNotEmpty)
                              //   Text(
                              //     card.type[0], // This would be dynamic in real app
                              //     style: TextStyle(
                              //       fontSize: 18,
                              //       fontWeight: FontWeight.bold,
                              //       color: AppColors.grey[800],
                              //     ),
                              //   ),
                              if (card.type.isNotEmpty)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: AppColors.grey),
                                  ),
                                  child: Text(
                                    card.type.first.toLowerCase(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: AppColors.getTextColor(
                                          Theme.of(context).brightness),
                                    ),
                                  ),
                                ),
                              // Multiword indicator
                              if (isMultiWord)
                                Text(
                                  "multiword",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.grey,
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // Definition section
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Vertical definition label
                              Container(
                                padding:
                                    const EdgeInsets.only(top: 12, left: 8),
                                width: 34,
                                child: const RotatedBox(
                                  quarterTurns: -1,
                                  child: Text(
                                    "definition",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),

                              // Orange vertical bar
                              Container(
                                width: 6,
                                height: 80, // Will adjust with content
                                color: levelColor,
                                //color: AppColors.orange,
                              ),

                              const SizedBox(width: 12),

                              // Definition text
                              Expanded(
                                child: Padding(
                                  padding:
                                      const EdgeInsets.only(right: 16, top: 4),
                                  child: Text(
                                    card.definition,
                                    style: const TextStyle(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Example section
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Vertical example label
                              Container(
                                padding:
                                    const EdgeInsets.only(top: 12, left: 8),
                                width: 34,
                                child: const RotatedBox(
                                  quarterTurns: -1,
                                  child: Text(
                                    "example",
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),

                              // Orange vertical bar
                              Container(
                                width: 6,
                                height: 80, // Will adjust with content
                                color: levelColor,
                              ),

                              const SizedBox(width: 12),

                              // Example text
                              Expanded(
                                child: Padding(
                                  padding:
                                      const EdgeInsets.only(right: 16, top: 4),
                                  child: Text(
                                    card.examples.isNotEmpty
                                        ? card.examples.first
                                        : "No examples available",
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Mastery status and progress bar
                        _buildDynamicMasterySection(card, context)
                      ],
                    ),

                    // Level indicator (A1, B2, etc.)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: levelColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                          ),
                        ),
                        child: Text(
                          card.level,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDynamicMasterySection(VocabCard card, BuildContext context) {
    // Get mastery status
    final masteryStatus = card.getMasteryStatus();
    final masteryText = card.getMasteryStatusText().toLowerCase();
    final masteryPercentage = card.getMasteryPercentage() / 100;
    final masteryColor = card.getMasteryStatusColor();

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Mastery status text
          Row(
            children: [
              Text(
                "lvl${card.masteryLevel} - $masteryText",
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                _getMasteryIcon(masteryStatus),
                color: masteryColor,
                size: 20,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Dynamic progress bar
          Padding(
            padding: const EdgeInsets.only(right: 60.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                height: 16,
                width: double.infinity,
                color: AppColors.grey, // Updated from grey300
                child: Row(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width *
                          0.7 *
                          masteryPercentage,
                      height: 16,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            masteryColor.withAlpha(178),masteryColor,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Progress counter below the bar
          if (masteryStatus != MasteryStatus.mastered)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                "Progress: ${card.correctAnswers}/${masteryStatus == MasteryStatus.wild ? 5 : 9}",
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.grey, // Updated from grey600
                ),
              ),
            ),
        ],
      ),
    );
  }

// Helper method to get appropriate icon based on mastery status
  IconData _getMasteryIcon(MasteryStatus status) {
    switch (status) {
      case MasteryStatus.wild:
        return Icons.pets;
      case MasteryStatus.tamed:
        return Icons.lightbulb;
      case MasteryStatus.mastered:
        return Icons.military_tech;
    }
  }

  int _parseFrequency(dynamic freq) {
    if (freq is int) return freq.clamp(1, 5);
    if (freq is String) return int.tryParse(freq)?.clamp(1, 5) ?? 3;
    return 3;
  }
}

class _FrequencyBar extends StatelessWidget {
  final int filledBars; // 1 to 5
  const _FrequencyBar({required this.filledBars});

  @override
  Widget build(BuildContext context) {
    // Bar heights for signal style
    const barHeights = [12.0, 18.0, 24.0, 30.0, 36.0];
    final color = AppColors.black;
    return SizedBox(
      height: barHeights.last,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(5, (i) {
          final isFilled = i < filledBars;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 1.5),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 6,
              height: barHeights[i],
              decoration: BoxDecoration(
                color: isFilled ? color : color.withAlpha(64),borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
