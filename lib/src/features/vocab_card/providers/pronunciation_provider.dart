import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/widgets/pronunciation_button.dart';

/// Provider for accessing the pronunciation functionality throughout the app
final pronunciationProvider = Provider<PronunciationService>((ref) {
  return PronunciationService();
});

/// Service to handle pronunciation-related functionality
class PronunciationService {
  /// Generate a URL for the pronunciation audio of a word
  /// This is used when creating new vocabulary cards
  String generatePronunciationUrl(String word) {
    // Use Google Translate TTS API as a simple solution
    // In a production app, this would be replaced with Firebase Storage URLs
    return 'https://translate.google.com/translate_tts?ie=UTF-8&q=${Uri.encodeComponent(word)}&tl=en-GB&client=tw-ob';
  }
  
  /// Get the pronunciation button widget for a vocabulary card
  PronunciationButton getPronunciationButton({
    required String word,
    String? audioUrl,
    double size = 20,
  }) {
    return PronunciationButton(
      word: word,
      audioUrl: audioUrl,
      size: size,
    );
  }
}
