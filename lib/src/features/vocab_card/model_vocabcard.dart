// VocabCard Model
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// Represents a vocabulary card with mastery tracking and additional features
class VocabCard {
  final String id;
  final String word;
  final String definition;
  final List<String> examples;
  final List<String> type;
  final String pronunciation;
  final String level;
  final String color;
  final int masteryLevel;
  final int correctAnswers;
  final int totalAnswers;
  final DateTime? lastReviewedAt;

  // New properties
  final int? frequency; // Word frequency: 1 (rare) to 5 (common)
  final List<Map<String, dynamic>>?
      multipleDefinitions; // For multiword support
  final bool hasMultipleMeanings; // Flag for multiword
  final String? audioUrl; // URL to the pronunciation audio file

  VocabCard({
    required this.id,
    required this.word,
    required this.definition,
    required this.examples,
    required this.type,
    required this.pronunciation,
    required this.level,
    required this.color,
    this.masteryLevel = 1,
    this.correctAnswers = 0,
    this.totalAnswers = 0,
    this.lastReviewedAt,
    this.frequency,
    this.multipleDefinitions,
    this.hasMultipleMeanings = false,
    this.audioUrl,
  });

  factory VocabCard.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Convert dynamic lists to List<String>
    List<String> convertToStringList(dynamic value) {
      if (value == null) return [];
      if (value is String) return [value];
      if (value is List) {
        return value.map((item) => item.toString()).toList();
      }
      return [];
    }

    // Process multiple definitions if present
    List<Map<String, dynamic>>? multipleDefinitions;
    if (data['multiple_definitions'] != null &&
        data['multiple_definitions'] is List) {
      final defList = data['multiple_definitions'] as List;
      multipleDefinitions = defList.map((def) {
        if (def is Map<String, dynamic>) {
          return def;
        } else {
          return <String, dynamic>{};
        }
      }).toList();
    }

    return VocabCard(
      id: doc.id,
      word: data['word'] ?? '',
      definition: data['definition'] ?? '',
      examples: convertToStringList(data['example']),
      type: convertToStringList(data['type']),
      pronunciation: data['pronunciation'] ?? '',
      level: data['level'] ?? 'A1',
      color: data['color'] ?? 'red',
      masteryLevel: (data['masteryLevel'] ?? 1) as int,
      correctAnswers: (data['correctAnswers'] ?? 0) as int,
      totalAnswers: (data['totalAnswers'] ?? 0) as int,
      lastReviewedAt: data['lastReviewedAt'] != null
          ? (data['lastReviewedAt'] as Timestamp).toDate()
          : null,
      frequency: data['frequency'] is int
          ? data['frequency'] as int
          : (data['frequency'] is String
              ? int.tryParse(data['frequency']) ?? 1
              : null),
      multipleDefinitions: multipleDefinitions,
      hasMultipleMeanings:
          multipleDefinitions != null && multipleDefinitions.isNotEmpty,
      audioUrl: data['audioUrl'] as String?,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'word': word,
      'definition': definition,
      'example': examples,
      'pronunciation': pronunciation,
      'level': level,
      'color': color,
      'type': type,
      'masteryLevel': masteryLevel,
      'correctAnswers': correctAnswers,
      'totalAnswers': totalAnswers,
      'lastReviewedAt':
          lastReviewedAt != null ? Timestamp.fromDate(lastReviewedAt!) : null,
      'frequency': frequency,
      'multiple_definitions': multipleDefinitions,
      'has_multiple_meanings': hasMultipleMeanings,
      'audioUrl': audioUrl,
    };
  }

  /// Get the current mastery status based on correct answers
  MasteryStatus getMasteryStatus() {
    if (correctAnswers >= 9) {
      return MasteryStatus.mastered;
    } else if (correctAnswers >= 5) {
      return MasteryStatus.tamed;
    } else {
      return MasteryStatus.wild;
    }
  }

  /// Get user-friendly description of mastery status
  String getMasteryStatusText() {
    switch (getMasteryStatus()) {
      case MasteryStatus.wild:
        return 'Wild';
      case MasteryStatus.tamed:
        return 'Tamed';
      case MasteryStatus.mastered:
        return 'Mastered';
    }
  }

  /// Calculate mastery percentage (0-100%)
  double getMasteryPercentage() {
    return (correctAnswers / 10) * 100;
  }

  /// Get color for the mastery status
  Color getMasteryStatusColor() {
    switch (getMasteryStatus()) {
      case MasteryStatus.wild:
        return const Color(0xFFEF4444); // Red
      case MasteryStatus.tamed:
        return const Color(0xFFF59E0B); // Amber
      case MasteryStatus.mastered:
        return const Color(0xFF10B981); // Green
    }
  }

  /// Create a copy of the card with updated properties
  VocabCard copyWith({
    String? id,
    String? word,
    String? definition,
    List<String>? examples,
    List<String>? type,
    String? pronunciation,
    String? level,
    String? color,
    int? masteryLevel,
    int? correctAnswers,
    int? totalAnswers,
    DateTime? lastReviewedAt,
    int? frequency,
    List<Map<String, dynamic>>? multipleDefinitions,
    bool? hasMultipleMeanings,
    String? audioUrl,
  }) {
    return VocabCard(
      id: id ?? this.id,
      word: word ?? this.word,
      definition: definition ?? this.definition,
      examples: examples ?? this.examples,
      type: type ?? this.type,
      pronunciation: pronunciation ?? this.pronunciation,
      level: level ?? this.level,
      color: color ?? this.color,
      masteryLevel: masteryLevel ?? this.masteryLevel,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      totalAnswers: totalAnswers ?? this.totalAnswers,
      lastReviewedAt: lastReviewedAt ?? this.lastReviewedAt,
      frequency: frequency ?? this.frequency,
      multipleDefinitions: multipleDefinitions ?? this.multipleDefinitions,
      hasMultipleMeanings: hasMultipleMeanings ?? this.hasMultipleMeanings,
      audioUrl: audioUrl ?? this.audioUrl,
    );
  }

  // Global Vocabulary Utilities

  /// Normalize a word for global repository search (case-insensitive, trimmed)
  static String normalizeWord(String word) {
    return word.toLowerCase().trim();
  }

  /// Create a global repository document ID from a word
  static String createGlobalId(String word) {
    return normalizeWord(word);
  }

  /// Create a VocabCard suitable for global repository storage
  /// Sets user-specific fields to default values
  VocabCard toGlobalCard() {
    return copyWith(
      id: createGlobalId(word),
      masteryLevel: 1,
      correctAnswers: 0,
      totalAnswers: 0,
      lastReviewedAt: null,
    );
  }

  /// Create a VocabCard for user collection from a global card
  /// Preserves vocabulary data and sets user-specific defaults
  VocabCard toUserCard({String? userId}) {
    return copyWith(
      id: '', // Will be set by Firebase when saving
      masteryLevel: 1,
      correctAnswers: 0,
      totalAnswers: 0,
      lastReviewedAt: null,
    );
  }

  /// Check if this card matches a search word (case-insensitive exact match)
  bool matchesWord(String searchWord) {
    return normalizeWord(word) == normalizeWord(searchWord);
  }
}

/// Enum for card mastery statuses
enum MasteryStatus {
  wild, // 0-4 correct answers
  tamed, // 5-8 correct answers
  mastered // 9-10 correct answers
}
