// lib/src/features/vocab_quiz/services/quiz_content_generation_service.dart

import 'dart:convert';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/foundation.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/models/quiz_content_models.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Service for generating pre-generated quiz content using AI
class QuizContentGenerationService {
  final FirebaseVertexAI _vertexAI = FirebaseVertexAI.instance;

  /// Generate comprehensive quiz content for a vocabulary word
  /// Creates 10 different questions across various types and contexts
  Future<WordQuizContent?> generateQuizContent(VocabCard vocabCard) async {
    try {
      debugPrint('🎯 Generating quiz content for word: ${vocabCard.word}');

      final wordId = VocabCard.createGlobalId(vocabCard.word);
      final now = DateTime.now();

      // Generate different types of questions
      final fillInBlankQuestions =
          await _generateFillInBlankQuestions(vocabCard);
      final trueFalseQuestions = await _generateTrueFalseQuestions(vocabCard);
      final spellWordQuestions = await _generateSpellWordQuestions(vocabCard);

      final content = WordQuizContent(
        wordId: wordId,
        word: vocabCard.word,
        difficulty: vocabCard.level,
        createdAt: now,
        lastUpdated: now,
        contentVersion: 1,
        fillInBlankQuestions: fillInBlankQuestions,
        trueFalseQuestions: trueFalseQuestions,
        spellWordQuestions: spellWordQuestions,
      );

      debugPrint(
          '✅ Generated ${content.totalQuestions} quiz questions for word: ${vocabCard.word}');
      return content;
    } catch (e) {
      debugPrint(
          '❌ Error generating quiz content for word ${vocabCard.word}: $e');
      return null;
    }
  }

  /// Generate 4 varied fill-in-the-blank questions
  Future<List<FillInBlankContent>> _generateFillInBlankQuestions(
      VocabCard vocabCard) async {
    try {
      debugPrint(
          '📝 Generating fill-in-blank questions for: ${vocabCard.word}');

      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');

      final prompt = '''
Create 4 different fill-in-the-blank questions for the word "${vocabCard.word}".

Word details:
- Definition: ${vocabCard.definition}
- Part of speech: ${vocabCard.type.join(', ')}
- Level: ${vocabCard.level}
- Examples: ${vocabCard.examples.join('; ')}

Requirements:
1. Create 4 COMPLETELY DIFFERENT sentences showing natural usage of the word in context
2. Focus on how the word is actually used in real situations, not definitions
3. Each sentence should demonstrate different usage scenarios (formal, casual, academic, practical)
4. Replace the target word with "___" in each sentence
5. Provide 3 plausible wrong answers (distractors) for each question
6. Make sentences natural and conversational, showing the word in action

Examples of good formats:
- "The _____ was looking great after the renovation"
- "She gave a clear _____ of the process to the team"
- "His _____ helped everyone understand the concept"

Avoid definition-style sentences like "The _____ means..." or "A _____ is defined as..."

Return ONLY a JSON array with this exact structure:
[
  {
    "sentence": "The teacher provided a clear ___ of the concept",
    "correctAnswer": "explanation",
    "distractors": ["description", "definition", "illustration"],
    "context": "educational"
  }
]

Generate exactly 4 questions with varied contexts like: educational, business, casual, academic, practical, social, etc.
''';

      final response = await model.generateContent([Content.text(prompt)]);
      final text = response.text ?? '';

      // Extract JSON from response
      final jsonMatch = RegExp(r'\[[\s\S]*\]').firstMatch(text);
      if (jsonMatch == null) {
        debugPrint('❌ No valid JSON found in fill-in-blank response');
        return [];
      }

      final jsonData = jsonDecode(jsonMatch.group(0)!) as List<dynamic>;
      final questions = <FillInBlankContent>[];

      for (int i = 0; i < jsonData.length && i < 4; i++) {
        final item = jsonData[i] as Map<String, dynamic>;
        questions.add(FillInBlankContent(
          id: FirebaseService.getUID('quiz_content'),
          difficulty: vocabCard.level,
          createdAt: DateTime.now(),
          sentence: item['sentence'] ?? '',
          correctAnswer: item['correctAnswer'] ?? vocabCard.word,
          distractors: List<String>.from(item['distractors'] ?? []),
          context: item['context'] ?? 'general',
        ));
      }

      debugPrint('✅ Generated ${questions.length} fill-in-blank questions');
      return questions;
    } catch (e) {
      debugPrint('❌ Error generating fill-in-blank questions: $e');
      return [];
    }
  }

  /// Generate 3 varied true/false questions
  Future<List<TrueFalseContent>> _generateTrueFalseQuestions(
      VocabCard vocabCard) async {
    try {
      debugPrint('✅❌ Generating true/false questions for: ${vocabCard.word}');

      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');

      final prompt = '''
Create 3 different true/false statements about the word "${vocabCard.word}".

Word details:
- Definition: ${vocabCard.definition}
- Part of speech: ${vocabCard.type.join(', ')}
- Level: ${vocabCard.level}

Requirements:
1. Create 3 DIFFERENT types of direct statements:
   - 1 about the word's meaning/properties
   - 1 about the word's usage/context
   - 1 about the word's characteristics or applications
2. Write direct, natural statements without explicit definition format
3. Mix of true and false statements (not all true or all false)
4. Include clear explanations for each answer
5. Make statements challenging but fair

Examples of good formats:
- "An example illustrates a general principle" (not "The word 'example' means...")
- "Examples are commonly used in educational settings"
- "Examples help clarify abstract concepts"

Avoid explicit definition formats like:
- "The word 'example' means..."
- "An example is defined as..."
- "The definition of example is..."

Return ONLY a JSON array with this exact structure:
[
  {
    "statement": "An example illustrates a general principle",
    "isTrue": true,
    "explanation": "This is correct - examples demonstrate or illustrate concepts",
    "category": "meaning"
  }
]

Categories should be: "meaning", "usage", or "characteristics"
''';

      final response = await model.generateContent([Content.text(prompt)]);
      final text = response.text ?? '';

      // Extract JSON from response
      final jsonMatch = RegExp(r'\[[\s\S]*\]').firstMatch(text);
      if (jsonMatch == null) {
        debugPrint('❌ No valid JSON found in true/false response');
        return [];
      }

      final jsonData = jsonDecode(jsonMatch.group(0)!) as List<dynamic>;
      final questions = <TrueFalseContent>[];

      for (int i = 0; i < jsonData.length && i < 3; i++) {
        final item = jsonData[i] as Map<String, dynamic>;
        questions.add(TrueFalseContent(
          id: FirebaseService.getUID('quiz_content'),
          difficulty: vocabCard.level,
          createdAt: DateTime.now(),
          statement: item['statement'] ?? '',
          isTrue: item['isTrue'] ?? false,
          explanation: item['explanation'] ?? '',
          category: item['category'] ?? 'definition',
        ));
      }

      debugPrint('✅ Generated ${questions.length} true/false questions');
      return questions;
    } catch (e) {
      debugPrint('❌ Error generating true/false questions: $e');
      return [];
    }
  }

  /// Generate 3 varied spell word questions
  Future<List<SpellWordContent>> _generateSpellWordQuestions(
      VocabCard vocabCard) async {
    try {
      debugPrint('🔤 Generating spell word questions for: ${vocabCard.word}');

      final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');

      final prompt = '''
Create 3 different spelling prompts for the word "${vocabCard.word}".

Word details:
- Definition: ${vocabCard.definition}
- Part of speech: ${vocabCard.type.join(', ')}
- Level: ${vocabCard.level}

Requirements:
1. Create 3 DIFFERENT types of prompts using a hybrid approach:
   - 1 using the definition directly
   - 1 using contextual/usage clues
   - 1 using a descriptive phrase about what the word represents
2. Each prompt should clearly lead to the target word
3. Vary the prompt styles between direct definitions and contextual clues
4. Make prompts natural and clear

Examples of good formats:
- Definition-based: "A typical case that illustrates a general rule"
- Usage-based: "What you give when explaining a concept to someone"
- Descriptive: "Something that helps clarify or demonstrate an idea"

Avoid synonym-based prompts. Focus on definitions and usage contexts only.

Return ONLY a JSON array with this exact structure:
[
  {
    "prompt": "A typical case that illustrates a general rule or principle",
    "answer": "example",
    "promptType": "definition"
  }
]

PromptTypes should be: "definition", "usage", or "descriptive"
''';

      final response = await model.generateContent([Content.text(prompt)]);
      final text = response.text ?? '';

      // Extract JSON from response
      final jsonMatch = RegExp(r'\[[\s\S]*\]').firstMatch(text);
      if (jsonMatch == null) {
        debugPrint('❌ No valid JSON found in spell word response');
        return [];
      }

      final jsonData = jsonDecode(jsonMatch.group(0)!) as List<dynamic>;
      final questions = <SpellWordContent>[];

      for (int i = 0; i < jsonData.length && i < 3; i++) {
        final item = jsonData[i] as Map<String, dynamic>;
        questions.add(SpellWordContent(
          id: FirebaseService.getUID('quiz_content'),
          difficulty: vocabCard.level,
          createdAt: DateTime.now(),
          prompt: item['prompt'] ?? '',
          answer: item['answer'] ?? vocabCard.word,
          promptType: item['promptType'] ?? 'definition',
        ));
      }

      debugPrint('✅ Generated ${questions.length} spell word questions');
      return questions;
    } catch (e) {
      debugPrint('❌ Error generating spell word questions: $e');
      return [];
    }
  }

  /// Generate quiz content with retry logic for better reliability
  Future<WordQuizContent?> generateQuizContentWithRetry(
    VocabCard vocabCard, {
    int maxRetries = 2,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint(
            '🔄 Attempt $attempt to generate quiz content for: ${vocabCard.word}');

        final content = await generateQuizContent(vocabCard);

        if (content != null && content.totalQuestions >= 8) {
          // Success if we have at least 8 questions (allowing for some AI generation failures)
          return content;
        }

        if (attempt < maxRetries) {
          debugPrint('⚠️ Insufficient questions generated, retrying...');
          await Future.delayed(
              Duration(seconds: attempt)); // Brief delay before retry
        }
      } catch (e) {
        debugPrint('❌ Attempt $attempt failed: $e');
        if (attempt == maxRetries) rethrow;
      }
    }

    debugPrint(
        '❌ Failed to generate sufficient quiz content after $maxRetries attempts');
    return null;
  }
}
