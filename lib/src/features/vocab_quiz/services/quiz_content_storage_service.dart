// lib/src/features/vocab_quiz/services/quiz_content_storage_service.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:vocadex/src/features/vocab_quiz/models/quiz_content_models.dart';

/// Service for storing and retrieving pre-generated quiz content from Firestore
class QuizContentStorageService {
  static const String _collectionName = 'global_quiz_content';
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get reference to the global quiz content collection
  CollectionReference get _collection => _firestore.collection(_collectionName);

  /// Save pre-generated quiz content for a word
  Future<bool> saveQuizContent(WordQuizContent content) async {
    try {
      debugPrint('💾 Saving quiz content for word: ${content.word}');

      await _collection.doc(content.wordId).set(content.toFirestore());

      debugPrint('✅ Successfully saved quiz content for word: ${content.word}');
      return true;
    } catch (e) {
      debugPrint('❌ Error saving quiz content for word ${content.word}: $e');
      return false;
    }
  }

  /// Retrieve quiz content for a specific word
  Future<WordQuizContent?> getQuizContent(String wordId) async {
    try {
      debugPrint('📖 Fetching quiz content for word ID: $wordId');

      final doc = await _collection.doc(wordId).get();

      if (!doc.exists) {
        debugPrint('📭 No quiz content found for word ID: $wordId');
        return null;
      }

      final data = doc.data() as Map<String, dynamic>;
      final content = WordQuizContent.fromFirestore(wordId, data);

      debugPrint(
          '✅ Retrieved quiz content for word: ${content.word} (${content.totalQuestions} questions)');
      return content;
    } catch (e) {
      debugPrint('❌ Error fetching quiz content for word ID $wordId: $e');
      return null;
    }
  }

  /// Retrieve quiz content for multiple words
  Future<List<WordQuizContent>> getQuizContentForWords(
      List<String> wordIds) async {
    try {
      debugPrint('📚 Fetching quiz content for ${wordIds.length} words');

      if (wordIds.isEmpty) {
        return [];
      }

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      final List<WordQuizContent> allContent = [];

      for (int i = 0; i < wordIds.length; i += 10) {
        final batch = wordIds.skip(i).take(10).toList();
        final query =
            await _collection.where(FieldPath.documentId, whereIn: batch).get();

        for (final doc in query.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final content = WordQuizContent.fromFirestore(doc.id, data);
          allContent.add(content);
        }
      }

      debugPrint('✅ Retrieved quiz content for ${allContent.length} words');
      return allContent;
    } catch (e) {
      debugPrint('❌ Error fetching quiz content for multiple words: $e');
      return [];
    }
  }

  /// Check if quiz content exists for a word
  Future<bool> hasQuizContent(String wordId) async {
    try {
      final doc = await _collection.doc(wordId).get();
      return doc.exists;
    } catch (e) {
      debugPrint(
          '❌ Error checking quiz content existence for word ID $wordId: $e');
      return false;
    }
  }

  /// Update quiz content for a word
  Future<bool> updateQuizContent(WordQuizContent content) async {
    try {
      debugPrint('🔄 Updating quiz content for word: ${content.word}');

      final updatedContent = content.copyWith(
        lastUpdated: DateTime.now(),
        contentVersion: content.contentVersion + 1,
      );

      await _collection
          .doc(content.wordId)
          .update(updatedContent.toFirestore());

      debugPrint(
          '✅ Successfully updated quiz content for word: ${content.word}');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating quiz content for word ${content.word}: $e');
      return false;
    }
  }

  /// Delete quiz content for a word
  Future<bool> deleteQuizContent(String wordId) async {
    try {
      debugPrint('🗑️ Deleting quiz content for word ID: $wordId');

      await _collection.doc(wordId).delete();

      debugPrint('✅ Successfully deleted quiz content for word ID: $wordId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting quiz content for word ID $wordId: $e');
      return false;
    }
  }

  /// Get random quiz content for quiz generation
  /// Returns a mix of different question types from available content
  Future<List<QuizContent>> getRandomQuizContent({
    required List<String> wordIds,
    required int questionCount,
    Map<String, int> questionTypeDistribution = const {
      'fillInBlank': 4,
      'trueFalse': 3,
      'spellWord': 3,
    },
  }) async {
    try {
      debugPrint(
          '🎲 Getting random quiz content for ${wordIds.length} words, $questionCount questions');

      // Fetch all available content for the words
      final allContent = await getQuizContentForWords(wordIds);

      if (allContent.isEmpty) {
        debugPrint('📭 No quiz content available for provided words');
        throw Exception(
            'No pre-generated quiz content available for the selected words');
      }

      final List<QuizContent> selectedQuestions = [];

      // Collect all questions by type
      final List<FillInBlankContent> allFillInBlank = [];
      final List<TrueFalseContent> allTrueFalse = [];
      final List<SpellWordContent> allSpellWord = [];

      for (final content in allContent) {
        allFillInBlank.addAll(content.fillInBlankQuestions);
        allTrueFalse.addAll(content.trueFalseQuestions);
        allSpellWord.addAll(content.spellWordQuestions);
      }

      // Shuffle all question lists
      allFillInBlank.shuffle();
      allTrueFalse.shuffle();
      allSpellWord.shuffle();

      // Select questions according to distribution
      int fillInBlankCount = questionTypeDistribution['fillInBlank'] ?? 0;
      int trueFalseCount = questionTypeDistribution['trueFalse'] ?? 0;
      int spellWordCount = questionTypeDistribution['spellWord'] ?? 0;

      // Adjust counts if we don't have enough questions
      fillInBlankCount = fillInBlankCount.clamp(0, allFillInBlank.length);
      trueFalseCount = trueFalseCount.clamp(0, allTrueFalse.length);
      spellWordCount = spellWordCount.clamp(0, allSpellWord.length);

      // Add selected questions
      selectedQuestions.addAll(allFillInBlank.take(fillInBlankCount));
      selectedQuestions.addAll(allTrueFalse.take(trueFalseCount));
      selectedQuestions.addAll(allSpellWord.take(spellWordCount));

      // If we need more questions and have available content, fill from any type
      final remainingNeeded = questionCount - selectedQuestions.length;
      if (remainingNeeded > 0) {
        final List<QuizContent> remainingQuestions = [];

        // Add remaining questions from all types
        remainingQuestions.addAll(allFillInBlank.skip(fillInBlankCount));
        remainingQuestions.addAll(allTrueFalse.skip(trueFalseCount));
        remainingQuestions.addAll(allSpellWord.skip(spellWordCount));

        remainingQuestions.shuffle();
        selectedQuestions.addAll(remainingQuestions.take(remainingNeeded));
      }

      // Final shuffle of selected questions
      selectedQuestions.shuffle();

      debugPrint(
          '✅ Selected ${selectedQuestions.length} random quiz questions');
      return selectedQuestions.take(questionCount).toList();
    } catch (e) {
      debugPrint('❌ Error getting random quiz content: $e');
      return [];
    }
  }

  /// Get statistics about quiz content in the database
  Future<Map<String, dynamic>> getQuizContentStats() async {
    try {
      final snapshot = await _collection.get();

      int totalWords = snapshot.docs.length;
      int totalQuestions = 0;
      int fillInBlankCount = 0;
      int trueFalseCount = 0;
      int spellWordCount = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final content = WordQuizContent.fromFirestore(doc.id, data);

        totalQuestions += content.totalQuestions;
        fillInBlankCount += content.fillInBlankQuestions.length;
        trueFalseCount += content.trueFalseQuestions.length;
        spellWordCount += content.spellWordQuestions.length;
      }

      return {
        'totalWords': totalWords,
        'totalQuestions': totalQuestions,
        'fillInBlankCount': fillInBlankCount,
        'trueFalseCount': trueFalseCount,
        'spellWordCount': spellWordCount,
        'averageQuestionsPerWord':
            totalWords > 0 ? totalQuestions / totalWords : 0,
      };
    } catch (e) {
      debugPrint('❌ Error getting quiz content stats: $e');
      return {};
    }
  }

  /// Batch save multiple quiz contents (for bulk operations)
  Future<bool> batchSaveQuizContent(List<WordQuizContent> contents) async {
    try {
      debugPrint('📦 Batch saving quiz content for ${contents.length} words');

      final batch = _firestore.batch();

      for (final content in contents) {
        final docRef = _collection.doc(content.wordId);
        batch.set(docRef, content.toFirestore());
      }

      await batch.commit();

      debugPrint(
          '✅ Successfully batch saved quiz content for ${contents.length} words');
      return true;
    } catch (e) {
      debugPrint('❌ Error batch saving quiz content: $e');
      return false;
    }
  }
}
