import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/dashboard/widgets/buttons.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_creation_sheet.dart';
// Removed unused imports
import 'package:vocadex/src/services/quiz_service.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_mode_selector.dart';

/// Provider for the QuizService
final quizServiceProvider = Provider<QuizService>((ref) => QuizService());

/// Provider for fetching quiz statistics with auto-dispose to enable refreshing
final quizStatsProvider =
    FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final quizService = ref.watch(quizServiceProvider);
  return await quizService.getQuizStatistics();
});

/// Global function to refresh quiz stats anywhere in the app
void refreshQuizStats(ProviderContainer container) {
  container.refresh(quizStatsProvider);
}

class QuizAnalysisScreen extends ConsumerWidget {
  const QuizAnalysisScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statsAsync = ref.watch(quizStatsProvider);

    return GradientScaffold(
      appBar: AppBar(
        title: const Text('Train Your Deck'),
        backgroundColor: AppColors.transparent,
        elevation: 0,
        actions: [
          // Add refresh button to manually refresh stats
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(quizStatsProvider);
              showInfoToast(
                context,
                title: 'Refreshing',
                description: 'Updating quiz analysis...',
              );
            },
          ),
        ],
      ),
      body: statsAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) =>
            Center(child: Text('Error loading quiz analysis: $error')),
        data: (stats) {
          if (stats.isEmpty) {
            return const Center(
              child: Text('No quiz data available yet. Start a quiz!'),
            );
          }

          return _buildAnalysisContent(context, stats, ref);
        },
      ),
    );
  }

  Widget _buildAnalysisContent(
      BuildContext context, Map<String, dynamic> stats, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Train Your Deck button - moved to top
          _buildTrainButton(context, ref),

          const SizedBox(height: 24),

          // Progress chart
          _buildProgressChart(context, stats),

          const SizedBox(height: 24),

          // Performance by quiz type - moved higher up since it's analysis focused
          // _buildPerformanceByType(context, stats),

          const SizedBox(height: 24),

          // Recent quizzes
          _buildRecentQuizzes(context, stats),
        ],
      ),
    );
  }

  Widget _buildProgressChart(BuildContext context, Map<String, dynamic> stats) {
    final history = stats['quizHistory'] as List;

    if (history.isEmpty) {
      return const SizedBox.shrink(); // Don't show chart if no data
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Trend',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'Your quiz score progress over time',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.black.withValues(alpha: 0.6),
              ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.cardBackgroundLight,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withAlpha(26),blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: LineChart(
            LineChartData(
              gridData: FlGridData(show: false),
              titlesData: FlTitlesData(
                rightTitles:
                    AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles:
                    AxisTitles(sideTitles: SideTitles(showTitles: false)),
                bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                  showTitles: true,
                  getTitlesWidget: (value, meta) {
                    if (value < 0 || value >= history.length)
                      return const SizedBox();
                    final date = (history[value.toInt()]['date'] as DateTime);
                    return Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(DateFormat('MM/dd').format(date)),
                    );
                  },
                )),
              ),
              borderData: FlBorderData(show: false),
              lineBarsData: [
                LineChartBarData(
                  spots: List.generate(history.length, (index) {
                    // Handle both int and double values safely
                    final score = history[index]['score'];
                    final total = history[index]['total'];
                    final double scoreValue =
                        score is int ? score.toDouble() : score as double;
                    final double totalValue =
                        total is int ? total.toDouble() : total as double;

                    return FlSpot(index.toDouble(),
                        totalValue > 0 ? (scoreValue / totalValue * 100) : 0.0);
                  }),
                  isCurved: true,
                  color: AppColors.primaryLight,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: true),
                  belowBarData: BarAreaData(
                    show: true,
                    color: AppColors.primaryLight.withAlpha(51),),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Add insight below the chart
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.primaryLight.withAlpha(26),borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryLight.withAlpha(51),),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppColors.warningLight,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getChartInsight(stats),
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getChartInsight(Map<String, dynamic> stats) {
    final history = stats['quizHistory'] as List;
    if (history.length < 2) {
      return "Complete more quizzes to see your progress trend.";
    }

    // Calculate trend (improving, declining, stable)
    List<double> scores = [];
    for (final quiz in history) {
      final score = quiz['score'] as int;
      final total = quiz['total'] as int;
      if (total > 0) {
        scores.add((score / total) * 100);
      }
    }

    if (scores.isEmpty)
      return "Complete more quizzes to see your progress trend.";

    // Check if recent scores are higher than earlier scores
    double firstHalfAvg = 0;
    double secondHalfAvg = 0;

    if (scores.length >= 4) {
      // With 4+ scores, compare first half to second half
      int midPoint = scores.length ~/ 2;
      firstHalfAvg =
          scores.sublist(0, midPoint).reduce((a, b) => a + b) / midPoint;
      secondHalfAvg = scores.sublist(midPoint).reduce((a, b) => a + b) /
          (scores.length - midPoint);
    } else {
      // With 2-3 scores, compare first score to last score
      firstHalfAvg = scores.first;
      secondHalfAvg = scores.last;
    }

    double difference = secondHalfAvg - firstHalfAvg;

    if (difference >= 10) {
      return "Great improvement! Your recent quiz scores are trending upward.";
    } else if (difference <= -10) {
      return "Your recent scores are lower than before. Consider reviewing your weak areas.";
    } else {
      return "Your quiz performance has been relatively consistent over time.";
    }
  }

  Widget _buildPerformanceByType(
      BuildContext context, Map<String, dynamic> stats) {
    final quizTypes = stats['questionTypeAccuracy'] as Map<String, dynamic>;

    if (quizTypes.isEmpty) {
      return const SizedBox.shrink(); // Don't show if no data
    }

    // Find strongest and weakest areas
    String strongestType = "";
    String weakestType = "";
    double highestScore = 0;
    double lowestScore = 100;

    quizTypes.forEach((key, value) {
      // Handle both int and double types safely
      final double score = value is int ? value.toDouble() : value as double;
      if (score > highestScore) {
        highestScore = score;
        strongestType = key;
      }
      if (score < lowestScore) {
        lowestScore = score;
        weakestType = key;
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Question Type Analysis',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'How well you perform on different question types',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.grey,
              ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withAlpha(26),blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              ...quizTypes.entries.map((entry) {
                // Handle both int and double types safely
                final double scoreValue = entry.value is int
                    ? (entry.value as int).toDouble()
                    : entry.value as double;

                final Color barColor = _getBarColor(scoreValue);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _formatQuestionType(entry.key),
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          Row(
                            children: [
                              Text(
                                '${scoreValue.toStringAsFixed(1)}%',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const SizedBox(width: 4),
                              _getPerformanceIcon(scoreValue),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: scoreValue / 100,
                        backgroundColor: AppColors.grey,
                        minHeight: 8,
                        valueColor: AlwaysStoppedAnimation<Color>(barColor),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                );
              }).toList(),

              // Insights section
              if (strongestType.isNotEmpty && weakestType.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(),
                      const SizedBox(height: 8),
                      Text(
                        'Your Strengths & Areas to Improve',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.grey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(Icons.star,
                              color: AppColors.warningLight, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.grey,
                                ),
                                children: [
                                  const TextSpan(
                                    text: 'Strongest: ',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text:
                                        '${_formatQuestionType(strongestType)} (${highestScore.toStringAsFixed(1)}%)',
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.trending_up,
                              color: AppColors.infoLight, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.grey,
                                ),
                                children: [
                                  const TextSpan(
                                    text: 'Focus on: ',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  TextSpan(
                                    text:
                                        '${_formatQuestionType(weakestType)} (${lowestScore.toStringAsFixed(1)}%)',
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatQuestionType(String type) {
    switch (type) {
      case 'fillInBlank':
        return 'Fill in the Blank';
      case 'matchDefinition':
        return 'Match Definition';
      case 'trueFalse':
        return 'True/False';
      default:
        return type;
    }
  }

  Widget _getPerformanceIcon(double performance) {
    if (performance >= 80) {
      return Icon(Icons.emoji_events, color: AppColors.warningLight, size: 16);
    } else if (performance >= 60) {
      return Icon(Icons.thumb_up, color: AppColors.infoLight, size: 16);
    } else {
      return Icon(Icons.trending_up, color: AppColors.warningLight, size: 16);
    }
  }

  Color _getBarColor(double performance) {
    if (performance >= 80) {
      return AppColors.green;
    } else if (performance >= 60) {
      return AppColors.infoLight;
    } else if (performance >= 40) {
      return AppColors.warningLight;
    } else {
      return AppColors.incorrectLight;
    }
  }

  Widget _buildRecentQuizzes(BuildContext context, Map<String, dynamic> stats) {
    final recentQuizzes = stats['recentQuizzes'] as List;

    if (recentQuizzes.isEmpty) {
      return const SizedBox.shrink(); // Don't show if no data
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Quiz Analysis',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'Detailed breakdown of your latest quizzes (tap for incorrect answers)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.black.withValues(alpha: 0.6),
              ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: recentQuizzes.length,
          itemBuilder: (context, index) {
            final quiz = recentQuizzes[index];
            final score = quiz['score'] as int;
            final total = quiz['total'] as int;
            final percentage = (score / total * 100).toInt();
            final date = quiz['date'] as DateTime;

            // Check if incorrect answers data is available
            final hasIncorrectAnswers = quiz.containsKey('incorrectAnswers') &&
                (quiz['incorrectAnswers'] as List).isNotEmpty;

            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () {
                  if (hasIncorrectAnswers || total > score) {
                    _showIncorrectAnswersDialog(context, quiz);
                  } else {
                    showInfoToast(context,
                        title: 'Perfect Score',
                        description: 'No incorrect answers in this quiz!');
                  }
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            quiz['type'] as String,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: _getGradeColor(percentage),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '$percentage%',
                              style: const TextStyle(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            size: 14,
                            color: AppColors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat('MMM d, yyyy • h:mm a').format(date),
                            style: TextStyle(
                              color: AppColors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          _buildQuizStat(
                            'Correct',
                            '$score',
                            Icons.check_circle,
                            AppColors.correctLight,
                          ),
                          const SizedBox(width: 16),
                          _buildQuizStat(
                            'Incorrect',
                            '${total - score}',
                            Icons.cancel,
                            AppColors.incorrectLight,
                          ),
                          const SizedBox(width: 16),
                          _buildQuizStat(
                            'Total',
                            '$total',
                            Icons.quiz,
                            AppColors.primaryLight,
                          ),
                        ],
                      ),
                      if (quiz.containsKey('performance'))
                        Padding(
                          padding: const EdgeInsets.only(top: 12.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _getQuizPerformanceText(percentage),
                                  style: TextStyle(
                                    fontStyle: FontStyle.italic,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ),
                              if (hasIncorrectAnswers || total > score)
                                Icon(
                                  Icons.touch_app,
                                  size: 16,
                                  color: AppColors.grey,
                                ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  void _showIncorrectAnswersDialog(BuildContext context, dynamic quiz) {
    // Extract incorrect answers if available, or create a placeholder
    List incorrectAnswers = [];

    if (quiz.containsKey('incorrectAnswers')) {
      incorrectAnswers = quiz['incorrectAnswers'] as List;
    }

    // If we don't have specific incorrect answers data but know there are wrong answers
    final int score = quiz['score'] as int;
    final int total = quiz['total'] as int;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.incorrectLight.withAlpha(26),shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.close,
                          color: AppColors.incorrectLight),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Incorrect Answers',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            '${total - score} out of $total questions',
                            style: TextStyle(
                              color: AppColors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const Divider(height: 24),
                incorrectAnswers.isNotEmpty
                    ? Flexible(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: incorrectAnswers.length,
                          itemBuilder: (context, index) {
                            final item = incorrectAnswers[index];
                            return _buildIncorrectAnswerItem(item);
                          },
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.grey.withAlpha(26),borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            const Icon(Icons.info_outline,
                                color: AppColors.grey),
                            const SizedBox(height: 8),
                            Text(
                              'You had ${total - score} incorrect answers in this quiz.',
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'Detailed information is not available for this quiz.',
                              style: TextStyle(
                                  fontSize: 12, color: AppColors.grey),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIncorrectAnswerItem(dynamic item) {
    // Handle different formats of incorrect answer data
    String word = item['word'] ?? 'Unknown Word';
    String questionType = item['questionType'] ?? 'Question';
    String correctAnswer = item['correctAnswer'] ?? 'N/A';
    String userAnswer = item['userAnswer'] ?? 'No answer provided';
    String context = item['context'] ?? '';

    // Check if this is actually a correct answer (for true/false questions that might be mislabeled)
    bool isActuallyCorrect =
        correctAnswer.toLowerCase() == userAnswer.toLowerCase();
    Color userAnswerColor =
        isActuallyCorrect ? AppColors.correctLight : AppColors.incorrectLight;

    // Format context label based on question type
    String contextLabel;
    Widget contextWidget;

    switch (questionType) {
      case 'fillInBlank':
        contextLabel = 'Sentence:';
        contextWidget = Text.rich(
          TextSpan(
            children: _highlightBlank(context, correctAnswer),
          ),
          style: const TextStyle(fontSize: 14),
        );
        break;
      case 'matchDefinition':
        contextLabel = 'Definition:';
        contextWidget = Text(
          context,
          style: const TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
        );
        break;
      case 'trueFalse':
        contextLabel = 'Statement:';
        contextWidget = Text(
          context,
          style: const TextStyle(fontSize: 14),
        );
        break;
      default:
        contextLabel = 'Question:';
        contextWidget = Text(
          context,
          style: const TextStyle(fontSize: 14),
        );
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
              color: isActuallyCorrect
                  ? AppColors.correctLight.withValues(alpha: 0.3)
                  : AppColors.incorrectLight.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
          color: isActuallyCorrect
              ? AppColors.correctLight.withValues(alpha: 0.05)
              : AppColors.incorrectLight.withValues(alpha: 0.05),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  word,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                if (isActuallyCorrect)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.green.withAlpha(26),borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Correct',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              _formatQuestionType(questionType),
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.grey,
              ),
            ),
            if (context.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.grey,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contextLabel,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    contextWidget,
                  ],
                ),
              ),
            ],
            const Divider(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Correct: ',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                Expanded(
                  child: Text(
                    correctAnswer,
                    style: const TextStyle(color: AppColors.green),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Your answer: ',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                Expanded(
                  child: Text(
                    userAnswer,
                    style: TextStyle(color: userAnswerColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to highlight the blank in fill-in-blank questions
  List<InlineSpan> _highlightBlank(String sentence, String answer) {
    // For fill-in-blank questions, try to highlight where the blank should be
    if (sentence.contains('___')) {
      final parts = sentence.split('___');
      List<InlineSpan> spans = [];

      for (int i = 0; i < parts.length; i++) {
        // Add the text part
        spans.add(TextSpan(text: parts[i]));

        // Add the blank part (except after the last part)
        if (i < parts.length - 1) {
          spans.add(
            TextSpan(
              text: ' $answer ',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.green,
                decoration: TextDecoration.underline,
              ),
            ),
          );
        }
      }
      return spans;
    }

    // Fallback to just showing the sentence
    return [TextSpan(text: sentence)];
  }

  Widget _buildQuizStat(
      String label, String value, IconData icon, Color color) {
    return Expanded(
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          SizedBox(width: 4),
          Text(
            '$label: $value',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  String _getQuizPerformanceText(int percentage) {
    final double percentageValue = percentage.toDouble();
    if (percentageValue >= 90) return "Excellent performance!";
    if (percentageValue >= 80) return "Great job!";
    if (percentageValue >= 70) return "Good performance.";
    if (percentageValue >= 60) return "Room for improvement.";
    return "Keep practicing to improve.";
  }

  Color _getGradeColor(int percentage) {
    final double percentageValue = percentage.toDouble();
    if (percentageValue >= 90) return AppColors.successLight;
    if (percentageValue >= 80) return AppColors.correctLight;
    if (percentageValue >= 70) return AppColors.infoLight;
    if (percentageValue >= 60) return AppColors.warningLight;
    return AppColors.incorrectLight;
  }

  Widget _buildTrainButton(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Add the quiz mode selector here
        const QuizModeSelector(),

        const SizedBox(height: 24),

        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryLight.withAlpha(51),blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              context.pushNamed(RouteNames.leaderboard);
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              side: BorderSide(
                  color:
                      AppColors.getPrimaryColor(Theme.of(context).brightness)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.leaderboard,
                    size: 20,
                    color: AppColors.getPrimaryColor(
                        Theme.of(context).brightness)),
                const SizedBox(width: 8),
                Text(
                  'Global Leaderboard',
                  style: TextStyle(
                      fontSize: 16,
                      color: AppColors.getPrimaryColor(
                          Theme.of(context).brightness)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class QuizActionButtons extends ConsumerWidget {
  const QuizActionButtons({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Add New Card Button
          Expanded(
            child: ActionButton(
              icon: Icons.add_circle_outline,
              label: 'Training Mode',
              color1: AppColors.gradientButton1,
              color2: AppColors.gradientButton2,
              iconColor: AppColors.mastered,
              onTap: () => showQuizCreationSheet(context, ref, QuizMode.train),
            ),
          ),
          const SizedBox(width: 16),
          // Train Your Vocab Button
          Expanded(
            child: ActionButton(
              icon: Icons.fitness_center,
              label: 'Challenge Mode',
              color1: AppColors.gradientButton3,
              color2: AppColors.gradientButton4,
              iconColor: AppColors.mastered,
              onTap: () =>
                  showQuizCreationSheet(context, ref, QuizMode.challenge),
            ),
          ),
        ],
      ),
    );
  }
}
