// lib/src/features/vocab_quiz/providers/quiz_providers.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/achievements/points/points_manager.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_creation_sheet.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_generation_service.dart';
import 'package:vocadex/src/services/leaderboard_service.dart';

/// Provider for the current active quiz
final currentQuizProvider = StateProvider<Quiz?>((ref) => null);

/// Provider to track if the last answer was correct
final lastAnswerCorrectProvider = StateProvider<bool>((ref) => false);

/// Provider for whether to double XP for the current quiz
final doubleXpEnabledProvider = StateProvider<bool>((ref) => false);

/// Provider for the selected quiz mode
final quizModeProvider = StateProvider<QuizMode>((ref) => QuizMode.train);

/// Provider for challenge mode points
final challengePointsProvider = StateProvider<int>((ref) => 0);

/// Provider for tracking if double XP is enabled for the current quiz
/// This is imported from quiz_creation_sheet.dart
/// final quizDoubleXpProvider = StateProvider<bool>((ref) => false);

/// Provider for the points manager
final pointsManagerProvider = Provider<PointsManager>((ref) {
  return PointsManager();
});

/// Provider for the leaderboard service
final quizLeaderboardServiceProvider = Provider<LeaderboardService>((ref) {
  return LeaderboardService();
});

/// Provider for updating quiz state after answering a question
final answerQuestionProvider = Provider<Function(dynamic)>((ref) {
  return (dynamic answer) {
    final quiz = ref.read(currentQuizProvider);
    final questionIndex = ref.read(currentQuestionIndexProvider);

    if (quiz == null || questionIndex >= quiz.questions.length) {
      return false;
    }

    final question = quiz.questions[questionIndex];
    final isCorrect = question.checkAnswer(answer);

    // Set the lastAnswerCorrect state
    ref.read(lastAnswerCorrectProvider.notifier).state = isCorrect;

    // Update the quiz with the answer
    final updatedQuestions = [...quiz.questions];
    updatedQuestions[questionIndex] = question;

    // Calculate new score
    final newScore = quiz.score + (isCorrect ? 1 : 0);

    // Update points for challenge mode
    int pointsEarned = quiz.pointsEarned;
    int pointsDeducted = quiz.pointsDeducted;

    if (quiz.mode == QuizMode.challenge) {
      // Check if double XP is enabled
      final isDoubleXp = ref.read(quizDoubleXpProvider);
      final pointMultiplier = isDoubleXp ? 2 : 1;

      if (isCorrect) {
        // Add points for correct answer in challenge mode (with possible double XP)
        pointsEarned += PointsManager.pointsForCorrectAnswer * pointMultiplier;
      } else {
        // Deduct points for incorrect answer in challenge mode
        pointsDeducted += (PointsManager.pointsForCorrectAnswer / 2).round();
      }

      // Update the challenge points provider
      ref.read(challengePointsProvider.notifier).state =
          pointsEarned - pointsDeducted;
    }

    // Check if this is the last question
    final isLastQuestion = questionIndex == quiz.questions.length - 1;
    final isCompleted = isLastQuestion && question.isAnswered;

    // Update the quiz
    ref.read(currentQuizProvider.notifier).state = quiz.copyWith(
      questions: updatedQuestions,
      score: newScore,
      isCompleted: isCompleted,
      pointsEarned: pointsEarned,
      pointsDeducted: pointsDeducted,
    );

    // If quiz is completed, mark it as completed and update XP
    if (isCompleted) {
      ref.read(quizCompletedProvider.notifier).state = true;

      // For challenge mode, update the leaderboard
      if (quiz.mode == QuizMode.challenge) {
        // Get the final points (earned minus deducted)
        final finalPoints = pointsEarned - pointsDeducted;
        if (finalPoints > 0) {
          // Update the leaderboard with the points earned
          final leaderboardService = ref.read(quizLeaderboardServiceProvider);
          leaderboardService.updateLeaderboardScore(finalPoints);
        }
      }
    }

    return isCorrect;
  };
});

/// Provider for the current question index in the quiz
final currentQuestionIndexProvider = StateProvider<int>((ref) => 0);

/// Provider for tracking if the quiz is loading
final quizLoadingProvider = StateProvider<bool>((ref) => false);

/// Provider for any error messages during quiz generation or play
final quizErrorProvider = StateProvider<String?>((ref) => null);

/// Provider for the quiz generation service
final quizGenerationServiceProvider = Provider<QuizGenerationService>((ref) {
  return QuizGenerationService();
});

/// Provider for generating a new training mode quiz
final generateQuizProvider = Provider<Future<Quiz?> Function()>((ref) {
  return () async {
    final service = ref.read(quizGenerationServiceProvider);

    try {
      // Clear any existing quiz to avoid state issues
      ref.read(currentQuizProvider.notifier).state = null;
      ref.read(currentQuestionIndexProvider.notifier).state = 0;

      // Set loading state
      ref.read(quizLoadingProvider.notifier).state = true;
      ref.read(quizErrorProvider.notifier).state = null;
      debugPrint('🔍 QUIZ_PROVIDER: Starting quiz generation');

      // Generate quiz using pre-generated content from database
      final quiz = await service.generateMixedQuizFromDatabase();

      if (quiz != null) {
        debugPrint(
            '✅ QUIZ_PROVIDER: Quiz generated successfully with ${quiz.questions.length} questions');

        // Set quiz mode to train and ensure it shows feedback
        final trainModeQuiz = quiz.copyWith(
          mode: QuizMode.train,
          showFeedback: true,
        );

        // Set the current quiz and reset question index
        ref.read(currentQuizProvider.notifier).state = trainModeQuiz;
        ref.read(currentQuestionIndexProvider.notifier).state = 0;

        return trainModeQuiz;
      } else {
        debugPrint('❌ QUIZ_PROVIDER: Quiz generation returned null');
      }

      return quiz;
    } catch (e, stackTrace) {
      debugPrint('❌ QUIZ_PROVIDER: Error generating quiz: $e');
      debugPrint('❌ QUIZ_PROVIDER: Stack trace: $stackTrace');
      ref.read(quizErrorProvider.notifier).state = 'Error generating quiz: $e';
      return null;
    } finally {
      ref.read(quizLoadingProvider.notifier).state = false;
      debugPrint('🔍 QUIZ_PROVIDER: Quiz generation process completed');
    }
  };
});

/// Provider for generating a challenge quiz
final generateChallengeQuizProvider = Provider<Future<Quiz?> Function()>((ref) {
  return () async {
    final service = ref.read(quizGenerationServiceProvider);

    try {
      // Clear any existing quiz to avoid state issues
      ref.read(currentQuizProvider.notifier).state = null;
      ref.read(currentQuestionIndexProvider.notifier).state = 0;

      // Set loading state
      ref.read(quizLoadingProvider.notifier).state = true;
      ref.read(quizErrorProvider.notifier).state = null;
      debugPrint(
          '🔍 CHALLENGE_QUIZ_PROVIDER: Starting challenge quiz generation');

      // Generate a challenge quiz with proper configuration
      final quiz = await service.generateChallengeQuiz(
        challengeType:
            ChallengeType.mixed, // Mixed type with true/false and drag-and-drop
      );

      if (quiz != null) {
        debugPrint(
            '✅ CHALLENGE_QUIZ_PROVIDER: Challenge quiz generated successfully with ${quiz.questions.length} questions');

        // The quiz should already be configured properly by the generation service
        // Just verify it has the right configuration
        final challengeQuiz = quiz.copyWith(
          mode: QuizMode.challenge,
          showFeedback: false, // No feedback screen in challenge mode
          isTimed: true,
          timeLimit: 45, // 45 seconds for the entire quiz
          isChallenge: true,
        );

        // Check quiz question types to make sure they're appropriate for challenge mode
        int trueFalseCount = 0;
        int matchDefCount = 0;
        int otherCount = 0;

        for (final question in challengeQuiz.questions) {
          if (question is TrueFalseQuestion) {
            trueFalseCount++;
          } else if (question is MatchDefinitionQuestion) {
            matchDefCount++;
          } else {
            otherCount++;
          }
        }

        debugPrint(
            '📋 Challenge quiz question types: TrueFalse=$trueFalseCount, MatchDefinition=$matchDefCount, Other=$otherCount');

        // Set the current quiz and reset question index
        ref.read(currentQuizProvider.notifier).state = challengeQuiz;
        ref.read(currentQuestionIndexProvider.notifier).state = 0;

        // Reset challenge points
        ref.read(challengePointsProvider.notifier).state = 0;

        return challengeQuiz;
      } else {
        debugPrint(
            '❌ CHALLENGE_QUIZ_PROVIDER: Challenge quiz generation returned null');
      }

      return quiz;
    } catch (e, stackTrace) {
      debugPrint(
          '❌ CHALLENGE_QUIZ_PROVIDER: Error generating challenge quiz: $e');
      debugPrint('❌ CHALLENGE_QUIZ_PROVIDER: Stack trace: $stackTrace');
      ref.read(quizErrorProvider.notifier).state =
          'Error generating challenge quiz: $e';
      return null;
    } finally {
      ref.read(quizLoadingProvider.notifier).state = false;
      debugPrint(
          '🔍 CHALLENGE_QUIZ_PROVIDER: Challenge quiz generation process completed');
    }
  };
});

/// Provider for generating a challenge quiz with specific type
final generateChallengeQuizTypeProvider =
    Provider<Future<Quiz?> Function(ChallengeType)>((ref) {
  return (ChallengeType challengeType) async {
    final service = ref.read(quizGenerationServiceProvider);

    try {
      // Set loading state
      ref.read(quizLoadingProvider.notifier).state = true;
      ref.read(quizErrorProvider.notifier).state = null;
      debugPrint(
          '🔍 CHALLENGE_QUIZ_PROVIDER: Starting challenge quiz generation');

      // Generate the appropriate challenge quiz based on type
      Quiz? quiz;
      switch (challengeType) {
        case ChallengeType.mixed:
          quiz = await service.generateChallengeQuiz(
            challengeType: ChallengeType.mixed,
          );
          break;
        case ChallengeType.trueFalse:
          quiz = await service.generateChallengeQuiz(
            challengeType: ChallengeType.trueFalse,
          );
          break;
        case ChallengeType.dragAndDrop:
          quiz = await service.generateChallengeQuiz(
            challengeType: ChallengeType.dragAndDrop,
          );
          break;
      }

      if (quiz != null) {
        // Configure as challenge mode quiz
        final challengeQuiz = quiz.copyWith(
          mode: QuizMode.challenge,
          showFeedback: false, // No feedback screen in challenge mode
          isTimed: true,
          timeLimit: 45, // 45 seconds for the entire quiz
        );

        debugPrint(
            '✅ CHALLENGE_QUIZ_PROVIDER: Challenge quiz generated successfully with ${quiz.questions.length} questions');
        // Set the current quiz and reset question index
        debugPrint(
            '🔍 CHALLENGE_QUIZ_PROVIDER: Setting currentQuizProvider state');
        ref.read(currentQuizProvider.notifier).state = challengeQuiz;
        debugPrint(
            '🔍 CHALLENGE_QUIZ_PROVIDER: Setting currentQuestionIndexProvider state to 0');
        ref.read(currentQuestionIndexProvider.notifier).state = 0;

        // Reset challenge points
        ref.read(challengePointsProvider.notifier).state = 0;

        // Verify the quiz was set correctly
        final verifyQuiz = ref.read(currentQuizProvider);
        debugPrint(
            '🔍 CHALLENGE_QUIZ_PROVIDER: Verifying quiz was set: ${verifyQuiz != null ? 'Quiz set successfully' : 'Failed to set quiz'}');
      } else {
        debugPrint(
            '❌ CHALLENGE_QUIZ_PROVIDER: Challenge quiz generation returned null');
      }

      return quiz;
    } catch (e, stackTrace) {
      debugPrint(
          '❌ CHALLENGE_QUIZ_PROVIDER: Error generating challenge quiz: $e');
      debugPrint('❌ CHALLENGE_QUIZ_PROVIDER: Stack trace: $stackTrace');
      ref.read(quizErrorProvider.notifier).state =
          'Error generating challenge quiz: $e';
      return null;
    } finally {
      ref.read(quizLoadingProvider.notifier).state = false;
      debugPrint(
          '🔍 CHALLENGE_QUIZ_PROVIDER: Challenge quiz generation process completed');
    }
  };
});

/// Provider to track if the quiz is completed
final quizCompletedProvider = StateProvider<bool>((ref) => false);
