// lib/src/features/vocab_quiz/models/quiz_models.dart

import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// Enum representing quiz modes: Train (practice) or Challenge (competitive)
enum QuizMode {
  train, // Regular training mode - slower pace, feedback after each question
  challenge // Competitive mode - timed, points, leaderboard
}

/// Represents a quiz session with multiple questions
class Quiz {
  final String id;
  final String title;
  final QuizType type;
  final List<QuizQuestion> questions;
  final DateTime createdAt;
  final bool isCompleted;
  final int score;
  final int totalQuestions;
  final bool isChallenge; // Flag to identify challenge quizzes
  final ChallengeType?
      challengeType; // Type of challenge if isChallenge is true
  final bool isTimed; // Flag for timed quizzes
  final int? timeLimit; // Time limit in seconds if isTimed is true
  final bool showFeedback; // Whether to show feedback after each question
  final QuizMode mode; // The mode of the quiz: train or challenge
  final int pointsEarned; // Points earned in challenge mode
  final int
      pointsDeducted; // Points deducted for incorrect answers in challenge mode

  Quiz({
    required this.id,
    required this.title,
    required this.type,
    required this.questions,
    required this.createdAt,
    this.isCompleted = false,
    this.score = 0,
    this.totalQuestions = 10,
    this.isChallenge = false,
    this.challengeType,
    this.isTimed = false,
    this.timeLimit,
    this.showFeedback = true,
    this.mode = QuizMode.train,
    this.pointsEarned = 0,
    this.pointsDeducted = 0,
  });

  /// Create a copy of this quiz with updated properties
  Quiz copyWith({
    String? id,
    String? title,
    QuizType? type,
    List<QuizQuestion>? questions,
    DateTime? createdAt,
    bool? isCompleted,
    int? score,
    int? totalQuestions,
    bool? isChallenge,
    ChallengeType? challengeType,
    bool? isTimed,
    int? timeLimit,
    bool? showFeedback,
    QuizMode? mode,
    int? pointsEarned,
    int? pointsDeducted,
  }) {
    return Quiz(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      questions: questions ?? this.questions,
      createdAt: createdAt ?? this.createdAt,
      isCompleted: isCompleted ?? this.isCompleted,
      score: score ?? this.score,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      isChallenge: isChallenge ?? this.isChallenge,
      challengeType: challengeType ?? this.challengeType,
      isTimed: isTimed ?? this.isTimed,
      timeLimit: timeLimit ?? this.timeLimit,
      showFeedback: showFeedback ?? this.showFeedback,
      mode: mode ?? this.mode,
      pointsEarned: pointsEarned ?? this.pointsEarned,
      pointsDeducted: pointsDeducted ?? this.pointsDeducted,
    );
  }
}

/// The different types of quizzes available
enum QuizType {
  fillInTheBlank,
  matchDefinition,
  trueFalse,
  mixed,
  spellWord,
}

/// Types of challenge quizzes
enum ChallengeType {
  mixed,
  trueFalse,
  dragAndDrop,
}

/// Base class for all quiz questions
abstract class QuizQuestion {
  final String id;
  final VocabCard card;
  bool isAnswered;
  bool isCorrect;

  QuizQuestion({
    required this.id,
    required this.card,
    this.isAnswered = false,
    this.isCorrect = false,
  });

  /// Check if the answer is correct
  bool checkAnswer(dynamic userAnswer);
}

/// Question where user fills in a blank in a sentence with a vocabulary word
class FillInBlankQuestion extends QuizQuestion {
  final String sentence;
  final String blankWord;
  final List<String> options;

  FillInBlankQuestion({
    required super.id,
    required super.card,
    required this.sentence,
    required this.blankWord,
    required this.options,
    super.isAnswered,
    super.isCorrect,
  });

  @override
  bool checkAnswer(dynamic userAnswer) {
    if (userAnswer is String) {
      isAnswered = true;
      isCorrect = userAnswer.toLowerCase() == blankWord.toLowerCase();
      return isCorrect;
    }
    return false;
  }
}

/// Question where user matches a word to its definition
class MatchDefinitionQuestion extends QuizQuestion {
  final String definition;
  final List<String> options;

  MatchDefinitionQuestion({
    required super.id,
    required super.card,
    required this.definition,
    required this.options,
    super.isAnswered,
    super.isCorrect,
  });

  @override
  bool checkAnswer(dynamic userAnswer) {
    if (userAnswer is String) {
      isAnswered = true;
      isCorrect = userAnswer.toLowerCase() == card.word.toLowerCase();
      return isCorrect;
    }
    return false;
  }
}

/// Question where user determines if a statement is true or false
class TrueFalseQuestion extends QuizQuestion {
  final String statement;
  final bool answer;

  TrueFalseQuestion({
    required super.id,
    required super.card,
    required this.statement,
    required this.answer,
    super.isAnswered,
    super.isCorrect,
  });

  @override
  bool checkAnswer(dynamic userAnswer) {
    isAnswered = true;

    if (userAnswer is bool) {
      isCorrect = userAnswer == answer;
    } else if (userAnswer is String) {
      // Handle string values like "true" and "false"
      final lowerAnswer = userAnswer.toLowerCase();
      final booleanValue = lowerAnswer == 'true';
      isCorrect = booleanValue == answer;
    } else {
      isCorrect = false;
    }

    return isCorrect;
  }
}

/// Question where user spells a word by selecting individual letters
class SpellWordQuestion extends QuizQuestion {
  final String prompt; // Definition or usage example
  final String correctWord; // Word to be spelled
  final List<String>
      options; // Letters available to select (includes the correct letters plus extras)
  final bool
      isDefinition; // Whether the prompt is a definition or usage example

  SpellWordQuestion({
    required super.id,
    required super.card,
    required this.prompt,
    required this.correctWord,
    required this.options,
    required this.isDefinition,
    super.isAnswered,
    super.isCorrect,
  });

  @override
  bool checkAnswer(dynamic userAnswer) {
    if (userAnswer is String) {
      isAnswered = true;
      isCorrect = userAnswer.toLowerCase() == correctWord.toLowerCase();
      return isCorrect;
    }
    return false;
  }
}
