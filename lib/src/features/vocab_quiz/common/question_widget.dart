import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/true_false_question.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/card_drag_and_drop_quiz.dart';

/// A hybrid question widget that uses draggable cards for multiple choice/match definition
/// and swipeable cards for true/false questions
///

/// A hybrid question widget that uses draggable cards for multiple choice/match definition
/// and swipeable cards for true/false questions
class HybridQuizQuestionWidget extends StatefulWidget {
  final QuizQuestion question;
  final Function(dynamic) onAnswerSelected;

  const HybridQuizQuestionWidget({
    super.key,
    required this.question,
    required this.onAnswerSelected,
  });

  @override
  State<HybridQuizQuestionWidget> createState() =>
      _HybridQuizQuestionWidgetState();
}

class _HybridQuizQuestionWidgetState extends State<HybridQuizQuestionWidget>
    with SingleTickerProviderStateMixin {
  // For draggable cards
  String? _draggedOption;
  bool _isCardDropped = false;
  String? _selectedAnswer;

  // Animation controller for draggable cards
  late AnimationController _animationController;
  late Animation<double> _dropAnimation;

  @override
  void initState() {
    super.initState();
    debugPrint(
        '🔍 QUESTION_WIDGET: Initializing widget for question type: ${widget.question.runtimeType}');

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // For draggable card animations
    _dropAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_selectedAnswer != null) {
          widget.onAnswerSelected(_selectedAnswer!);
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🚨 QUESTION_WIDGET: Building widget for question type: ${widget.question.runtimeType}');

    try {
      if (widget.question is FillInBlankQuestion) {
        debugPrint(
            '🚨 QUESTION_WIDGET: Using CardDragAndDropQuiz for FillInBlankQuestion');
        try {
          // Use CardDragAndDropQuiz for Fill in the Blank questions
          final question = widget.question as FillInBlankQuestion;

          // Validate question data is complete before creating the CardDragAndDropQuiz
          if (question.sentence.isEmpty || question.options.isEmpty) {
            throw Exception("Question data is incomplete");
          }

          final dragDropQuestion = CardDragAndDropQuizQuestion(
            questionText: question.sentence,
            options: question.options,
            correctAnswer: question.blankWord,
          );

          return CardDragAndDropQuiz(
            question: dragDropQuestion,
            onCheck: (String answer) {
              // Convert the answer before passing it up
              widget.onAnswerSelected(answer);
            },
          );
        } catch (e, stackTrace) {
          debugPrint(
              '❌ QUESTION_WIDGET: Error with fill-in-blank question: $e');
          debugPrint('❌ QUESTION_WIDGET: Stack trace: $stackTrace');
          // Fall back to a basic implementation
          return _buildBasicQuestion(
            title: 'Fill in the Blank',
            content: (widget.question as FillInBlankQuestion).sentence,
            options: (widget.question as FillInBlankQuestion).options,
          );
        }
      } else if (widget.question is MatchDefinitionQuestion) {
        debugPrint(
            '🚨 QUESTION_WIDGET: Using draggable question for MatchDefinitionQuestion');
        try {
          return _buildDraggableQuestion(
            'Match Definition',
            (widget.question as MatchDefinitionQuestion).definition,
            (widget.question as MatchDefinitionQuestion).options,
            isDefinition: true,
          );
        } catch (e, stackTrace) {
          debugPrint(
              '❌ QUESTION_WIDGET: Error with match definition question: $e');
          debugPrint('❌ QUESTION_WIDGET: Stack trace: $stackTrace');
          // Fall back to a basic implementation
          return _buildBasicQuestion(
            title: 'Match Definition',
            content: (widget.question as MatchDefinitionQuestion).definition,
            options: (widget.question as MatchDefinitionQuestion).options,
            isDefinition: true,
          );
        }
      } else if (widget.question is TrueFalseQuestion) {
        debugPrint(
            '🚨 QUESTION_WIDGET: Using TrueFalseQuestionWidget for TrueFalseQuestion');
        try {
          return TrueFalseQuestionWidget(
            question: widget.question as TrueFalseQuestion,
            onAnswerSelected: (bool answer) {
              // Convert boolean to string before passing up
              widget.onAnswerSelected(answer.toString());
            },
          );
        } catch (e, stackTrace) {
          debugPrint('❌ QUESTION_WIDGET: Error with true/false question: $e');
          debugPrint('❌ QUESTION_WIDGET: Stack trace: $stackTrace');
          // Fall back to a basic true/false implementation
          return _buildBasicTrueFalseQuestion(
              widget.question as TrueFalseQuestion);
        }
      } else {
        debugPrint(
            '❌ QUESTION_WIDGET: Unsupported question type: ${widget.question.runtimeType}');
        return _buildErrorWidget(
            "Unsupported question type: ${widget.question.runtimeType}");
      }
    } catch (e, stackTrace) {
      debugPrint('❌ QUESTION_WIDGET: Unhandled error: $e');
      debugPrint('❌ QUESTION_WIDGET: Stack trace: $stackTrace');
      return _buildErrorWidget("Error displaying question: $e");
    }
  }

  // Common builder for draggable question types (Fill in Blank & Match Definition)
  Widget _buildDraggableQuestion(
      String title, String content, List<String> options,
      {bool isDefinition = false}) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Question title
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Question content
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: isDefinition
                  ? AppColors.getInfoColor(Theme.of(context).brightness)
                  : AppColors.grey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: isDefinition
                      ? AppColors.getInfoColor(Theme.of(context).brightness)
                      : AppColors.grey),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isDefinition)
                  Text(
                    'Definition:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color:
                          AppColors.getInfoColor(Theme.of(context).brightness),
                    ),
                  ),
                if (isDefinition) const SizedBox(height: 8),
                Text(
                  content,
                  style: const TextStyle(
                    fontSize: 18,
                    height: 1.5,
                  ),
                  textAlign: isDefinition ? TextAlign.left : TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Drop target area
          _buildDropTarget(),

          const Spacer(),

          // Option cards at the bottom
          _buildOptionCards(options),

          // Show word info at bottom
          _buildWordInfoBar(widget.question.card),
        ],
      ),
    );
  }

  // The drop target area for draggable cards
  Widget _buildDropTarget() {
    return DragTarget<String>(
      onWillAccept: (data) => !_isCardDropped && data != null,
      onAccept: (data) {
        setState(() {
          _isCardDropped = true;
          _selectedAnswer = data;
          _draggedOption = data;
        });

        // Start the drop animation
        _animationController.reset();
        _animationController.forward();
      },
      builder: (context, candidateData, rejectedData) {
        return Container(
          width: 200,
          height: 100,
          decoration: BoxDecoration(
            color: _isCardDropped
                ? AppColors.getPrimaryColor(Theme.of(context).brightness)
                    .withAlpha(26): AppColors.grey,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isCardDropped
                  ? AppColors.getPrimaryColor(Theme.of(context).brightness)
                  : AppColors.grey,
              width: 2,
              style: BorderStyle.solid,
            ),
          ),
          child: _isCardDropped
              ? ScaleTransition(
                  scale: _dropAnimation,
                  child: _buildOptionCard(_draggedOption!, isInTarget: true),
                )
              : const Center(
                  child: Text(
                    'Drop answer here',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.grey,
                    ),
                  ),
                ),
        );
      },
    );
  }

  // The row of option cards at the bottom
  Widget _buildOptionCards(List<String> options) {
    return SizedBox(
      height: 120,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: options.map((option) => _buildDraggableCard(option)).toList(),
      ),
    );
  }

  // A single draggable option card
  Widget _buildDraggableCard(String option) {
    // Hide the card if it has been dragged to the target
    if (_isCardDropped && _draggedOption == option) {
      return const SizedBox(width: 100);
    }

    return Draggable<String>(
      data: option,
      feedback: _buildOptionCard(option, isDragging: true),
      childWhenDragging: Opacity(
        opacity: 0.2,
        child: _buildOptionCard(option),
      ),
      child: _buildOptionCard(option),
    );
  }

  // The visual representation of an option card
  Widget _buildOptionCard(String option,
      {bool isInTarget = false, bool isDragging = false}) {
    return Card(
      elevation: isDragging ? 8 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: isInTarget ? 180 : 100,
        height: isInTarget ? 80 : 110,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withAlpha(178),Theme.of(context).primaryColor,
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                option,
                style: TextStyle(
                  fontSize: isInTarget ? 18 : 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            if (!isInTarget)
              Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Icon(
                  Icons.drag_handle,
                  size: 20,
                  color: AppColors.white.withAlpha(178),),
              ),
          ],
        ),
      ),
    );
  }

  // Word information bar at bottom of screen
  Widget _buildWordInfoBar(VocabCard card) {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.grey,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(13),blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Level badge
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: _getLevelColor(card.level),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              card.level,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getLevelColor(card.level).computeLuminance() > 0.5
                    ? AppColors.black
                    : AppColors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Word types
          Expanded(
            child: Wrap(
              spacing: 8,
              children: card.type
                  .take(2)
                  .map((type) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.getBackgroundColor(
                              Theme.of(context).brightness),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          type,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.getTextColor(
                                Theme.of(context).brightness),
                          ),
                        ),
                      ))
                  .toList(),
            ),
          ),

          // Mastery level
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Mastery',
                style: TextStyle(
                  fontSize: 10,
                  color: AppColors.grey,
                ),
              ),
              const SizedBox(height: 2),
              Container(
                width: 50,
                height: 6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  color: AppColors.grey,
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: card.masteryLevel / 10,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      color: _getMasteryColor(card.masteryLevel),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods for UI
  Color _getLevelColor(String level) {
    switch (level) {
      case 'A1':
        return AppColors.levelA1;
      case 'A2':
        return AppColors.levelA2;
      case 'B1':
        return AppColors.levelB1;
      case 'B2':
        return AppColors.levelB2;
      case 'C1':
        return AppColors.levelC1;
      case 'C2':
        return AppColors.levelC2;
      default:
        return AppColors.levelUnknown;
    }
  }

  Color _getMasteryColor(int masteryLevel) {
    if (masteryLevel >= 8)
      return AppColors.getSuccessColor(Theme.of(context).brightness);
    if (masteryLevel >= 5)
      return AppColors.getWarningColor(Theme.of(context).brightness);
    return AppColors.getFailureColor(Theme.of(context).brightness);
  }

  void _handleDrop(String option) {
    setState(() {
      _draggedOption = option;
      _isCardDropped = true;
      _selectedAnswer = option;
    });
    _animationController.forward();
  }

  // Basic question implementation as fallback
  Widget _buildBasicQuestion({
    required String title,
    required String content,
    required List<String> options,
    bool isDefinition = false,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: isDefinition
                  ? AppColors.getInfoColor(Theme.of(context).brightness)
                      .withAlpha(51): AppColors.grey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: isDefinition
                      ? AppColors.getInfoColor(Theme.of(context).brightness)
                      : AppColors.grey),
            ),
            child: Text(
              content,
              style: const TextStyle(
                fontSize: 18,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),
          const Text(
            'Select your answer:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(option),
                    onTap: () => widget.onAnswerSelected(option),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Basic true/false question implementation as fallback
  Widget _buildBasicTrueFalseQuestion(TrueFalseQuestion question) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'True or False?',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.getBackgroundColor(Theme.of(context).brightness),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey),
            ),
            child: Text(
              question.statement,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 64),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () => widget.onAnswerSelected(false.toString()),
                child: const Text('FALSE'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      AppColors.getFailureColor(Theme.of(context).brightness),
                  foregroundColor: AppColors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),
              ElevatedButton(
                onPressed: () => widget.onAnswerSelected(true.toString()),
                child: const Text('TRUE'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      AppColors.getSuccessColor(Theme.of(context).brightness),
                  foregroundColor: AppColors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Error widget to show when something goes wrong
  Widget _buildErrorWidget(String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.getFailureColor(Theme.of(context).brightness),
            size: 64,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error displaying question',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              errorMessage,
              style: TextStyle(
                  color:
                      AppColors.getFailureColor(Theme.of(context).brightness)),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => widget.onAnswerSelected("error"),
            child: const Text('Skip to Next Question'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              backgroundColor:
                  AppColors.getWarningColor(Theme.of(context).brightness),
              foregroundColor: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }
}
