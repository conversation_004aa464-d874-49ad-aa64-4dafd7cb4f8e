import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

class VocabGridCard extends StatelessWidget {
  final String word;
  final String? type;
  final Color color;
  final String? pronunciation;
  final int? masteryLevel;
  final Color? masteryColor;
  final bool showMastery;
  final double width;
  final double height;
  final bool isRaised;
  final bool applyMargin;
  final VoidCallback? onTap;

  const VocabGridCard({
    super.key,
    required this.word,
    required this.color,
    this.type,
    this.pronunciation,
    this.masteryLevel,
    this.masteryColor,
    this.showMastery = false,
    this.width = 160,
    this.height = 220,
    this.isRaised = false,
    this.applyMargin = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
        width: width,
        height: height,
        margin: applyMargin
            ? EdgeInsets.only(bottom: isRaised ? 24 : 0)
            : EdgeInsets.zero,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color, width: 4),
          boxShadow: [
            if (isRaised)
              BoxShadow(
                color: color.withAlpha(76),spreadRadius: 2,
                blurRadius: 12,
                offset: const Offset(0, 8),
              ),
            BoxShadow(
              color: AppColors.black.withAlpha(20),blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header with color and word
              Container(
                color: color,
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 6),
                    Center(
                      child: Text(
                        word.toLowerCase(),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (pronunciation != null && pronunciation!.isNotEmpty)
                      Center(
                        child: Text(
                          pronunciation!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              ),
              // Content area
              Expanded(
                child: Container(
                  color: AppColors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (type != null && type!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.fromLTRB(12, 8, 12, 4),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: color.withAlpha(204),borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: AppColors.white),
                            ),
                            child: Text(
                              type!.toLowerCase(),
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.white,
                              ),
                            ),
                          ),
                        ),
                      const Spacer(),
                      Center(
                        child: Icon(
                          Icons.lock,
                          size: 40,
                          color: color.withAlpha(178),),
                      ),
                      const Spacer(),
                      if (showMastery &&
                          masteryLevel != null &&
                          masteryColor != null)
                        Padding(
                          padding: const EdgeInsets.fromLTRB(12, 4, 12, 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: LinearProgressIndicator(
                                  value: masteryLevel! / 10,
                                  backgroundColor: AppColors.grey,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    masteryColor!,
                                  ),
                                  minHeight: 6,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Mastery: $masteryLevel/10',
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
