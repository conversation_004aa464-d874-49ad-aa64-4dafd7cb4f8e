// lib/src/features/vocab_quiz/true_false_question.dart

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Widget to render a true/false question with Tinder-like swipe interactions
class TrueFalseQuestionWidget extends StatefulWidget {
  final TrueFalseQuestion question;
  final Function(bool) onAnswerSelected;

  const TrueFalseQuestionWidget({
    super.key,
    required this.question,
    required this.onAnswerSelected,
  });

  @override
  State<TrueFalseQuestionWidget> createState() =>
      _TrueFalseQuestionWidgetState();
}

class _TrueFalseQuestionWidgetState extends State<TrueFalseQuestionWidget>
    with SingleTickerProviderStateMixin {
  /// Tracks card position during drag
  Offset _cardPosition = Offset.zero;

  /// Tracks if card is being dragged
  bool _isDragging = false;

  /// Tracks if the question has been answered
  bool _hasAnswered = false;

  /// Animation controller for the card
  late AnimationController _animationController;
  late Animation<Offset> _animation;

  /// The swipe direction (null, left, or right)
  SwipeDirection? _swipeDirection;

  /// Swipe threshold (percentage of screen width)
  final double _swipeThreshold = 0.25;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _animation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        if (_swipeDirection != null) {
          widget.onAnswerSelected(_swipeDirection == SwipeDirection.right);
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Definition text
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.getBackgroundColor(Theme.of(context).brightness),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey),
            ),
            child: Text(
              widget.question.statement,
              style: const TextStyle(
                fontSize: 20,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),

          // Question text
          Text(
            'Is this the correct definition?',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.getTextColor(Theme.of(context).brightness),
            ),
          ),
          const SizedBox(height: 16),

          // Swipe instruction
          if (!_hasAnswered)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color:
                    AppColors.getBackgroundColor(Theme.of(context).brightness),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.swipe,
                      size: 20,
                      color:
                          AppColors.getTextColor(Theme.of(context).brightness)),
                  const SizedBox(width: 8),
                  Text(
                    'Swipe right for TRUE, left for FALSE',
                    style: TextStyle(
                      color:
                          AppColors.getTextColor(Theme.of(context).brightness),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

          // Swipeable vocab card section
          Expanded(
            child: Center(
              child: Stack(
                children: [
                  // FALSE indicator (left side)
                  Positioned(
                    left: 16,
                    top: size.height * 0.2,
                    child: AnimatedOpacity(
                      opacity: _cardPosition.dx < -20 ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 150),
                      child: _buildOverlayBadge(
                          'FALSE',
                          AppColors.getFailureColor(
                              Theme.of(context).brightness)),
                    ),
                  ),

                  // TRUE indicator (right side)
                  Positioned(
                    right: 16,
                    top: size.height * 0.2,
                    child: AnimatedOpacity(
                      opacity: _cardPosition.dx > 20 ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 150),
                      child: _buildOverlayBadge(
                          'TRUE',
                          AppColors.getSuccessColor(
                              Theme.of(context).brightness)),
                    ),
                  ),

                  // The vocab card
                  AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      final offset = _animationController.isAnimating
                          ? _animation.value
                          : _cardPosition;

                      final rotation = offset.dx / screenWidth * 0.5;

                      final rightOpacity = max(0,
                          min(1, offset.dx / (screenWidth * _swipeThreshold)));
                      final leftOpacity = max(0,
                          min(1, -offset.dx / (screenWidth * _swipeThreshold)));

                      return GestureDetector(
                        onPanStart: _hasAnswered
                            ? null
                            : (_) {
                                setState(() {
                                  _isDragging = true;
                                });
                              },
                        onPanUpdate: _hasAnswered
                            ? null
                            : (details) {
                                setState(() {
                                  _cardPosition += details.delta;
                                });
                              },
                        onPanEnd: _hasAnswered
                            ? null
                            : (details) {
                                setState(() {
                                  _isDragging = false;
                                });

                                if (_cardPosition.dx.abs() >
                                    screenWidth * _swipeThreshold) {
                                  _animateCardAway();
                                } else {
                                  _resetCardPosition();
                                }
                              },
                        child: Transform.translate(
                          offset: offset,
                          child: Transform.rotate(
                            angle: rotation,
                            child: Stack(
                              children: [
                                _buildVocabCard(),

                                // Red overlay (left swipe)
                                if (leftOpacity > 0)
                                  Positioned.fill(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Opacity(
                                        opacity: leftOpacity * 0.7,
                                        child: Container(
                                          color: AppColors.getFailureColor(
                                              Theme.of(context).brightness),
                                          child: Center(
                                            child: Icon(
                                              Icons.close,
                                              color: AppColors.white,
                                              size: 64 * leftOpacity.toDouble(),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),

                                // Green overlay (right swipe)
                                if (rightOpacity > 0)
                                  Positioned.fill(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Opacity(
                                        opacity: rightOpacity * 0.7,
                                        child: Container(
                                          color: AppColors.getSuccessColor(
                                              Theme.of(context).brightness),
                                          child: Center(
                                            child: Icon(
                                              Icons.check,
                                              color: AppColors.white,
                                              size:
                                                  64 * rightOpacity.toDouble(),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // True/False circular buttons
          if (!_hasAnswered)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildCircularButton(
                    false,
                    AppColors.getFailureColor(Theme.of(context).brightness),
                    Icons.close,
                  ),
                  _buildCircularButton(
                    true,
                    AppColors.getSuccessColor(Theme.of(context).brightness),
                    Icons.check,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVocabCard() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85, // Reduced width
      height: 400, // Increased height
      decoration: BoxDecoration(
        color: AppColors.getWarningColor(Theme.of(context).brightness),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
            color: AppColors.getWarningColor(Theme.of(context).brightness),
            width: 2),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(26),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top section with word
          Expanded(
            flex: 3, // Give more space to top section
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    widget.question.card.word,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '/${widget.question.card.pronunciation}/',
                    style: TextStyle(
                      fontSize: 18,
                      color: AppColors.white.withAlpha(230),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Bottom section with locked sections
          Expanded(
            flex: 4,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
                border: Border.all(
                  color: AppColors.getWarningColor(Theme.of(context).brightness)
                      .withAlpha(178),
                ),
              ),
              child: Column(
                children: [
                  // Definition section
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Vertical definition label
                          Container(
                            padding: const EdgeInsets.only(top: 4),
                            width: 34,
                            child: const RotatedBox(
                              quarterTurns: -1,
                              child: Text(
                                "definition",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          // Orange vertical bar
                          Container(
                            width: 6,
                            height: double.infinity,
                            color: AppColors.primaryLight,
                          ),

                          const SizedBox(width: 12),

                          // Lock icon
                          Expanded(
                            child: Center(
                              child: Icon(
                                Icons.lock,
                                size: 48,
                                color: AppColors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Example section
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Vertical example label
                          Container(
                            padding: const EdgeInsets.only(top: 4),
                            width: 34,
                            child: const RotatedBox(
                              quarterTurns: -1,
                              child: Text(
                                "example",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          // Orange vertical bar
                          Container(
                            width: 6,
                            height: double.infinity,
                            color: AppColors.primaryLight,
                          ),

                          const SizedBox(width: 12),

                          // Lock icon
                          Expanded(
                            child: Center(
                              child: Icon(
                                Icons.lock,
                                size: 48,
                                color: AppColors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverlayBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.white, width: 2),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: AppColors.white,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildCircularButton(bool isTrue, Color color, IconData icon) {
    return InkWell(
      onTap: _hasAnswered ? null : () => _selectAnswer(isTrue),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
          boxShadow: [
            BoxShadow(
              color: color.withAlpha(76),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: AppColors.white,
          size: 32,
        ),
      ),
    );
  }

  void _selectAnswer(bool isTrue) {
    setState(() {
      _hasAnswered = true;
    });

    if (isTrue) {
      _swipeDirection = SwipeDirection.right;
      _animation = Tween<Offset>(
        begin: _cardPosition,
        end: Offset(
            MediaQuery.of(context).size.width * 1.5, 0), // Increased offset
      ).animate(_animationController);
    } else {
      _swipeDirection = SwipeDirection.left;
      _animation = Tween<Offset>(
        begin: _cardPosition,
        end: Offset(
            -MediaQuery.of(context).size.width * 1.5, 0), // Increased offset
      ).animate(_animationController);
    }

    _animationController.forward().then((_) {
      // Add a small delay before calling onAnswerSelected
      Future.delayed(const Duration(milliseconds: 300), () {
        widget.onAnswerSelected(isTrue);
      });
    });
  }

  void _animateCardAway() {
    if (_cardPosition.dx > 0) {
      _swipeDirection = SwipeDirection.right;
      _animation = Tween<Offset>(
        begin: _cardPosition,
        end: Offset(
            MediaQuery.of(context).size.width * 1.5, 0), // Increased offset
      ).animate(_animationController);
    } else {
      _swipeDirection = SwipeDirection.left;
      _animation = Tween<Offset>(
        begin: _cardPosition,
        end: Offset(
            -MediaQuery.of(context).size.width * 1.5, 0), // Increased offset
      ).animate(_animationController);
    }

    setState(() {
      _hasAnswered = true;
    });

    _animationController.forward().then((_) {
      // Add a small delay before calling onAnswerSelected
      Future.delayed(const Duration(milliseconds: 300), () {
        widget.onAnswerSelected(_swipeDirection == SwipeDirection.right);
      });
    });
  }

  void _resetCardPosition() {
    _animation = Tween<Offset>(
      begin: _cardPosition,
      end: Offset.zero,
    ).animate(_animationController);

    setState(() {
      _swipeDirection = null;
    });

    _animationController.forward().then((_) {
      setState(() {
        _cardPosition = Offset.zero;
      });
      _animationController.reset();
    });
  }
}

enum SwipeDirection { left, right }
