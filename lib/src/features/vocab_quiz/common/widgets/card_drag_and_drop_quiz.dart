import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'vocab_grid_card.dart';

/// Data model for a card drag-and-drop quiz question
class CardDragAndDropQuizQuestion {
  final String questionText;
  final List<String> options;
  final String correctAnswer;

  CardDragAndDropQuizQuestion({
    required this.questionText,
    required this.options,
    required this.correctAnswer,
  });
}

/// Widget for card drag-and-drop quiz UI
class CardDragAndDropQuiz extends StatefulWidget {
  final CardDragAndDropQuizQuestion question;
  final void Function(String selectedAnswer) onCheck;

  const CardDragAndDropQuiz({
    super.key,
    required this.question,
    required this.onCheck,
  });

  @override
  State<CardDragAndDropQuiz> createState() => _CardDragAndDropQuizState();
}

class _CardDragAndDropQuizState extends State<CardDragAndDropQuiz>
    with SingleTickerProviderStateMixin {
  String? _draggedOption;
  bool _isCardDropped = false;
  int? _raisedIndex;
  late AnimationController _controller;
  late Animation<double> _dropAnimation;

  static const double cardWidth = 160;
  static const double cardHeight = 220;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _dropAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOutBack),
    );
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Add delay before showing feedback
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_draggedOption != null) {
            widget.onCheck(_draggedOption!);
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onCardDropped(String option) {
    setState(() {
      _draggedOption = option;
      _isCardDropped = true;
      _raisedIndex = null;
    });
    _controller.forward(from: 0);
  }

  void _onCardTapped(int index) {
    setState(() {
      _raisedIndex = _raisedIndex == index ? null : index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final cardColor = Theme.of(context).primaryColor;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Question text
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              widget.question.questionText,
              style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),

          // Drop zone
          DragTarget<String>(
            onWillAccept: (data) => !_isCardDropped && data != null,
            onAccept: (data) => _onCardDropped(data),
            builder: (context, candidateData, rejectedData) {
              return Container(
                width: cardWidth,
                height: cardHeight,
                decoration: BoxDecoration(
                  color: _isCardDropped
                      ? AppColors.transparent
                      : AppColors.getBackgroundColor(
                          Theme.of(context).brightness),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color:
                        _isCardDropped ? AppColors.transparent : AppColors.grey,
                    width: 3,
                  ),
                ),
                child: _isCardDropped && _draggedOption != null
                    ? ScaleTransition(
                        scale: _dropAnimation,
                        child: VocabGridCard(
                          word: _draggedOption!,
                          color: cardColor,
                          width: cardWidth,
                          height: cardHeight,
                          isRaised: false,
                          applyMargin: false,
                        ),
                      )
                    : const Center(
                        child: Text(
                          'Drop answer here',
                          style: TextStyle(fontSize: 16, color: AppColors.grey),
                        ),
                      ),
              );
            },
          ),

          const SizedBox(height: 48),

          // Fan of option cards, centered
          _buildFannedOptionCards(widget.question.options),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildFannedOptionCards(List<String> options) {
    // Fan the cards with a slight rotation and horizontal offset
    final double spread = 80.0;
    final List<double> offsets = [-spread, 0.0, spread];
    final List<double> angles = [-0.18, 0.0, 0.18];
    final cardColor = Theme.of(context).primaryColor;
    return SizedBox(
      width: cardWidth * 2.2,
      height: cardHeight + 80,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          for (int i = 0; i < options.length; i++)
            if (!_isCardDropped || _draggedOption != options[i])
              AnimatedPositioned(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
                left: (cardWidth + offsets[i]) - cardWidth / 2,
                bottom: _raisedIndex == i ? 50 : 0,
                child: Transform.rotate(
                  angle: angles[i],
                  child: Draggable<String>(
                    data: options[i],
                    feedback: Material(
                      color: AppColors.transparent,
                      elevation: 4.0,
                      child: SizedBox(
                        width: cardWidth,
                        height: cardHeight,
                        child: VocabGridCard(
                          word: options[i],
                          color: cardColor,
                          width: cardWidth,
                          height: cardHeight,
                          isRaised: true,
                          applyMargin: false,
                        ),
                      ),
                    ),
                    childWhenDragging: Opacity(
                      opacity: 0.2,
                      child: VocabGridCard(
                        word: options[i],
                        color: cardColor,
                        width: cardWidth,
                        height: cardHeight,
                        isRaised: _raisedIndex == i,
                        applyMargin: true,
                      ),
                    ),
                    child: VocabGridCard(
                      word: options[i],
                      color: cardColor,
                      width: cardWidth,
                      height: cardHeight,
                      isRaised: _raisedIndex == i,
                      applyMargin: true,
                      onTap: () => _onCardTapped(i),
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }
}
