// lib/src/features/vocab_quiz/models/quiz_content_models.dart

import 'package:cloud_firestore/cloud_firestore.dart';

/// Base class for all pre-generated quiz content
abstract class QuizContent {
  final String id;
  final String difficulty;
  final DateTime createdAt;

  const QuizContent({
    required this.id,
    required this.difficulty,
    required this.createdAt,
  });

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore();

  /// Create from Firestore document
  static QuizContent fromFirestore(Map<String, dynamic> data, String type) {
    switch (type) {
      case 'fillInBlank':
        return FillInBlankContent.fromFirestore(data);
      case 'trueFalse':
        return TrueFalseContent.fromFirestore(data);
      case 'spellWord':
        return SpellWordContent.fromFirestore(data);
      default:
        throw ArgumentError('Unknown quiz content type: $type');
    }
  }
}

/// Pre-generated fill-in-the-blank question content
class FillInBlankContent extends QuizContent {
  final String sentence;
  final String correctAnswer;
  final List<String> distractors;
  final String context; // e.g., "educational", "business", "casual"

  const FillInBlankContent({
    required super.id,
    required super.difficulty,
    required super.createdAt,
    required this.sentence,
    required this.correctAnswer,
    required this.distractors,
    required this.context,
  });

  @override
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'difficulty': difficulty,
      'createdAt': Timestamp.fromDate(createdAt),
      'sentence': sentence,
      'correctAnswer': correctAnswer,
      'distractors': distractors,
      'context': context,
    };
  }

  static FillInBlankContent fromFirestore(Map<String, dynamic> data) {
    return FillInBlankContent(
      id: data['id'] ?? '',
      difficulty: data['difficulty'] ?? 'B1',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      sentence: data['sentence'] ?? '',
      correctAnswer: data['correctAnswer'] ?? '',
      distractors: List<String>.from(data['distractors'] ?? []),
      context: data['context'] ?? 'general',
    );
  }

  FillInBlankContent copyWith({
    String? id,
    String? difficulty,
    DateTime? createdAt,
    String? sentence,
    String? correctAnswer,
    List<String>? distractors,
    String? context,
  }) {
    return FillInBlankContent(
      id: id ?? this.id,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      sentence: sentence ?? this.sentence,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      distractors: distractors ?? this.distractors,
      context: context ?? this.context,
    );
  }
}

/// Pre-generated true/false question content
class TrueFalseContent extends QuizContent {
  final String statement;
  final bool isTrue;
  final String explanation;
  final String category; // e.g., "definition", "usage", "synonym"

  const TrueFalseContent({
    required super.id,
    required super.difficulty,
    required super.createdAt,
    required this.statement,
    required this.isTrue,
    required this.explanation,
    required this.category,
  });

  @override
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'difficulty': difficulty,
      'createdAt': Timestamp.fromDate(createdAt),
      'statement': statement,
      'isTrue': isTrue,
      'explanation': explanation,
      'category': category,
    };
  }

  static TrueFalseContent fromFirestore(Map<String, dynamic> data) {
    return TrueFalseContent(
      id: data['id'] ?? '',
      difficulty: data['difficulty'] ?? 'B1',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      statement: data['statement'] ?? '',
      isTrue: data['isTrue'] ?? false,
      explanation: data['explanation'] ?? '',
      category: data['category'] ?? 'definition',
    );
  }

  TrueFalseContent copyWith({
    String? id,
    String? difficulty,
    DateTime? createdAt,
    String? statement,
    bool? isTrue,
    String? explanation,
    String? category,
  }) {
    return TrueFalseContent(
      id: id ?? this.id,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      statement: statement ?? this.statement,
      isTrue: isTrue ?? this.isTrue,
      explanation: explanation ?? this.explanation,
      category: category ?? this.category,
    );
  }
}

/// Pre-generated spell word question content
class SpellWordContent extends QuizContent {
  final String prompt;
  final String answer;
  final String promptType; // e.g., "definition", "sentence", "synonym"

  const SpellWordContent({
    required super.id,
    required super.difficulty,
    required super.createdAt,
    required this.prompt,
    required this.answer,
    required this.promptType,
  });

  @override
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'difficulty': difficulty,
      'createdAt': Timestamp.fromDate(createdAt),
      'prompt': prompt,
      'answer': answer,
      'promptType': promptType,
    };
  }

  static SpellWordContent fromFirestore(Map<String, dynamic> data) {
    return SpellWordContent(
      id: data['id'] ?? '',
      difficulty: data['difficulty'] ?? 'B1',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      prompt: data['prompt'] ?? '',
      answer: data['answer'] ?? '',
      promptType: data['promptType'] ?? 'definition',
    );
  }

  SpellWordContent copyWith({
    String? id,
    String? difficulty,
    DateTime? createdAt,
    String? prompt,
    String? answer,
    String? promptType,
  }) {
    return SpellWordContent(
      id: id ?? this.id,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      prompt: prompt ?? this.prompt,
      answer: answer ?? this.answer,
      promptType: promptType ?? this.promptType,
    );
  }
}

/// Container for all pre-generated quiz content for a word
class WordQuizContent {
  final String wordId;
  final String word;
  final String difficulty;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final int contentVersion;
  final List<FillInBlankContent> fillInBlankQuestions;
  final List<TrueFalseContent> trueFalseQuestions;
  final List<SpellWordContent> spellWordQuestions;

  const WordQuizContent({
    required this.wordId,
    required this.word,
    required this.difficulty,
    required this.createdAt,
    required this.lastUpdated,
    required this.contentVersion,
    required this.fillInBlankQuestions,
    required this.trueFalseQuestions,
    required this.spellWordQuestions,
  });

  Map<String, dynamic> toFirestore() {
    return {
      'metadata': {
        'word': word,
        'difficulty': difficulty,
        'createdAt': Timestamp.fromDate(createdAt),
        'lastUpdated': Timestamp.fromDate(lastUpdated),
        'contentVersion': contentVersion,
      },
      'fillInBlank': fillInBlankQuestions.map((q) => q.toFirestore()).toList(),
      'trueFalse': trueFalseQuestions.map((q) => q.toFirestore()).toList(),
      'spellWord': spellWordQuestions.map((q) => q.toFirestore()).toList(),
    };
  }

  static WordQuizContent fromFirestore(String wordId, Map<String, dynamic> data) {
    final metadata = data['metadata'] as Map<String, dynamic>? ?? {};
    
    return WordQuizContent(
      wordId: wordId,
      word: metadata['word'] ?? '',
      difficulty: metadata['difficulty'] ?? 'B1',
      createdAt: (metadata['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastUpdated: (metadata['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
      contentVersion: metadata['contentVersion'] ?? 1,
      fillInBlankQuestions: (data['fillInBlank'] as List<dynamic>? ?? [])
          .map((item) => FillInBlankContent.fromFirestore(item as Map<String, dynamic>))
          .toList(),
      trueFalseQuestions: (data['trueFalse'] as List<dynamic>? ?? [])
          .map((item) => TrueFalseContent.fromFirestore(item as Map<String, dynamic>))
          .toList(),
      spellWordQuestions: (data['spellWord'] as List<dynamic>? ?? [])
          .map((item) => SpellWordContent.fromFirestore(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get total number of questions across all types
  int get totalQuestions => 
      fillInBlankQuestions.length + trueFalseQuestions.length + spellWordQuestions.length;

  /// Check if content is available for quiz generation
  bool get hasContent => totalQuestions > 0;

  WordQuizContent copyWith({
    String? wordId,
    String? word,
    String? difficulty,
    DateTime? createdAt,
    DateTime? lastUpdated,
    int? contentVersion,
    List<FillInBlankContent>? fillInBlankQuestions,
    List<TrueFalseContent>? trueFalseQuestions,
    List<SpellWordContent>? spellWordQuestions,
  }) {
    return WordQuizContent(
      wordId: wordId ?? this.wordId,
      word: word ?? this.word,
      difficulty: difficulty ?? this.difficulty,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      contentVersion: contentVersion ?? this.contentVersion,
      fillInBlankQuestions: fillInBlankQuestions ?? this.fillInBlankQuestions,
      trueFalseQuestions: trueFalseQuestions ?? this.trueFalseQuestions,
      spellWordQuestions: spellWordQuestions ?? this.spellWordQuestions,
    );
  }
}
