import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_button.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_stats_screen.dart';

/// A widget that displays both Train Mode and Challenge Mode buttons
class QuizModeSelector extends ConsumerWidget {
  final double height;
  final double spacing;
  final EdgeInsets padding;

  const QuizModeSelector({
    super.key,
    this.height = 56,
    this.spacing = 16,
    this.padding = const EdgeInsets.symmetric(vertical: 16),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Mode title
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            child: Text(
              'Quiz Mode',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w600,
                color: AppColors.textLight,
              ),
            ),
          ),

          // Mode description
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            padding: const EdgeInsets.symmetric(horizontal: 1),
            child: Text(
              'Train mode helps you learn at your own pace with feedback. Challenge mode tests your speed with a timed quiz to earn points for the leaderboard.',
              textAlign: TextAlign.start,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
          ),
          QuizActionButtons(),
          // Quiz mode buttons
          // Row(
          //   children: [
          //     // Train Mode Button
          //     Expanded(
          //       child: TrainModeButton(
          //         height: height,
          //       ),
          //     ),

          //     SizedBox(width: spacing),

          //     // Challenge Mode Button
          //     Expanded(
          //       child: ChallengeModeButton(
          //         height: height,
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }
}

/// A vertical version of the quiz mode selector with buttons stacked
class QuizModeSelectorVertical extends ConsumerWidget {
  final double buttonHeight;
  final double spacing;
  final EdgeInsets padding;

  const QuizModeSelectorVertical({
    super.key,
    this.buttonHeight = 56,
    this.spacing = 16,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Mode title
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            child: Text(
              'Select Quiz Mode',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),

          // Mode description
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            child: Text(
              'Train mode helps you learn at your own pace with feedback. Challenge mode tests your speed with a timed quiz to earn points for the leaderboard.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.grey,
              ),
            ),
          ),

          // Train Mode Button
          TrainModeButton(
            height: buttonHeight,
          ),

          SizedBox(height: spacing),

          // Challenge Mode Button
          ChallengeModeButton(
            height: buttonHeight,
          ),
        ],
      ),
    );
  }
}
