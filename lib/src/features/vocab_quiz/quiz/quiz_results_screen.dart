import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/points/points_manager.dart';
import 'package:vocadex/src/features/ads/models/ad_trigger.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/features/analytics/providers/daily_metrics_provider.dart';
import 'package:vocadex/src/features/ads/providers/ad_providers.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_analysis_screen.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_providers.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_stats_screen.dart';
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/services/quiz_service.dart';

/// Screen to display quiz results with enhanced UI and word XP information
class QuizResultScreen extends ConsumerStatefulWidget {
  final Quiz quiz;

  const QuizResultScreen({
    super.key,
    required this.quiz,
  });

  @override
  ConsumerState<QuizResultScreen> createState() => _QuizResultScreenState();
}

class _QuizResultScreenState extends ConsumerState<QuizResultScreen>
    with SingleTickerProviderStateMixin {
  bool _isCompleted = false;
  bool _isSaving = false;
  late AnimationController _animationController;
  late Animation<double> _scoreAnimation;

  // Track cards that gained XP
  final List<XpGainInfo> _cardsWithXpGain = [];

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Calculate percentage for animation
    final percentCorrect =
        widget.quiz.score / widget.quiz.questions.length * 100;
    _scoreAnimation = Tween<double>(begin: 0, end: percentCorrect).animate(
        CurvedAnimation(
            parent: _animationController, curve: Curves.easeOutCubic));

    // Start animation and save results
    _animationController.forward();
    _saveResults();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _saveResults() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final pointsManager = PointsManager();

      // Update mastery levels for each card
      for (final question in widget.quiz.questions) {
        final updatedCard =
            await _updateCardMastery(question.card, question.isCorrect);

        // Only award points in challenge mode for correct answers
        if (widget.quiz.mode == QuizMode.challenge && question.isCorrect) {
          await pointsManager.awardPoints(
            amount: PointsManager.pointsForCorrectAnswer,
            reason: 'Correct answer in challenge mode',
            cardId: question.card.id,
            cardType: question.card.type
                .join(', '), // Join list of types into a comma-separated string
          );
        }

        // Track cards that gained XP (for display purposes)
        if (question.isCorrect) {
          _cardsWithXpGain.add(
            XpGainInfo(
              card: question.card,
              updatedCard: updatedCard,
              xpGained: widget.quiz.mode == QuizMode.challenge
                  ? PointsManager.pointsForCorrectAnswer
                  : 0,
            ),
          );
        }
      }

      // Save quiz history to Firebase using QuizService
      final quizService = QuizService();

      // Calculate question type statistics
      Map<String, dynamic> questionStats = {
        'fillInBlank': {'total': 0, 'correct': 0},
        'matchDefinition': {'total': 0, 'correct': 0},
        'trueFalse': {'total': 0, 'correct': 0},
      };

      // Collect incorrect answers for detailed analysis
      List<Map<String, dynamic>> incorrectAnswers = [];

      // Count questions by type and correctness
      for (final question in widget.quiz.questions) {
        if (question is FillInBlankQuestion) {
          questionStats['fillInBlank']['total'] =
              questionStats['fillInBlank']['total'] + 1;
          if (question.isCorrect) {
            questionStats['fillInBlank']['correct'] =
                questionStats['fillInBlank']['correct'] + 1;
          } else {
            // For incorrect fill-in-blank questions
            incorrectAnswers.add({
              'word': question.card.word,
              'questionType': 'fillInBlank',
              'correctAnswer': question.blankWord,
              'userAnswer':
                  'Incorrect answer', // We don't track the exact user input
              'context': question.sentence,
            });
          }
        } else if (question is MatchDefinitionQuestion) {
          questionStats['matchDefinition']['total'] =
              questionStats['matchDefinition']['total'] + 1;
          if (question.isCorrect) {
            questionStats['matchDefinition']['correct'] =
                questionStats['matchDefinition']['correct'] + 1;
          } else {
            // For incorrect match definition questions
            incorrectAnswers.add({
              'word': question.card.word,
              'questionType': 'matchDefinition',
              'correctAnswer': question.card.word,
              'userAnswer':
                  'Incorrect choice', // We don't track which wrong option was selected
              'context': question.definition,
            });
          }
        } else if (question is TrueFalseQuestion) {
          questionStats['trueFalse']['total'] =
              questionStats['trueFalse']['total'] + 1;
          if (question.isCorrect) {
            questionStats['trueFalse']['correct'] =
                questionStats['trueFalse']['correct'] + 1;
          } else {
            // For incorrect true/false questions
            incorrectAnswers.add({
              'word': question.card.word,
              'questionType': 'trueFalse',
              'correctAnswer': question.answer ? 'True' : 'False',
              'userAnswer': question.isCorrect
                  ? (question.answer
                      ? 'True'
                      : 'False') // If correct, show the same answer
                  : (!question.answer
                      ? 'True'
                      : 'False'), // If incorrect, show the opposite
              'context': question.statement,
            });
          }
        }
      }

      // Save the quiz results to Firebase
      await quizService.saveQuizResult(
        quizId: widget.quiz.id,
        title: widget.quiz.title,
        score: widget.quiz.score,
        totalQuestions: widget.quiz.questions.length,
        isCompleted: widget.quiz.isCompleted,
        questionStats: questionStats,
        quizType: widget.quiz.type.toString().split('.').last,
        incorrectAnswers: incorrectAnswers,
        isChallenge: widget.quiz.isChallenge,
        challengeType: widget.quiz.challengeType?.toString().split('.').last,
      );

      // Refresh the quiz stats provider so the stats screen will update automatically
      ref.invalidate(quizStatsProvider);

      // Trigger dashboard refresh to update resource values
      ref
          .read(dashboardRefreshProvider.notifier)
          .update((state) => DateTime.now());

      // Record quiz completion for ad frequency tracking
      final adService = ref.read(adServiceProvider);
      await adService.recordQuizCompletion();

      // Track quiz completion analytics
      try {
        final isPremium = ref.read(subscriptionStateProvider);
        // Estimate quiz duration (could be improved with actual start time tracking)
        final estimatedDuration = widget.quiz.questions.length *
            30; // 30 seconds per question estimate

        await AnalyticsService.instance.trackQuizCompleted(
          quizType: widget.quiz.mode.toString().split('.').last,
          score: widget.quiz.score,
          totalQuestions: widget.quiz.questions.length,
          timeSpent: estimatedDuration,
          isPremium: isPremium,
        );

        // Update daily metrics
        await ref
            .read(dailyMetricsProvider.notifier)
            .incrementQuizzesCompleted();

        // Track daily active user
        await AnalyticsService.instance
            .track('Daily Active User - Quiz Completed');
      } catch (analyticsError) {
        debugPrint('Analytics tracking error: $analyticsError');
      }

      setState(() {
        _isCompleted = true;
        _isSaving = false;
      });

      // Show ad after quiz completion (with delay for better UX)
      Future.delayed(const Duration(milliseconds: 500), () async {
        if (mounted) {
          final isPremium = ref.read(subscriptionStateProvider);
          await adService.showAdIfEligible(
            AdTrigger.quizCompletion,
            isPremium,
            onAdShown: () {
              debugPrint('Quiz completion ad shown');
            },
            onAdClosed: () {
              debugPrint('Quiz completion ad closed');
            },
            onAdFailed: () {
              debugPrint('Quiz completion ad failed to show');
            },
            onSkipped: () {
              debugPrint('Quiz completion ad skipped');
            },
          );
        }
      });
    } catch (e) {
      debugPrint('Error saving quiz results: $e');
      setState(() {
        _isSaving = false;
        _isCompleted = true; // Still mark as completed to allow navigation
      });
    }
  }

  Future<VocabCard> _updateCardMastery(VocabCard card, bool wasCorrect) async {
    try {
      // Calculate new values
      final newCorrectAnswers =
          wasCorrect ? card.correctAnswers + 1 : card.correctAnswers;
      final cappedCorrectAnswers =
          newCorrectAnswers > 10 ? 10 : newCorrectAnswers;

      // Calculate new mastery level (1-10 scale)
      final newMasteryLevel = (cappedCorrectAnswers / 10 * 10).ceil();

      // Create the updated card
      final updatedCard = card.copyWith(
        masteryLevel: newMasteryLevel,
        correctAnswers: cappedCorrectAnswers,
        totalAnswers: card.totalAnswers + 1,
        lastReviewedAt: DateTime.now(),
      );

      // Update the card in Firestore
      await FirebaseService().updateVocabCard(
        card.id,
        {
          'masteryLevel': newMasteryLevel,
          'correctAnswers': cappedCorrectAnswers,
          'totalAnswers': updatedCard.totalAnswers,
          'lastReviewedAt': DateTime.now(),
        },
      );

      // Check if the card has been mastered and award points if needed
      if (cappedCorrectAnswers == 10 && card.correctAnswers < 10) {
        await PointsManager().awardMasteryPoints(card);
      }

      return updatedCard;
    } catch (e) {
      debugPrint('Error updating card mastery: $e');
      return card;
    }
  }

  @override
  Widget build(BuildContext context) {
    final double percentCorrect =
        widget.quiz.score / widget.quiz.questions.length * 100;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quiz Results'),
        backgroundColor: percentCorrect >= 70
            ? AppColors.successLight
            : (percentCorrect >= 40
                ? AppColors.warningLight
                : AppColors.failureLight),
        foregroundColor: AppColors.white,
        centerTitle: true,
      ),
      body: _isSaving
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Saving your results...',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Updating vocabulary mastery',
                    style: TextStyle(fontSize: 14, color: AppColors.grey),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Results header with score and animation
                  _buildResultsHeader(percentCorrect),

                  // XP Gained section
                  if (_cardsWithXpGain.isNotEmpty) _buildXpGainedSection(),

                  // Question results list
                  _buildQuestionResultsSection(),

                  // Action buttons
                  _buildActionButtons(theme),
                ],
              ),
            ),
    );
  }

  String _buildResultsBreakdown() {
    final correctCount = widget.quiz.questions.where((q) => q.isCorrect).length;
    final incorrectCount =
        widget.quiz.questions.where((q) => q.isAnswered && !q.isCorrect).length;
    final skippedCount =
        widget.quiz.questions.where((q) => !q.isAnswered).length;

    List<String> parts = [];
    if (correctCount > 0) parts.add('$correctCount correct');
    if (incorrectCount > 0) parts.add('$incorrectCount incorrect');
    if (skippedCount > 0) parts.add('$skippedCount skipped');

    return parts.join(' • ');
  }

  Widget _buildResultsHeader(double percentCorrect) {
    final isChallengeMode = widget.quiz.mode == QuizMode.challenge;
    final pointsEarned = isChallengeMode
        ? widget.quiz.questions.where((q) => q.isCorrect).length *
            PointsManager.pointsForCorrectAnswer
        : 0;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            percentCorrect >= 70
                ? AppColors.successLight.withAlpha(178)
                : (percentCorrect >= 40)
                    ? AppColors.warningLight.withAlpha(178)
                    : AppColors.failureLight.withAlpha(178),
            percentCorrect >= 70
                ? AppColors.successLight
                : (percentCorrect >= 40
                    ? AppColors.warningLight
                    : AppColors.failureLight),
          ],
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 16),
          _buildCircularScoreIndicator(),
          const SizedBox(height: 16),

          // Points earned (only in challenge mode)
          if (isChallengeMode) ...[
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(51),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '+$pointsEarned Points',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Text(
                'You earn ${PointsManager.pointsForCorrectAnswer} points for each correct answer in Challenge Mode!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Score display
          Text(
            '${widget.quiz.score}/${widget.quiz.questions.length} Correct',
            style: const TextStyle(
              fontSize: 18,
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          // Show breakdown of results
          Text(
            _buildResultsBreakdown(),
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.white,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 8),
          _buildFeedbackText(percentCorrect),
        ],
      ),
    );
  }

  Widget _buildXpGainedSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Vocabulary XP Gained',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._cardsWithXpGain.map((info) => _buildXpGainCard(info)),
        ],
      ),
    );
  }

  Widget _buildQuestionResultsSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Question Results',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.quiz.questions
              .map((question) => _buildQuestionResultCard(question)),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 32.0,
        horizontal: 16.0,
      ),
      child: Column(
        children: [
          ElevatedButton.icon(
            onPressed: () {
              // Clear quiz state and generate a new quiz
              ref.read(currentQuizProvider.notifier).state = null;
              ref.read(currentQuestionIndexProvider.notifier).state = 0;

              // Navigate back to quiz screen
              context.pushNamed('quiz');
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Take Another Quiz'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.getButtonBackgroundColor(
                  Theme.of(context).brightness),
              foregroundColor: AppColors.getButtonForegroundColor(
                  Theme.of(context).brightness),
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
            ),
          ),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () async {
              // Clear quiz state before navigating home
              ref.read(currentQuizProvider.notifier).state = null;
              ref.read(currentQuestionIndexProvider.notifier).state = 0;

              // Pop any open dialogs first
              if (context.mounted) {
                Navigator.of(context, rootNavigator: true)
                    .popUntil((route) => route.isFirst);
              }

              // Then navigate to home
              if (context.mounted) {
                context.goNamed(RouteNames.home);
              }
            },
            icon: const Icon(Icons.home),
            label: const Text('Return to Home'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () async {
                    // Record stats navigation for ad frequency tracking
                    final adService = ref.read(adServiceProvider);
                    await adService.recordStatsNavigation();

                    // Show ad if eligible before navigating to stats
                    final isPremium = ref.read(subscriptionStateProvider);
                    await adService.showAdIfEligible(
                      AdTrigger.statsNavigation,
                      isPremium,
                      onAdShown: () {
                        debugPrint('Stats navigation ad shown');
                      },
                      onAdClosed: () {
                        debugPrint('Stats navigation ad closed');
                        // Navigate to stats after ad is closed
                        if (context.mounted) {
                          context.pushNamed(RouteNames.quizStats);
                        }
                      },
                      onAdFailed: () {
                        debugPrint('Stats navigation ad failed to show');
                        // Navigate to stats if ad fails
                        if (context.mounted) {
                          context.pushNamed(RouteNames.quizStats);
                        }
                      },
                      onSkipped: () {
                        debugPrint('Stats navigation ad skipped');
                        // Navigate to stats if ad is skipped
                        if (context.mounted) {
                          context.pushNamed(RouteNames.quizStats);
                        }
                      },
                    );
                  },
                  icon: const Icon(Icons.analytics),
                  label: const Text('View Stats'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    context.pushNamed(RouteNames.leaderboard);
                  },
                  icon: const Icon(Icons.leaderboard),
                  label: const Text('Leaderboard'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCircularScoreIndicator() {
    final size = 120.0;
    final strokeWidth = 12.0;

    return AnimatedBuilder(
      animation: _scoreAnimation,
      builder: (context, child) {
        return SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              SizedBox(
                width: size,
                height: size,
                child: CircularProgressIndicator(
                  value: _scoreAnimation.value / 100,
                  strokeWidth: strokeWidth,
                  backgroundColor: AppColors.white.withAlpha(76),
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                ),
              ),
              Center(
                child: Text(
                  '${_scoreAnimation.value.toInt()}%',
                  style: const TextStyle(
                    color: AppColors.white,
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeedbackText(double percentCorrect) {
    String feedbackText;
    IconData feedbackIcon;

    if (percentCorrect >= 90) {
      feedbackText = "Outstanding!";
      feedbackIcon = Icons.auto_awesome;
    } else if (percentCorrect >= 75) {
      feedbackText = "Great job!";
      feedbackIcon = Icons.thumb_up;
    } else if (percentCorrect >= 50) {
      feedbackText = "Good effort!";
      feedbackIcon = Icons.trending_up;
    } else if (percentCorrect >= 25) {
      feedbackText = "Keep practicing!";
      feedbackIcon = Icons.fitness_center;
    } else {
      feedbackText = "Don't give up!";
      feedbackIcon = Icons.emoji_emotions;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(51),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(feedbackIcon, color: AppColors.white),
          const SizedBox(width: 8),
          Text(
            feedbackText,
            style: const TextStyle(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildXpGainCard(XpGainInfo info) {
    final previousMastery = info.card.correctAnswers;
    final newMastery = info.updatedCard.correctAnswers;
    final hasMasteryIncrease = newMastery > previousMastery;

    // Determine if mastery level changed
    final oldMasteryStatus = info.card.getMasteryStatus();
    final newMasteryStatus = info.updatedCard.getMasteryStatus();
    final hasMasteryLevelUp = oldMasteryStatus != newMasteryStatus;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Word
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        info.card.word,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (info.card.type.isNotEmpty)
                        Text(
                          info.card.type.first,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.grey,
                          ),
                        ),
                    ],
                  ),
                ),

                // XP gained indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.warningLight.withAlpha(51),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.warningLight),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: AppColors.warningLight,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '+${info.xpGained}XP',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.warningLight,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Mastery progress
            Row(
              children: [
                // Before progress
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Before',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildProgressBar(previousMastery / 10,
                          info.card.getMasteryStatusColor()),
                      const SizedBox(height: 4),
                      Text(
                        info.card.getMasteryStatusText(),
                        style: TextStyle(
                          fontSize: 12,
                          color: info.card.getMasteryStatusColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // Arrow
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                  child: Icon(
                    Icons.arrow_forward,
                    color: AppColors.grey,
                  ),
                ),

                // After progress
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'After',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildProgressBar(newMastery / 10,
                          info.updatedCard.getMasteryStatusColor()),
                      const SizedBox(height: 4),
                      Text(
                        info.updatedCard.getMasteryStatusText(),
                        style: TextStyle(
                          fontSize: 12,
                          color: info.updatedCard.getMasteryStatusColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Mastery level up indicator
            if (hasMasteryLevelUp)
              Container(
                margin: const EdgeInsets.only(top: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.successLight.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.successLight),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.arrow_upward,
                      color: AppColors.successLight,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Level Up! ${_getMasteryLevelUpText(oldMasteryStatus, newMasteryStatus)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.successLight,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(double progress, Color color) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4),
      child: LinearProgressIndicator(
        value: progress,
        backgroundColor: AppColors.grey.withAlpha(76),
        valueColor: AlwaysStoppedAnimation<Color>(color),
        minHeight: 8,
      ),
    );
  }

  String _getMasteryLevelUpText(MasteryStatus old, MasteryStatus current) {
    if (old == MasteryStatus.wild && current == MasteryStatus.tamed) {
      return 'Wild → Tamed';
    } else if (old == MasteryStatus.tamed &&
        current == MasteryStatus.mastered) {
      return 'Tamed → Mastered';
    }
    return '';
  }

  Widget _buildQuestionResultCard(QuizQuestion question) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Correct/incorrect/skipped icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: !question.isAnswered
                    ? AppColors.grey.withAlpha(26)
                    : question.isCorrect
                        ? AppColors.successLight.withAlpha(26)
                        : AppColors.failureLight.withAlpha(26),
              ),
              child: Icon(
                !question.isAnswered
                    ? Icons.skip_next
                    : question.isCorrect
                        ? Icons.check
                        : Icons.close,
                color: !question.isAnswered
                    ? AppColors.grey
                    : question.isCorrect
                        ? AppColors.successLight
                        : AppColors.failureLight,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Question content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Word
                  Text(
                    question.card.word,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Question content varies based on question type
                  if (!question.isAnswered)
                    _buildSkippedResult()
                  else if (question is FillInBlankQuestion)
                    _buildFillInBlankResult(question)
                  else if (question is MatchDefinitionQuestion)
                    _buildMatchDefinitionResult(question)
                  else if (question is TrueFalseQuestion)
                    _buildTrueFalseResult(question),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFillInBlankResult(FillInBlankQuestion question) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.infoLight.withAlpha(51),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Text(
            'Fill in the Blank',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.infoLight,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          question.sentence,
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 8),
        Text(
          'Correct answer: ${question.blankWord}',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildMatchDefinitionResult(MatchDefinitionQuestion question) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.primaryLight.withAlpha(51),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Text(
            'Match Definition',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.primaryLight,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Definition: ${question.definition}',
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildTrueFalseResult(TrueFalseQuestion question) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.warningLight.withAlpha(51),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Text(
            'True or False',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.warningLight,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Statement: ${question.statement}',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 8),
        Text(
          'Correct answer: ${question.answer ? 'TRUE' : 'FALSE'}',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildSkippedResult() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.grey.withAlpha(51),
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Text(
        'Skipped',
        style: TextStyle(
          fontSize: 12,
          color: AppColors.grey,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// Class to track XP gained for words
class XpGainInfo {
  final VocabCard card;
  final VocabCard updatedCard;
  final int xpGained;

  XpGainInfo({
    required this.card,
    required this.updatedCard,
    required this.xpGained,
  });
}
