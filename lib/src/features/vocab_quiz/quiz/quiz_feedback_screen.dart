// lib/src/features/vocab_quiz/quiz_feedback_screen.dart

import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';

/// Full-screen feedback overlay that appears after answering a question
class QuizFeedbackScreen extends ConsumerStatefulWidget {
  final bool isCorrect;
  final VoidCallback onContinue;
  final QuizQuestion question;
  final String? userAnswer;
  final String? userAnswerDefinition;

  const QuizFeedbackScreen({
    super.key,
    required this.isCorrect,
    required this.onContinue,
    required this.question,
    this.userAnswer,
    this.userAnswerDefinition,
  });

  @override
  ConsumerState<QuizFeedbackScreen> createState() => _QuizFeedbackScreenState();
}

class _QuizFeedbackScreenState extends ConsumerState<QuizFeedbackScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _scaleAnimation;
  String? _correctWord;
  bool _isLoadingCorrectWord = false;

  @override
  void initState() {
    super.initState();

    // Set up animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    // If this is a true/false question with an incorrect answer, find the correct word for the definition
    if (widget.question is TrueFalseQuestion && !widget.isCorrect) {
      _isLoadingCorrectWord = true;
      // Delay to ensure that vocabListStreamProvider is ready
      Future.delayed(Duration.zero, () {
        _findCorrectWordForDefinition();
      });
    }

    // Start the animation
    _animationController.forward();
  }

  void _findCorrectWordForDefinition() async {
    if (widget.question is TrueFalseQuestion) {
      final tfQuestion = widget.question as TrueFalseQuestion;

      // Access vocabulary cards
      final vocabCardsAsync = ref.read(vocabListStreamProvider);

      try {
        // Wait for the vocabulary cards to be available
        vocabCardsAsync.whenData((cards) {
          // Try to find a card whose definition matches the statement in the question
          for (final card in cards) {
            // Look for a card with a definition matching or very similar to the statement
            if (_isDefinitionMatch(card.definition, tfQuestion.statement)) {
              if (mounted) {
                setState(() {
                  _correctWord = card.word;
                  _isLoadingCorrectWord = false;
                });
              }
              break;
            }
          }

          // If we couldn't find a match, use the card's word as fallback
          if (_correctWord == null && mounted) {
            setState(() {
              _correctWord = widget.question.card.word;
              _isLoadingCorrectWord = false;
            });
          }
        });

        // Add a timeout to ensure we don't wait forever
        Future.delayed(const Duration(seconds: 2), () {
          if (_isLoadingCorrectWord && mounted) {
            setState(() {
              _correctWord = widget.question.card.word; // Fallback
              _isLoadingCorrectWord = false;
            });
          }
        });
      } catch (e) {
        debugPrint('Error finding correct word: $e');
        if (mounted) {
          setState(() {
            _correctWord = widget.question.card.word; // Fallback
            _isLoadingCorrectWord = false;
          });
        }
      }
    }
  }

  // Helper to check if definitions are a match (allowing for some flexibility)
  bool _isDefinitionMatch(String def1, String def2) {
    // Simple exact match check
    if (def1.trim().toLowerCase() == def2.trim().toLowerCase()) {
      return true;
    }

    // Check if one is a substring of the other
    final lower1 = def1.toLowerCase();
    final lower2 = def2.toLowerCase();
    if (lower1.contains(lower2) || lower2.contains(lower1)) {
      return true;
    }

    // More sophisticated matching could be added here

    return false;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final correctColor = AppColors.correctLight;
    final incorrectColor = AppColors.incorrectLight;

    // Check if this is a true/false question
    final isTrueFalseQuestion = widget.question is TrueFalseQuestion;

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeInAnimation.value,
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(
                    bottom: 100), // Add padding for bottom bar
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 24),

                    // Feedback icon
                    Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color:
                              widget.isCorrect ? correctColor : incorrectColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: (widget.isCorrect
                                      ? correctColor
                                      : incorrectColor)
                                  .withAlpha(76),blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Icon(
                          widget.isCorrect ? Icons.check : Icons.close,
                          color: AppColors.white,
                          size: 40,
                        ),
                      ),
                    ),

                    const SizedBox(height: 18),

                    // Feedback text
                    Text(
                      widget.isCorrect ? 'Correct!' : 'Incorrect!',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: widget.isCorrect ? correctColor : incorrectColor,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Answer cards
                    // if (!widget.isCorrect)
                    //   _buildUserAnswerCard(isTrueFalseQuestion),

                    if (!widget.isCorrect) const SizedBox(height: 16),

                    // Correct answer card (always show this)
                    _buildCorrectAnswerCard(isTrueFalseQuestion),

                    const SizedBox(height: 32), // Extra spacing at bottom
                  ],
                ),
              ),
            );
          },
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.black.withAlpha(26),//     blurRadius: 8,
          //     offset: const Offset(0, -2),
          //   ),
          // ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton(
                onPressed: _isLoadingCorrectWord ? null : widget.onContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      widget.isCorrect ? correctColor : incorrectColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 18,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 5,
                ),
                child: _isLoadingCorrectWord
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                            color: AppColors.white, strokeWidth: 2),
                      )
                    : const Text(
                        'Continue',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build the user answer card
  // Widget _buildUserAnswerCard(bool isTrueFalseQuestion) {
  //   // For true/false questions, show a simplified user answer card
  //   if (isTrueFalseQuestion) {
  //     return _buildSimpleAnswerCard(
  //       title: 'Your Answer',
  //       word: widget.userAnswer ?? 'Unknown',
  //       definition: null, // No definition for true/false answers
  //       color: AppColors.incorrectLight,
  //       showDefinition: false,
  //     );
  //   } else {
  //     // For other question types, show the regular card with definition
  //     return _buildSimpleAnswerCard(
  //       title: 'Your Answer',
  //       word: widget.userAnswer ?? 'Unknown',
  //       definition: widget.userAnswerDefinition ?? "Definition not available",
  //       color: AppColors.incorrectLight,
  //       showDefinition: true,
  //     );
  //   }
  // }

  // Build the correct answer card with the standard VocabularyCard widget
  Widget _buildCorrectAnswerCard(bool isTrueFalseQuestion) {
    // Calculate scale factor based on screen width
    final screenWidth = MediaQuery.of(context).size.width;
    final scaleFactor = (screenWidth > 600)
        ? 0.9 // For larger screens
        : (screenWidth > 400 ? 0.8 : 0.7); // For medium and small screens

    if (isTrueFalseQuestion) {
      // For true/false questions, handle the display differently
      final tfQuestion = widget.question as TrueFalseQuestion;

      if (_isLoadingCorrectWord) {
        // Show loading state
        return Container(
          width: screenWidth * 0.9,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          height: 180, // Approximate height for consistency
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withAlpha(26),blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // Get the card for the true/false question
      final card = widget.question.card;

      return Transform.scale(
        scale: scaleFactor,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 400, // Maximum width for the card
            maxHeight: MediaQuery.of(context).size.height *
                0.6, // 60% of screen height
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.correctLight,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: const Text(
                  'Correct Answer',
                  style: TextStyle(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              // The actual VocabularyCard with flexible constraints
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      VocabularyCard(
                        card: card,
                        isFlippable: false,
                        showActions: false,
                      ),
                      // Additional info for true/false questions
                      if (tfQuestion.statement.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            '\"${card.word}\" - ${tfQuestion.statement}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).brightness ==
                                      Brightness.light
                                  ? const Color(0xFF757575)
                                  : const Color(0xFFBDBDBD),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // For other question types, show the standard VocabularyCard
      return Transform.scale(
        scale: scaleFactor,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 400, // Maximum width for the card
            maxHeight: MediaQuery.of(context).size.height *
                0.6, // 60% of screen height
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.correctLight,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Text(
                  widget.isCorrect ? 'Your Answer' : 'Correct Answer',
                  style: const TextStyle(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              // The VocabularyCard with flexible constraints
              Flexible(
                child: SingleChildScrollView(
                  child: VocabularyCard(
                    card: widget.question.card,
                    isFlippable: false,
                    showActions: false,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildSimpleAnswerCard({
    required String title,
    required String word,
    required String? definition,
    required Color color,
    required bool showDefinition,
  }) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(26),blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Text(
              title,
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  word,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.black,
                  ),
                ),
                if (showDefinition && definition != null) ...[
                  const SizedBox(height: 8),
                  const Text(
                    'definition',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    definition,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.black,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
