// lib/src/features/vocab_quiz/services/quiz_generation_service.dart

import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/models/quiz_content_models.dart';
import 'package:vocadex/src/features/vocab_quiz/services/quiz_content_storage_service.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Service for generating quizzes using AI
class QuizGenerationService {
  final FirebaseService _firebaseService = FirebaseService();
  final FirebaseVertexAI _vertexAI = FirebaseVertexAI.instance;
  final QuizContentStorageService _quizContentStorage =
      QuizContentStorageService();

  /// Check if the current user is a premium subscriber
  /// This method now requires a WidgetRef to check premium status
  bool isUserPremium(WidgetRef ref) {
    return ref.watch(subscriptionStateProvider);
  }

  /// Generate a mixed quiz with all types of questions
  Future<Quiz?> generateMixedQuiz({
    bool useSelectedCards = false,
    Set<String> selectedCardIds = const {},
    int questionCount = 10, // Add parameter for number of questions
  }) async {
    try {
      // Fetch vocabulary cards
      List<VocabCard> availableCards;

      if (useSelectedCards && selectedCardIds.isNotEmpty) {
        // For premium users with selected cards
        final allCards = await _firebaseService.fetchVocabulary();
        availableCards = allCards
            .where((card) => selectedCardIds.contains(card.id))
            .toList();

        // If not enough cards selected, return null
        if (availableCards.isEmpty) {
          return null;
        }
      } else {
        // For free users or premium users without selection
        availableCards = await _firebaseService.fetchVocabulary();

        // If no cards at all, return null
        if (availableCards.isEmpty) {
          return null;
        }
      }

      // Add more randomization - first shuffle with a random seed based on current time
      final random = DateTime.now().microsecondsSinceEpoch;
      availableCards.sort(
          (a, b) => (a.hashCode * random) % 100 - (b.hashCode * random) % 100);
      availableCards.shuffle();

      // Determine the number of questions based on available cards and provided count
      final int totalQuestions = questionCount;

      // We'll allow the same card to appear in different question types
      // but we'll track which cards have been used for specific question type content
      final Map<String, Set<String>> usedContent = {
        'fillInBlanks': <String>{}, // Track used sentences
        'trueFalse': <String>{}, // Track used definitions
        'spellWord': <String>{}, // Track used content
      };

      // Calculate how many questions of each type to create
      // Aim for roughly equal distribution across three types
      final int questionsPerType = totalQuestions ~/ 3;
      final int fillInBlanksCount = questionsPerType;
      final int trueFalseCount = questionsPerType;
      final int spellWordCount =
          totalQuestions - fillInBlanksCount - trueFalseCount;

      // Create card pools for each question type
      // We can reuse the same word for different question types
      // but we'll ensure different sentences/definitions when possible
      final List<VocabCard> fillInBlanksCards =
          _selectCards(availableCards, fillInBlanksCount);
      final List<VocabCard> trueFalseCards =
          _selectCards(availableCards, trueFalseCount);
      final List<VocabCard> spellWordCards =
          _selectCards(availableCards, spellWordCount);

      // Generate all questions
      final List<QuizQuestion> allQuestions = [];

      // Generate Fill-in-the-Blanks questions (for drag and drop)
      if (fillInBlanksCards.isNotEmpty) {
        try {
          final fillInBlankQuestions = await _generateFillInBlankQuestions(
            fillInBlanksCards,
            availableCards,
            numberOfOptions: 3,
            usedContent: usedContent['fillInBlanks']!,
          );

          allQuestions.addAll(fillInBlankQuestions);
          debugPrint(
              '✅ Generated ${fillInBlankQuestions.length} fill-in-blank questions for training mode');
        } catch (e) {
          debugPrint(
              '❌ SERVICE: Error generating fill-in-blanks questions: $e');
        }
      }

      // Generate True/False questions
      if (trueFalseCards.isNotEmpty) {
        try {
          final trueFalseQuestions = await _generateTrueFalseQuestions(
            trueFalseCards,
            definitionsOnly: true, // Only about definitions
            usedContent: usedContent['trueFalse']!,
          );

          allQuestions.addAll(trueFalseQuestions);
          debugPrint(
              '✅ Generated ${trueFalseQuestions.length} true/false questions for training mode');
        } catch (e) {
          debugPrint('❌ SERVICE: Error generating true/false questions: $e');
        }
      }

      // Generate Spell Word questions
      if (spellWordCards.isNotEmpty) {
        try {
          final spellWordQuestions = await _generateSpellWordQuestions(
            spellWordCards,
            usedContent: usedContent['spellWord']!,
          );

          allQuestions.addAll(spellWordQuestions);
          debugPrint(
              '✅ Generated ${spellWordQuestions.length} spell-word questions for training mode');
        } catch (e) {
          debugPrint('❌ SERVICE: Error generating spell word questions: $e');
        }
      }

      // Check if we have any questions at all
      if (allQuestions.isEmpty) {
        return null;
      }

      // Shuffle the questions for the final quiz order
      allQuestions.sort(
          (a, b) => (a.hashCode * random) % 100 - (b.hashCode * random) % 100);
      allQuestions.shuffle();
      debugPrint('🔍 SERVICE: Shuffled ${allQuestions.length} questions');

      // Create the mixed quiz
      final quiz = Quiz(
        id: FirebaseService.getUID('quizzes'),
        title: 'Vocabulary Quiz',
        type: QuizType.mixed,
        questions: allQuestions,
        createdAt: DateTime.now(),
        totalQuestions: allQuestions.length,
        mode: QuizMode.train, // Explicitly set to training mode
        showFeedback: true, // Show feedback in training mode
        isTimed: false, // Training mode is not timed
      );

      return quiz;
    } catch (e, stackTrace) {
      debugPrint('❌ SERVICE: Error generating mixed quiz: $e');
      debugPrint('❌ SERVICE: Stack trace: $stackTrace');
      throw Exception('Failed to generate quiz: $e');
    }
  }

  /// Generate a mixed quiz using pre-generated content from database
  /// This is the new preferred method that uses pre-generated quiz content
  Future<Quiz?> generateMixedQuizFromDatabase({
    bool useSelectedCards = false,
    Set<String> selectedCardIds = const {},
    int questionCount = 10,
  }) async {
    try {
      debugPrint('🎯 Generating quiz from pre-generated database content');

      // Fetch vocabulary cards
      List<VocabCard> availableCards;

      if (useSelectedCards && selectedCardIds.isNotEmpty) {
        // For premium users with selected cards
        final allCards = await _firebaseService.fetchVocabulary();
        availableCards = allCards
            .where((card) => selectedCardIds.contains(card.id))
            .toList();

        if (availableCards.isEmpty) {
          debugPrint('❌ No selected cards available');
          return null;
        }
      } else {
        // For free users or premium users without selection
        availableCards = await _firebaseService.fetchVocabulary();

        if (availableCards.isEmpty) {
          debugPrint('❌ No vocabulary cards available');
          return null;
        }
      }

      // Shuffle and limit cards for quiz
      availableCards.shuffle();
      final cardsForQuiz = availableCards
          .take(questionCount * 2)
          .toList(); // Get extra cards for variety

      // Get word IDs for database lookup
      final wordIds = cardsForQuiz
          .map((card) => VocabCard.createGlobalId(card.word))
          .toList();

      // Fetch pre-generated quiz content from database
      final quizContent = await _quizContentStorage.getRandomQuizContent(
        wordIds: wordIds,
        questionCount: questionCount,
        questionTypeDistribution: {
          'fillInBlank': 4,
          'trueFalse': 3,
          'spellWord': 3,
        },
      );

      if (quizContent.isEmpty) {
        debugPrint(
            '⚠️ No pre-generated content found, falling back to AI generation');
        return await generateMixedQuiz(
          useSelectedCards: useSelectedCards,
          selectedCardIds: selectedCardIds,
          questionCount: questionCount,
        );
      }

      // Convert pre-generated content to quiz questions
      final List<QuizQuestion> allQuestions = [];

      for (final content in quizContent) {
        final QuizQuestion? question =
            await _convertContentToQuizQuestion(content, availableCards);
        if (question != null) {
          allQuestions.add(question);
        }
      }

      if (allQuestions.isEmpty) {
        debugPrint(
            '⚠️ Failed to convert content to questions, falling back to AI generation');
        return await generateMixedQuiz(
          useSelectedCards: useSelectedCards,
          selectedCardIds: selectedCardIds,
          questionCount: questionCount,
        );
      }

      // Final shuffle
      allQuestions.shuffle();

      // Create the quiz
      final quiz = Quiz(
        id: FirebaseService.getUID('quizzes'),
        title: 'Vocabulary Quiz',
        type: QuizType.mixed,
        questions: allQuestions.take(questionCount).toList(),
        createdAt: DateTime.now(),
        totalQuestions: allQuestions.length.clamp(0, questionCount),
        mode: QuizMode.train,
        showFeedback: true,
        isTimed: false,
      );

      debugPrint(
          '✅ Generated quiz from database with ${quiz.questions.length} questions');
      return quiz;
    } catch (e) {
      debugPrint('❌ Error generating quiz from database: $e');
      // Fallback to original AI generation
      return await generateMixedQuiz(
        useSelectedCards: useSelectedCards,
        selectedCardIds: selectedCardIds,
        questionCount: questionCount,
      );
    }
  }

  /// Generate a challenge quiz
  Future<Quiz?> generateChallengeQuiz({
    required ChallengeType challengeType,
  }) async {
    try {
      // Fetch all vocabulary cards
      final availableCards = await _firebaseService.fetchVocabulary();

      // If no cards at all, return null
      if (availableCards.isEmpty) {
        debugPrint('❌ SERVICE: No cards available for challenge quiz');
        return null;
      }

      // Add randomization
      final random = DateTime.now().microsecondsSinceEpoch;
      availableCards.sort(
          (a, b) => (a.hashCode * random) % 100 - (b.hashCode * random) % 100);
      availableCards.shuffle();

      // Filter out cards with missing data
      final validCards = availableCards
          .where((card) =>
              card.word.isNotEmpty &&
              card.definition.isNotEmpty &&
              card.word.length > 1) // Ensure words are valid
          .toList();

      if (validCards.isEmpty) {
        debugPrint(
            '❌ SERVICE: No valid cards with complete data for challenge quiz');
        return null;
      }

      debugPrint(
          '✅ SERVICE: Found ${validCards.length} valid cards for challenge quiz');

      // Challenge quizzes have 15 questions
      const int totalQuestions = 15;

      // We'll allow the same card to appear in different question types
      // but we'll track which cards have been used for specific question type content
      final Map<String, Set<String>> usedContent = {
        'trueFalse': <String>{}, // Track used definitions
        'fillInBlank': <String>{}, // Track used sentences
      };

      // Generate all questions
      final List<QuizQuestion> allQuestions = [];

      // Select cards based on challenge type
      List<VocabCard> selectedCards = _selectCards(validCards,
          totalQuestions * 2); // Select extra cards in case some fail

      switch (challengeType) {
        case ChallengeType.mixed:
          // For mixed challenges, split between true/false and fill-in-blank
          final int trueFalseCount = totalQuestions ~/ 2;
          final int fillInBlankCount = totalQuestions - trueFalseCount;

          final trueFalseCards = _selectCards(
              selectedCards, trueFalseCount * 2); // Double the count for safety
          final fillInBlankCards =
              _selectCards(selectedCards, fillInBlankCount * 2);

          // Generate True/False questions
          try {
            debugPrint(
                '🔍 SERVICE: Generating true/false questions for challenge');
            final trueFalseQuestions = await _generateTrueFalseQuestions(
              trueFalseCards,
              definitionsOnly: true,
              usedContent: usedContent['trueFalse']!,
            );

            // Validate all true/false questions
            final validTrueFalseQuestions = trueFalseQuestions.where((q) {
              if (q is TrueFalseQuestion) {
                return q.statement.isNotEmpty &&
                    q.card.word.isNotEmpty &&
                    q.statement.length > 5;
              }
              return false;
            }).toList();

            allQuestions.addAll(validTrueFalseQuestions);
            debugPrint(
                '✅ Generated ${validTrueFalseQuestions.length} valid true/false questions for challenge');
          } catch (e, stackTrace) {
            debugPrint('❌ SERVICE: Error generating true/false questions: $e');
            debugPrint('❌ SERVICE: Stack trace: $stackTrace');
          }

          // Generate Fill-in-the-Blank questions
          try {
            debugPrint(
                '🔍 SERVICE: Generating fill-in-blank questions for challenge');
            final fillInBlankQuestions = await _generateFillInBlankQuestions(
              fillInBlankCards,
              validCards,
              numberOfOptions: 3,
              usedContent: usedContent['fillInBlank']!,
            );

            // Validate all fill-in-blank questions
            final validFillInBlankQuestions = fillInBlankQuestions.where((q) {
              if (q is FillInBlankQuestion) {
                return q.sentence.isNotEmpty &&
                    q.sentence.contains('_____') &&
                    q.options.isNotEmpty &&
                    q.blankWord.isNotEmpty;
              }
              return false;
            }).toList();

            allQuestions.addAll(validFillInBlankQuestions);
            debugPrint(
                '✅ Generated ${validFillInBlankQuestions.length} valid fill-in-blank questions for challenge');
          } catch (e, stackTrace) {
            debugPrint(
                '❌ SERVICE: Error generating fill-in-blank questions: $e');
            debugPrint('❌ SERVICE: Stack trace: $stackTrace');
          }
          break;

        case ChallengeType.trueFalse:
          // For true/false challenges, only generate true/false questions
          try {
            final trueFalseQuestions = await _generateTrueFalseQuestions(
              selectedCards,
              definitionsOnly: true,
              usedContent: usedContent['trueFalse']!,
            );

            // Validate all true/false questions
            final validTrueFalseQuestions = trueFalseQuestions.where((q) {
              if (q is TrueFalseQuestion) {
                return q.statement.isNotEmpty &&
                    q.card.word.isNotEmpty &&
                    q.statement.length > 5;
              }
              return false;
            }).toList();

            allQuestions.addAll(validTrueFalseQuestions);
            debugPrint(
                '✅ Generated ${validTrueFalseQuestions.length} valid true/false questions for challenge type TrueFalse');
          } catch (e, stackTrace) {
            debugPrint('❌ SERVICE: Error generating true/false questions: $e');
            debugPrint('❌ SERVICE: Stack trace: $stackTrace');
          }
          break;

        case ChallengeType.dragAndDrop:
          // For drag-and-drop challenges, only generate fill-in-blank questions
          try {
            final fillInBlankQuestions = await _generateFillInBlankQuestions(
              selectedCards,
              validCards,
              numberOfOptions: 3,
              usedContent: usedContent['fillInBlank']!,
            );

            // Validate all fill-in-blank questions
            final validFillInBlankQuestions = fillInBlankQuestions.where((q) {
              if (q is FillInBlankQuestion) {
                return q.sentence.isNotEmpty &&
                    q.sentence.contains('_____') &&
                    q.options.isNotEmpty &&
                    q.blankWord.isNotEmpty;
              }
              return false;
            }).toList();

            allQuestions.addAll(validFillInBlankQuestions);
            debugPrint(
                '✅ Generated ${validFillInBlankQuestions.length} valid fill-in-blank questions for challenge type DragAndDrop');
          } catch (e, stackTrace) {
            debugPrint(
                '❌ SERVICE: Error generating fill-in-blank questions: $e');
            debugPrint('❌ SERVICE: Stack trace: $stackTrace');
          }
          break;
      }

      // Check if we have any questions at all
      if (allQuestions.isEmpty) {
        debugPrint(
            '❌ SERVICE: No valid questions generated for challenge quiz');
        return null;
      }

      // Log question types for debugging
      int trueFalseCount = 0;
      int fillInBlankCount = 0;
      int otherCount = 0;

      for (final question in allQuestions) {
        if (question is TrueFalseQuestion) {
          trueFalseCount++;
        } else if (question is FillInBlankQuestion) {
          fillInBlankCount++;
        } else {
          otherCount++;
        }
      }

      debugPrint(
          '📋 Challenge quiz question types: TrueFalse=$trueFalseCount, FillInBlank=$fillInBlankCount, Other=$otherCount');

      // Shuffle the questions for the final quiz order
      allQuestions.sort(
          (a, b) => (a.hashCode * random) % 100 - (b.hashCode * random) % 100);
      allQuestions.shuffle();

      // Take only the required number of questions or as many as we have
      final actualQuestionCount = allQuestions.length > totalQuestions
          ? totalQuestions
          : allQuestions.length;

      final finalQuestions = allQuestions.take(actualQuestionCount).toList();

      debugPrint(
          '🔍 SERVICE: Created ${finalQuestions.length} challenge questions');

      // Create the challenge quiz
      final quiz = Quiz(
        id: FirebaseService.getUID('quizzes'),
        title: 'Challenge Quiz',
        type: QuizType.mixed,
        questions: finalQuestions,
        createdAt: DateTime.now(),
        totalQuestions: finalQuestions.length,
        isChallenge: true,
        challengeType: challengeType,
        isTimed: true,
        timeLimit: 45, // 45 seconds for challenge quizzes
        showFeedback: false, // No feedback for challenge quizzes
        mode: QuizMode.challenge,
      );

      return quiz;
    } catch (e, stackTrace) {
      debugPrint('❌ SERVICE: Error generating challenge quiz: $e');
      debugPrint('❌ SERVICE: Stack trace: $stackTrace');
      return null;
    }
  }

  /// Helper method to select cards for a specific question type
  List<VocabCard> _selectCards(List<VocabCard> availableCards, int count) {
    // Shuffle to ensure randomness
    final shuffledCards = List<VocabCard>.from(availableCards);
    shuffledCards.shuffle();

    // Return the requested number of cards or all available if fewer
    return shuffledCards.take(count).toList();
  }

  /// Generate fill-in-the-blank questions for a subset of cards
  Future<List<QuizQuestion>> _generateFillInBlankQuestions(
      List<VocabCard> quizCards, List<VocabCard> allCards,
      {int numberOfOptions = 3, Set<String> usedContent = const {}}) async {
    final questions = <QuizQuestion>[];

    for (final card in quizCards) {
      // Generate a sentence with a blank for the word
      final sentence =
          await _generateSentenceWithBlank(card, usedContent: usedContent);

      // Create options (including the correct answer)
      final options = _generateOptions(allCards, card, numberOfOptions - 1);

      questions.add(
        FillInBlankQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          sentence: sentence,
          blankWord: card.word,
          options: options,
        ),
      );
    }

    return questions;
  }

  /// Generate match definition questions for a subset of cards
  Future<List<QuizQuestion>> _generateMatchDefinitionQuestions(
      List<VocabCard> quizCards, List<VocabCard> allCards,
      {int numberOfOptions = 3}) async {
    final questions = <QuizQuestion>[];

    for (final card in quizCards) {
      // Use the card's definition
      final definition = card.definition;

      // Create options (including the correct answer)
      final options = _generateOptions(allCards, card, numberOfOptions - 1);

      questions.add(
        MatchDefinitionQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          definition: definition,
          options: options,
        ),
      );
    }

    return questions;
  }

  /// Generate true/false questions for a subset of cards
  Future<List<QuizQuestion>> _generateTrueFalseQuestions(
      List<VocabCard> quizCards,
      {bool definitionsOnly = false,
      Set<String> usedContent = const {}}) async {
    final questions = <QuizQuestion>[];
    final random = DateTime.now().millisecondsSinceEpoch;

    for (int i = 0; i < quizCards.length; i++) {
      final card = quizCards[i];

      // Use different seed for each card to increase randomness
      final cardSeed = random + card.hashCode;

      // Decide if this question will be true or false
      // Make it more random by using the card's characteristics in the calculation
      final isTrue = cardSeed % 2 == 0;

      // Initialize statement with a default value that will be overwritten
      String statement = "";

      if (definitionsOnly) {
        // For true/false questions, we'll only focus on definition matching
        if (isTrue) {
          // For true statements, use the actual definition of the word without mentioning the word
          statement = card.definition;

          // Check if we've already used this definition in another true/false question
          if (usedContent.contains(statement)) {
            // If definition already used, create a slightly modified version
            // Make sure to only include the definition without "The word X means:"
            final modifiedDef =
                _createSlightlyModifiedDefinition(card.definition);
            statement = modifiedDef;
          }
        } else {
          // For false statements, use a definition from another card
          final otherCards = quizCards.where((c) => c.id != card.id).toList();

          if (otherCards.isNotEmpty) {
            // Find a definition we haven't used yet if possible
            otherCards.shuffle();

            bool foundUnused = false;
            for (final otherCard in otherCards) {
              // Use only the definition without any prefixes
              final potentialStatement = otherCard.definition;
              if (!usedContent.contains(potentialStatement)) {
                statement = potentialStatement;
                foundUnused = true;
                break;
              }
            }

            // If all definitions used, modify one of them
            if (!foundUnused) {
              statement = _createModifiedDefinition(
                  otherCards.first.definition, cardSeed);
            }
          } else {
            // If no other cards available, modify the current definition
            statement = _createModifiedDefinition(card.definition, cardSeed);
          }
        }
      } else {
        // This branch is for backward compatibility but won't be used in new generation
        if (isTrue) {
          // For true questions, use the actual definition
          statement = card.definition;
        } else {
          // For false questions, use a definition from another random card
          final otherCards = quizCards.where((c) => c.id != card.id).toList();
          if (otherCards.isNotEmpty) {
            otherCards.shuffle();
            statement = otherCards.first.definition;
          } else {
            // If no other cards available, modify the current definition
            statement = _createModifiedDefinition(card.definition, cardSeed);
          }
        }
      }

      // Track the statement to avoid repeating identical content
      usedContent.add(statement);

      questions.add(
        TrueFalseQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          statement: statement,
          answer: isTrue,
        ),
      );
    }

    return questions;
  }

  /// Creates a slightly modified version of a definition without changing its meaning
  String _createSlightlyModifiedDefinition(String definition) {
    // Remove any leading/trailing whitespace
    final trimmed = definition.trim();

    // Check if the definition starts with an article
    if (trimmed.toLowerCase().startsWith('a ') ||
        trimmed.toLowerCase().startsWith('an ') ||
        trimmed.toLowerCase().startsWith('the ')) {
      // If it does, capitalize the first letter after the article
      final parts = trimmed.split(' ');
      if (parts.length > 1) {
        parts[1] = parts[1][0].toUpperCase() + parts[1].substring(1);
        return parts.join(' ');
      }
    }

    // If it doesn't start with an article, add "Being" or "The act of" occasionally
    final random = DateTime.now().millisecondsSinceEpoch % 3;
    if (random == 0) {
      return 'Being ' + trimmed;
    } else if (random == 1 && !trimmed.toLowerCase().startsWith('the act of')) {
      return 'The act of ' + trimmed;
    }

    // If none of the above apply, return the original
    return definition;
  }

  /// Helper method to create modified definitions for false questions
  String _createModifiedDefinition(String original, int seed) {
    final words = original.split(' ');
    if (words.length > 3) {
      // Choose a strategy based on the seed
      final strategyIndex = seed % 3;

      switch (strategyIndex) {
        case 0:
          // Replace a word with its opposite
          final replaceIndex = words.length ~/ 2;
          words[replaceIndex] = _getOppositeWord(words[replaceIndex]);
          break;
        case 1:
          // Negate a key part of the definition
          if (!original.contains('not ')) {
            // Find a good place to insert "not"
            for (int i = 0; i < words.length - 1; i++) {
              if (['a', 'an', 'the', 'is', 'are', 'be', 'to']
                  .contains(words[i].toLowerCase())) {
                words.insert(i + 1, 'not');
                break;
              }
            }
          } else {
            // If already contains "not", remove it
            final notIndex = words.indexWhere((w) => w.toLowerCase() == 'not');
            if (notIndex >= 0) {
              words.removeAt(notIndex);
            } else {
              // Fallback to replacing a word
              final replaceIndex = words.length ~/ 2;
              words[replaceIndex] = _getOppositeWord(words[replaceIndex]);
            }
          }
          break;
        case 2:
          // Swap two adjacent words to change meaning
          if (words.length > 3) {
            final index = seed % (words.length - 1);
            final temp = words[index];
            words[index] = words[index + 1];
            words[index + 1] = temp;
          } else {
            // Not enough words to swap, use opposite
            final replaceIndex = words.length ~/ 2;
            words[replaceIndex] = _getOppositeWord(words[replaceIndex]);
          }
          break;
      }
    } else {
      // For short definitions, just go with "opposite of"
      return 'The opposite of $original';
    }

    return words.join(' ');
  }

  /// Generate a sentence with a blank for the given word
  Future<String> _generateSentenceWithBlank(VocabCard card,
      {Set<String> usedContent = const {}}) async {
    try {
      // Check if the card already has examples
      if (card.examples.isNotEmpty && card.examples.length > 1) {
        // If multiple examples exist, find one that hasn't been used
        final unusedExamples =
            card.examples.where((ex) => !usedContent.contains(ex)).toList();

        if (unusedExamples.isNotEmpty) {
          // Use an unused example
          final example = unusedExamples[0];
          final wordPattern = RegExp(card.word, caseSensitive: false);
          final result = example.replaceFirst(wordPattern, '_____');

          // Add to used content to avoid repeating
          usedContent.add(example);
          return result;
        } else {
          // All examples have been used, try to generate a new one
          final random = DateTime.now().millisecondsSinceEpoch;
          final exampleIndex = random % card.examples.length;
          final example = card.examples[exampleIndex];
          final wordPattern = RegExp(card.word, caseSensitive: false);
          return example.replaceFirst(wordPattern, '_____');
        }
      } else if (card.examples.isNotEmpty) {
        // If only one example, check if it's been used
        final example = card.examples[0];

        if (!usedContent.contains(example)) {
          // Use the example if it hasn't been used
          final wordPattern = RegExp(card.word, caseSensitive: false);
          final result = example.replaceFirst(wordPattern, '_____');

          // Add to used content
          usedContent.add(example);
          return result;
        } else {
          // Example already used, generate a new sentence
          return await _generateNewSentence(card, usedContent);
        }
      } else {
        // No examples, generate a new sentence
        return await _generateNewSentence(card, usedContent);
      }
    } catch (e, stackTrace) {
      // Catch all errors/exceptions
      debugPrint(
          'Error generating sentence with blank for word "${card.word}": $e');
      debugPrint('Stack trace: $stackTrace');

      // Fallback in case of error with more variety
      final fallbackFormats = [
        'The meaning of _____ is: ${card.definition}',
        'In English, _____ refers to: ${card.definition}',
        'The word _____ can be defined as: ${card.definition}'
      ];

      // Find an unused fallback if possible
      for (final format in fallbackFormats) {
        if (!usedContent.contains(format)) {
          usedContent.add(format);
          return format;
        }
      }

      // If all used, pick one randomly
      final random = DateTime.now().millisecondsSinceEpoch;
      return fallbackFormats[random % fallbackFormats.length];
    }
  }

  /// Helper method to generate a new sentence using the AI model
  Future<String> _generateNewSentence(
      VocabCard card, Set<String> usedContent) async {
    final model = _vertexAI.generativeModel(model: 'gemini-2.0-flash');

    // Create a more dynamic prompt for more varied sentences
    final promptStyles = [
      '''Create a simple sentence using the word "${card.word}" (${card.type.join(', ')}).
Then provide the same sentence with the word replaced by a blank (___).
Make sure this is DIFFERENT from any common examples.
Make the sentence natural and conversational.
Output format: "Sentence with blank: [Your sentence with blank]"''',
      '''Write a unique sentence that demonstrates the meaning of "${card.word}" (${card.type.join(', ')}).
Then provide the same sentence with the word replaced by a blank (___).
The sentence should be creative and different from typical examples.
Make the sentence educational and clear.
Output format: "Sentence with blank: [Your sentence with blank]"''',
      '''Compose an original practical example sentence using the word "${card.word}" (${card.type.join(', ')}).
Then provide the same sentence with the word replaced by a blank (___).
Create something different from standard examples.
Make the context realistic and everyday.
Output format: "Sentence with blank: [Your sentence with blank]"'''
    ];

    // Select a random prompt style
    final random = DateTime.now().millisecondsSinceEpoch;
    final promptStyle = promptStyles[random % promptStyles.length];

    final prompt = [Content.text(promptStyle)];

    final response = await model.generateContent(prompt);
    final text = response.text ?? '';

    // Extract the sentence with blank
    final regex = RegExp(r'Sentence with blank: (.+)');
    final match = regex.firstMatch(text);

    if (match != null && match.groupCount >= 1) {
      final sentence = match.group(1)!;
      usedContent.add(sentence);
      return sentence;
    }

    // Fallback: create a simple blank manually, with a bit more variety
    final fallbackFormats = [
      'The meaning of _____ is: ${card.definition}',
      'In English, _____ refers to: ${card.definition}',
      'The word _____ can be defined as: ${card.definition}'
    ];

    // Find an unused fallback
    for (final format in fallbackFormats) {
      if (!usedContent.contains(format)) {
        usedContent.add(format);
        return format;
      }
    }

    return fallbackFormats[random % fallbackFormats.length];
  }

  /// Get a simple opposite word for fallback false statements
  String _getOppositeWord(String word) {
    const opposites = {
      'good': 'bad',
      'big': 'small',
      'high': 'low',
      'up': 'down',
      'in': 'out',
      'over': 'under',
      'more': 'less',
      'many': 'few',
      'strong': 'weak',
      'fast': 'slow',
      'hot': 'cold',
      'easy': 'hard',
      'early': 'late',
      'new': 'old',
      'young': 'old',
      'right': 'wrong',
      'true': 'false',
      'happy': 'sad',
      'light': 'dark',
      'open': 'closed',
      'first': 'last',
      'begin': 'end',
      'front': 'back',
      'top': 'bottom',
      'above': 'below',
      'success': 'failure',
      'positive': 'negative',
      'active': 'passive',
    };

    return opposites[word.toLowerCase()] ?? 'not $word';
  }

  /// Generate a list of options including the correct answer
  List<String> _generateOptions(List<VocabCard> allCards, VocabCard correctCard,
      int numberOfWrongOptions) {
    // Start with the correct answer
    final options = <String>[correctCard.word];

    // Create a list of cards excluding the correct one
    final otherCards =
        allCards.where((card) => card.id != correctCard.id).toList();
    otherCards.shuffle();

    // If we don't have enough cards for different options,
    // we'll create some made-up similar words
    if (otherCards.length < numberOfWrongOptions) {
      // Add real options first
      for (int i = 0; i < otherCards.length && i < numberOfWrongOptions; i++) {
        // Make sure the option is not already in the list
        if (!options.contains(otherCards[i].word)) {
          options.add(otherCards[i].word);
        }
      }

      // If we still need more options, create similar-looking fake words
      while (options.length <= numberOfWrongOptions) {
        final word = correctCard.word;
        final fakeOptions = _generateSimilarWords(
            word,
            numberOfWrongOptions -
                options.length +
                2); // Generate extra options

        // Add only unique fake options that don't match existing options
        for (final fakeWord in fakeOptions) {
          if (!options.contains(fakeWord) && fakeWord != word) {
            options.add(fakeWord);
            if (options.length > numberOfWrongOptions) {
              break; // We have enough options
            }
          }
        }

        // If we can't generate more unique options, break to avoid an infinite loop
        if (fakeOptions.isEmpty) break;
      }
    } else {
      // We have enough cards, add random options
      int added = 0;
      for (int i = 0;
          i < otherCards.length && added < numberOfWrongOptions;
          i++) {
        // Make sure the option is not already in the list
        if (!options.contains(otherCards[i].word)) {
          options.add(otherCards[i].word);
          added++;
        }
      }
    }

    // Ensure we have no duplicate options
    final uniqueOptions = options.toSet().toList();

    // If we lost options due to duplicates, generate more
    while (uniqueOptions.length <= numberOfWrongOptions &&
        uniqueOptions.length < allCards.length) {
      // Find a card we haven't used yet
      final unusedCards =
          allCards.where((card) => !uniqueOptions.contains(card.word)).toList();
      if (unusedCards.isNotEmpty) {
        unusedCards.shuffle();
        uniqueOptions.add(unusedCards.first.word);
      } else {
        break; // No more unused cards
      }
    }

    // Shuffle the options
    uniqueOptions.shuffle();
    return uniqueOptions;
  }

  /// Generate similar-looking words for quiz options when we don't have enough real cards
  List<String> _generateSimilarWords(String word, int count) {
    final result = <String>[];
    final random = DateTime.now().millisecondsSinceEpoch;

    // Some simple transformations for similar words
    if (word.length > 3) {
      // Swap letters
      for (int i = 0; i < word.length - 1 && result.length < count; i++) {
        final chars = word.split('');
        final temp = chars[i];
        chars[i] = chars[i + 1];
        chars[i + 1] = temp;
        result.add(chars.join(''));
      }

      // Change a vowel
      for (int i = 0; i < word.length && result.length < count; i++) {
        if ('aeiou'.contains(word[i].toLowerCase())) {
          final vowels = 'aeiou'.replaceAll(word[i].toLowerCase(), '');
          final newVowel = vowels[random % vowels.length];
          final chars = word.split('');
          chars[i] = newVowel;
          result.add(chars.join(''));
        }
      }

      // Add a letter
      if (result.length < count) {
        final chars = word.split('');
        chars.add(String.fromCharCode(97 + (random % 26))); // random letter
        result.add(chars.join(''));
      }
    }

    // If we still don't have enough, add a suffix
    while (result.length < count) {
      final suffixes = [
        'ize',
        'ify',
        'ate',
        'en',
        'ic',
        'al',
        'y',
        'ing',
        'ed'
      ];
      final suffix = suffixes[random % suffixes.length];
      result.add(word + suffix);
    }

    return result.toSet().toList(); // Remove any duplicates
  }

  /// Generate spell-word questions where the user has to spell the word from letters
  Future<List<QuizQuestion>> _generateSpellWordQuestions(
      List<VocabCard> quizCards,
      {Set<String> usedContent = const {}}) async {
    final questions = <QuizQuestion>[];
    debugPrint(
        '🔍 SPELL_WORD: Starting to generate ${quizCards.length} spell word questions');

    for (int i = 0; i < quizCards.length; i++) {
      try {
        final card = quizCards[i];
        debugPrint(
            '🔍 SPELL_WORD: Processing card ${i + 1}/${quizCards.length}: "${card.word}"');

        // Skip cards with empty words
        if (card.word.isEmpty) {
          debugPrint(
              '⚠️ SPELL_WORD: Skipping card with empty word in spell word question');
          continue;
        }

        // For spell word questions, we'll alternate between definition and usage example
        // This ensures a good mix of both types as requested
        final bool useDefinition =
            i % 2 == 0; // Even indices use definition, odd use example
        debugPrint('🔍 SPELL_WORD: Using definition: $useDefinition');

        String prompt;
        if (useDefinition) {
          // Use the card's definition without phrases like "The word X means:"
          prompt = _getCleanDefinition(card.definition);

          // Check if we've already used this definition
          if (usedContent.contains(prompt)) {
            // If already used, create a slightly modified version
            prompt = _createSlightlyModifiedDefinition(card.definition);
          }

          // Track usage
          usedContent.add(prompt);

          debugPrint(
              '🔍 SPELL_WORD: Using definition as prompt: "${prompt.substring(0, prompt.length > 30 ? 30 : prompt.length)}..."');
        } else {
          // Generate a sentence using the word
          debugPrint('🔍 SPELL_WORD: Generating sentence with blank');
          prompt =
              await _generateSentenceWithBlank(card, usedContent: usedContent);
          debugPrint(
              '🔍 SPELL_WORD: Generated sentence: "${prompt.substring(0, prompt.length > 30 ? 30 : prompt.length)}..."');
        }

        // Generate letter options including all the letters of the correct word
        // plus some additional random letters (3 more than needed)
        final correctWord = card.word.toLowerCase();
        debugPrint(
            '🔍 SPELL_WORD: Generating letter options for word: "$correctWord"');
        final options = _generateLetterOptions(correctWord, extraLetters: 3);

        debugPrint(
            '🔍 SPELL_WORD: Generated ${options.length} letter options: $options');

        // Ensure we have options
        if (options.isEmpty) {
          debugPrint(
              '❌ SPELL_WORD: Failed to generate letter options for word: ${card.word}');
          continue;
        }

        debugPrint('🔍 SPELL_WORD: Creating SpellWordQuestion object');
        final question = SpellWordQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          prompt: prompt,
          correctWord: correctWord,
          options: options,
          isDefinition: useDefinition,
        );

        questions.add(question);
        debugPrint('✅ SPELL_WORD: Successfully added spell word question');
      } catch (e, stackTrace) {
        debugPrint('❌ SPELL_WORD: Error generating spell word question: $e');
        debugPrint('❌ SPELL_WORD: Stack trace: $stackTrace');
        // Skip this question and continue with others
      }
    }

    debugPrint(
        '✅ SPELL_WORD: Completed generation of ${questions.length} spell word questions');
    return questions;
  }

  /// Helper method to extract a clean definition without phrases like "The word X means:"
  String _getCleanDefinition(String definition) {
    // Remove any leading/trailing whitespace
    final trimmed = definition.trim();

    // Check for and remove common definitional phrases
    final patterns = [
      RegExp(r'^the\s+word\s+["\w\s]+\s+means:?\s*', caseSensitive: false),
      RegExp(r'^definition\s+of\s+["\w\s]+:?\s*', caseSensitive: false),
      RegExp(r'^meaning\s+of\s+["\w\s]+:?\s*', caseSensitive: false),
      RegExp(r'^the\s+meaning\s+of\s+["\w\s]+\s+is:?\s*', caseSensitive: false),
      RegExp(r'^["\w\s]+\s+is\s+defined\s+as:?\s*', caseSensitive: false),
    ];

    String cleaned = trimmed;
    for (final pattern in patterns) {
      cleaned = cleaned.replaceFirst(pattern, '');
      if (cleaned != trimmed) break; // Stop after first match
    }

    // Capitalize first letter if needed
    if (cleaned.isNotEmpty) {
      cleaned = cleaned[0].toUpperCase() + cleaned.substring(1);
    }

    return cleaned.isEmpty
        ? trimmed
        : cleaned; // Return original if stripped to empty
  }

  /// Generate letter options for spell-word questions
  List<String> _generateLetterOptions(String word, {int extraLetters = 3}) {
    try {
      debugPrint('🔍 LETTERS: Generating options for word: "$word"');

      // Safety check - ensure word is not empty
      if (word.isEmpty) {
        debugPrint('⚠️ LETTERS: Empty word provided to _generateLetterOptions');
        return ['a', 'b', 'c', 'd', 'e']; // Fallback
      }

      // Convert word to lowercase and split into individual letters
      final letters = word.toLowerCase().split('');
      debugPrint(
          '🔍 LETTERS: Split word into ${letters.length} letters: $letters');

      // Create a set of unique letters to add random letters that aren't already in the word
      final uniqueLettersSet = letters.toSet();

      // Add extra random letters that aren't already in the word
      final alphabet = 'abcdefghijklmnopqrstuvwxyz';
      final random = DateTime.now().millisecondsSinceEpoch;

      debugPrint('🔍 LETTERS: Adding $extraLetters extra random letters');
      int added = 0;
      int attempts = 0;
      while (added < extraLetters && attempts < 26) {
        // Avoid infinite loop
        final randomChar = alphabet[(random + attempts) % alphabet.length];
        attempts++;

        // Only add if this letter isn't already in our set of unique letters
        if (!uniqueLettersSet.contains(randomChar)) {
          letters.add(randomChar);
          uniqueLettersSet
              .add(randomChar); // Add to set to track unique letters
          added++;
          debugPrint('🔍 LETTERS: Added random letter: $randomChar');
        }
      }

      // Ensure we have at least some minimum number of letters
      if (letters.length < 5) {
        debugPrint(
            '🔍 LETTERS: Not enough letters, adding more to reach minimum of 5');
        for (int i = letters.length; i < 5; i++) {
          final randomChar = alphabet[(random + i + 10) % alphabet.length];
          if (!uniqueLettersSet.contains(randomChar)) {
            letters.add(randomChar);
            uniqueLettersSet.add(randomChar);
            debugPrint('🔍 LETTERS: Added extra letter: $randomChar');
          }
        }
      }

      // Shuffle the options to randomize the order
      letters.shuffle();
      debugPrint('🔍 LETTERS: Shuffled letters: $letters');

      // Final check to ensure all original word letters (including duplicates) are included
      final wordLetters = word.toLowerCase().split('');
      final letterCount = <String, int>{};

      // Count occurrences of each letter in the original word
      for (final letter in wordLetters) {
        letterCount[letter] = (letterCount[letter] ?? 0) + 1;
      }

      // Count occurrences of each letter in our options
      final optionsCount = <String, int>{};
      for (final letter in letters) {
        optionsCount[letter] = (optionsCount[letter] ?? 0) + 1;
      }

      // Add any missing letters or additional occurrences needed
      for (final entry in letterCount.entries) {
        final letter = entry.key;
        final requiredCount = entry.value;
        final currentCount = optionsCount[letter] ?? 0;

        if (currentCount < requiredCount) {
          // Add the missing occurrences
          for (int i = 0; i < requiredCount - currentCount; i++) {
            letters.add(letter);
            debugPrint('🔍 LETTERS: Added missing letter occurrence: $letter');
          }
        }
      }

      // Shuffle again after adding any missing letters
      if (letters.length != wordLetters.length + extraLetters) {
        letters.shuffle();
      }

      debugPrint(
          '✅ LETTERS: Final letter options (${letters.length}): $letters');
      return letters;
    } catch (e, stackTrace) {
      debugPrint('❌ LETTERS: Error generating letter options: $e');
      debugPrint('❌ LETTERS: Stack trace: $stackTrace');
      // Provide a fallback set of letters
      return ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'];
    }
  }

  /// Get a random boolean
  bool _getRandomBool() {
    return DateTime.now().millisecondsSinceEpoch % 2 == 0;
  }

  /// Convert pre-generated quiz content to QuizQuestion objects
  Future<QuizQuestion?> _convertContentToQuizQuestion(
    QuizContent content,
    List<VocabCard> availableCards,
  ) async {
    try {
      if (content is FillInBlankContent) {
        // Find the vocabulary card for this content
        final card = availableCards.firstWhere(
          (c) => c.word.toLowerCase() == content.correctAnswer.toLowerCase(),
          orElse: () => VocabCard(
            id: '',
            word: content.correctAnswer,
            definition: '',
            examples: [],
            type: [],
            pronunciation: '',
            level: content.difficulty,
            frequency: 1,
            color: 'Red',
            masteryLevel: 1,
          ),
        );

        // Create options including the correct answer and distractors
        final options = List<String>.from(content.distractors);
        options.add(content.correctAnswer);
        options.shuffle();

        return FillInBlankQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          sentence: content.sentence,
          blankWord: content.correctAnswer,
          options: options,
        );
      } else if (content is TrueFalseContent) {
        // Find the vocabulary card for this content
        final card = availableCards.firstWhere(
          (c) => content.statement.toLowerCase().contains(c.word.toLowerCase()),
          orElse: () => VocabCard(
            id: '',
            word: 'unknown',
            definition: '',
            examples: [],
            type: [],
            pronunciation: '',
            level: content.difficulty,
            frequency: 1,
            color: 'Red',
            masteryLevel: 1,
          ),
        );

        return TrueFalseQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          statement: content.statement,
          answer: content.isTrue,
        );
      } else if (content is SpellWordContent) {
        // Find the vocabulary card for this content
        final card = availableCards.firstWhere(
          (c) => c.word.toLowerCase() == content.answer.toLowerCase(),
          orElse: () => VocabCard(
            id: '',
            word: content.answer,
            definition: '',
            examples: [],
            type: [],
            pronunciation: '',
            level: content.difficulty,
            frequency: 1,
            color: 'Red',
            masteryLevel: 1,
          ),
        );

        // Generate letter options for spell word question
        final letters = content.answer.toLowerCase().split('');
        final allLetters = 'abcdefghijklmnopqrstuvwxyz'.split('');

        // Add some random letters to make it challenging
        final extraLetters =
            allLetters.where((l) => !letters.contains(l)).toList();
        extraLetters.shuffle();

        final letterOptions = [...letters, ...extraLetters.take(6)];
        letterOptions.shuffle();

        return SpellWordQuestion(
          id: FirebaseService.getUID('quiz_questions'),
          card: card,
          prompt: content.prompt,
          correctWord: content.answer,
          options: letterOptions,
          isDefinition: content.promptType == 'definition',
        );
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error converting content to quiz question: $e');
      return null;
    }
  }
}
