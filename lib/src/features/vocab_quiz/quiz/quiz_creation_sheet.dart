import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vocadex/src/common/widgets/gradient_button.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/user/user_providers.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_button.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// State provider for tracking if double XP is selected
final doubleXpSelectedProvider = StateProvider<bool>((ref) => false);

/// A bottom sheet that shows quiz creation options with diamond costs
class QuizCreationSheet extends ConsumerStatefulWidget {
  final QuizMode mode;

  const QuizCreationSheet({
    super.key,
    required this.mode,
  });

  @override
  ConsumerState<QuizCreationSheet> createState() => _QuizCreationSheetState();
}

class _QuizCreationSheetState extends ConsumerState<QuizCreationSheet> {
  @override
  void deactivate() {
    // Reset any state when the sheet is deactivated
    ref.read(doubleXpSelectedProvider.notifier).state = false;
    super.deactivate();
  }

  @override
  void dispose() {
    // Clean up any controllers or resources here
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isPremium = ref.watch(subscriptionStateProvider);
    final diamondsAsync = ref.watch(diamondsProvider);
    final doubleXpSelected = ref.watch(doubleXpSelectedProvider);

    // Diamond costs
    final int baseCost = widget.mode == QuizMode.train ? 3 : 5;
    final int doubleXpCost = 20;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title
          Text(
            widget.mode == QuizMode.train
                ? 'Create your Training Plan'
                : 'Create your Challenge',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Diamond cost info
          if (!isPremium) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/diamond.svg',
                  height: 24,
                  width: 24,
                  // colorFilter: ColorFilter.mode(
                  //   AppColors.getSecondaryColor(Theme.of(context).brightness),
                  //   BlendMode.srcIn,
                  // ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Cost: $baseCost diamonds',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Current diamond count
            diamondsAsync.when(
              data: (diamonds) => Text(
                'You have: $diamonds diamonds',
                style: TextStyle(
                  fontSize: 14,
                  color: const Color.fromARGB(255, 97, 96, 96),
                ),
                textAlign: TextAlign.center,
              ),
              loading: () => const Center(
                child: SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
              error: (_, __) => const Text(
                'Could not load diamond count',
                style: TextStyle(color: AppColors.warningLight),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 16),
          ],

          // Double XP option (only for challenge mode)
          if (widget.mode == QuizMode.challenge && !isPremium) ...[
            const Divider(),
            CheckboxListTile(
              value: doubleXpSelected,
              onChanged: (value) {
                ref.read(doubleXpSelectedProvider.notifier).state =
                    value ?? false;
              },
              title: const Text('Double XP'),
              subtitle: Text(
                  'Earn twice as many points (costs $doubleXpCost diamonds)'),
              secondary: const Icon(
                Icons.bolt,
                color: Colors.amber,
              ),
              controlAffinity: ListTileControlAffinity.leading,
            ),
            const Divider(),
          ],

          const SizedBox(height: 24),

          // Create Now button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: GradientButton(
              height: 60,
              text: 'Create Now',
              onPressed: () => _createQuiz(context, ref),
            ),
          ),
          // SizedBox(
          //   height: 56,
          //   child: ElevatedButton(
          //     onPressed: () => _createQuiz(context, ref),
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: Theme.of(context).primaryColor,
          //       foregroundColor: AppColors.white,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(16),
          //       ),
          //     ),
          //     child: const Text(
          //       'Create Now',
          //       style: TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.bold,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Future<void> _createQuiz(BuildContext context, WidgetRef ref) async {
    final isPremium = ref.read(subscriptionStateProvider);
    final doubleXpSelected = ref.read(doubleXpSelectedProvider);
    // No need to read diamondsAsync here as we're not using it

    // Diamond costs
    final int baseCost = widget.mode == QuizMode.train ? 3 : 5;
    final int doubleXpCost = 20;

    // Set double XP in a provider if selected
    if (doubleXpSelected) {
      ref.read(quizDoubleXpProvider.notifier).state = true;
    } else {
      ref.read(quizDoubleXpProvider.notifier).state = false;
    }

    // Calculate total diamond cost
    int totalCost = 0;
    if (!isPremium) {
      totalCost = widget.mode == QuizMode.train
          ? baseCost
          : baseCost + (doubleXpSelected ? doubleXpCost : 0);
    }

    // Check if user has enough diamonds
    if (!isPremium && totalCost > 0) {
      final firebaseService = FirebaseService();
      final success = await firebaseService.decrementDiamondsBy(totalCost);

      if (!success) {
        if (context.mounted) {
          Navigator.pop(context); // Close the bottom sheet

          // Show paywall instead of just a toast
          ShowPaywall().presentPaywall();

          // Also show a toast to explain why the paywall is shown
          showFailureToast(
            context,
            title: 'Not Enough Diamonds',
            description: 'You don\'t have enough diamonds for this action.',
          );
        }
        return;
      }

      // Refresh diamonds count
      final _ = await ref.refresh(diamondsProvider.future);
    }

    // Close the bottom sheet
    if (context.mounted) {
      Navigator.pop(context);

      // Start the quiz
      StartQuizButton.startQuiz(context, ref, widget.mode);
    }
  }
}

/// Provider to track if double XP is enabled for the current quiz
final quizDoubleXpProvider = StateProvider<bool>((ref) => false);

/// Show the quiz creation bottom sheet
Future<void> showQuizCreationSheet(
    BuildContext context, WidgetRef ref, QuizMode mode) async {
  // Reset double XP selection
  ref.read(doubleXpSelectedProvider.notifier).state = false;

  // We'll use the navigator to handle back button presses

  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: AppColors.transparent,
    builder: (context) => PopScope(
      canPop: true,
      onPopInvoked: (didPop) {
        if (didPop) {
          // Reset any state if needed when dialog is dismissed
          ref.read(doubleXpSelectedProvider.notifier).state = false;
        }
      },
      child: QuizCreationSheet(mode: mode),
    ),
  );

  // Ensure we reset the state when the dialog is popped
  ref.read(doubleXpSelectedProvider.notifier).state = false;
}
