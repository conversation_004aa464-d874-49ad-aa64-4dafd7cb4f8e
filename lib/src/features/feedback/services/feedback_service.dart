// lib/src/features/feedback/services/feedback_service.dart

import 'package:flutter/material.dart';
import 'package:vocadex/src/core/config/url_config.dart';
import 'package:vocadex/src/core/utils/url_launcher_utils.dart';
import 'package:vocadex/src/features/localization/app_strings.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'dart:io' show Platform;

/// Service to handle feedback functionality
class FeedbackService {
  /// Send feedback via email
  static Future<void> sendFeedback(
    BuildContext context, {
    required String feedbackText,
    String? userEmail,
    String? userId,
  }) async {
    try {
      // Validate feedback
      if (feedbackText.trim().isEmpty) {
        showFailureToast(
          context,
          title: 'Validation Error',
          description: AppStrings.feedbackRequired,
        );
        return;
      }

      if (feedbackText.trim().length < 10) {
        showFailureToast(
          context,
          title: 'Validation Error',
          description: AppStrings.feedbackTooShort,
        );
        return;
      }

      // Compose email content
      final emailBody = _composeEmailBody(
        feedbackText: feedbackText,
        userEmail: userEmail,
        userId: userId,
      );

      // Launch email client
      await UrlLauncherUtils.launchEmail(
        context,
        email: UrlConfig.supportEmail,
        subject: AppStrings.feedbackEmailSubject,
        body: emailBody,
      );

      // Show success message
      if (context.mounted) {
        showSuccessToast(
          context,
          title: 'Feedback Sent',
          description: AppStrings.feedbackSent,
        );
      }
    } catch (e) {
      debugPrint('Error sending feedback: $e');
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: AppStrings.feedbackError,
        );
      }
    }
  }

  /// Compose the email body with feedback and system information
  static String _composeEmailBody({
    required String feedbackText,
    String? userEmail,
    String? userId,
  }) {
    final buffer = StringBuffer();
    
    // User feedback
    buffer.writeln('User Feedback:');
    buffer.writeln(feedbackText.trim());
    buffer.writeln();
    
    // Separator
    buffer.writeln('---');
    buffer.writeln('App Information:');
    
    // App version (from pubspec.yaml)
    buffer.writeln('• App Version: 1.0.3');
    
    // Platform information
    buffer.writeln('• Platform: ${_getPlatformName()}');
    
    // User information (if available)
    if (userEmail != null && userEmail.isNotEmpty) {
      buffer.writeln('• User Email: $userEmail');
    }
    
    if (userId != null && userId.isNotEmpty) {
      buffer.writeln('• User ID: $userId');
    }
    
    // Timestamp
    buffer.writeln('• Timestamp: ${DateTime.now().toIso8601String()}');
    
    return buffer.toString();
  }

  /// Get platform name for system information
  static String _getPlatformName() {
    try {
      if (Platform.isIOS) return 'iOS';
      if (Platform.isAndroid) return 'Android';
      if (Platform.isMacOS) return 'macOS';
      if (Platform.isWindows) return 'Windows';
      if (Platform.isLinux) return 'Linux';
      return 'Unknown';
    } catch (e) {
      return 'Web/Unknown';
    }
  }

  /// Validate feedback text
  static String? validateFeedback(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.feedbackRequired;
    }
    
    if (value.trim().length < 10) {
      return AppStrings.feedbackTooShort;
    }
    
    return null;
  }
}
