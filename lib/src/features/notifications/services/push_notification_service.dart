import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'push_notification_service.g.dart';

@riverpod
PushNotificationService pushNotificationService(Ref ref) {
  return PushNotificationService();
}

class PushNotificationService {
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Initialize push notification service
  Future<void> initialize() async {
    try {
      debugPrint('🚀 Starting push notification service initialization...');

      // Request permission for iOS
      debugPrint('📱 Requesting notification permissions...');
      final permissionGranted = await requestPermission();
      debugPrint('✅ Permission granted: $permissionGranted');

      // Wait for APNS token on iOS before getting FCM token
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        debugPrint('🍎 iOS detected - waiting for APNS token...');
        await _waitForAPNSToken();
      }

      // Get initial token with retry logic
      debugPrint('🎯 Getting FCM token...');
      final token = await _getTokenWithRetry();
      if (token != null) {
        debugPrint('✅ FCM Token received: ${token.substring(0, 20)}...');
        await _saveTokenToFirestore(token);
      } else {
        debugPrint('⚠️ No FCM token received');
      }

      // Listen for token refresh
      debugPrint('👂 Setting up token refresh listener...');
      _messaging.onTokenRefresh.listen((token) {
        debugPrint('🔄 Token refreshed: ${token.substring(0, 20)}...');
        _saveTokenToFirestore(token);
      });

      // Handle foreground messages
      debugPrint('📨 Setting up foreground message handler...');
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background message taps
      debugPrint('📱 Setting up background message handler...');
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

      // Handle app launch from terminated state
      debugPrint('🚀 Checking for initial message...');
      final initialMessage = await _messaging.getInitialMessage();
      if (initialMessage != null) {
        debugPrint('📬 Initial message found: ${initialMessage.messageId}');
        _handleMessageOpenedApp(initialMessage);
      } else {
        debugPrint('📭 No initial message');
      }

      debugPrint('✅ Push notification service initialized successfully');
    } catch (e) {
      debugPrint('🚨 Error initializing push notification service: $e');
      debugPrint('🔍 Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Request notification permission
  Future<bool> requestPermission() async {
    try {
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      final isEnabled =
          settings.authorizationStatus == AuthorizationStatus.authorized ||
              settings.authorizationStatus == AuthorizationStatus.provisional;

      debugPrint(
          'Push notification permission: ${settings.authorizationStatus}');
      return isEnabled;
    } catch (e) {
      debugPrint('Error requesting push notification permission: $e');
      return false;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      final settings = await _messaging.getNotificationSettings();
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      debugPrint('Error checking notification settings: $e');
      return false;
    }
  }

  /// Get FCM token
  Future<String?> getToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  /// Send notification (admin functionality)
  Future<void> sendNotification(NotificationModel notification) async {
    try {
      // Save notification to Firestore
      await _firestore
          .collection('notifications')
          .doc(notification.id)
          .set(notification.toFirestore());

      // For now, we'll use Firebase Cloud Functions to send the actual push notification
      // This is a placeholder for the admin sending functionality
      debugPrint('Notification saved to Firestore: ${notification.id}');

      // TODO: Implement actual FCM sending via Cloud Functions or server
      // This would typically involve calling a Cloud Function that sends to all tokens
    } catch (e) {
      debugPrint('Error sending notification: $e');
      rethrow;
    }
  }

  /// Save FCM token to Firestore
  Future<void> _saveTokenToFirestore(String token) async {
    try {
      // Save token associated with current user
      // This will be used by admin to send notifications to all users
      await _firestore.collection('fcm_tokens').doc(token).set({
        'token': token,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
        'platform': defaultTargetPlatform.name,
      }, SetOptions(merge: true));

      debugPrint('FCM token saved to Firestore');
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  /// Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Received foreground message: ${message.messageId}');
    debugPrint('Title: ${message.notification?.title}');
    debugPrint('Body: ${message.notification?.body}');
    debugPrint('Data: ${message.data}');

    // Show local notification for foreground messages
    // This will be handled by the local notification service
  }

  /// Handle message opened app
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('Message opened app: ${message.messageId}');
    debugPrint('Data: ${message.data}');

    // Handle navigation based on message data
    // TODO: Implement navigation logic
  }

  /// Wait for APNS token to be available (iOS only)
  Future<void> _waitForAPNSToken() async {
    if (defaultTargetPlatform != TargetPlatform.iOS) return;

    debugPrint('⏳ Waiting for APNS token...');

    // Try to get APNS token with timeout
    for (int attempt = 1; attempt <= 10; attempt++) {
      try {
        final apnsToken = await _messaging.getAPNSToken();
        if (apnsToken != null) {
          debugPrint(
              '✅ APNS token available: ${apnsToken.substring(0, 20)}...');
          return;
        }

        debugPrint('⏳ APNS token not available yet (attempt $attempt/10)');
        await Future.delayed(Duration(seconds: attempt)); // Exponential backoff
      } catch (e) {
        debugPrint('⚠️ Error checking APNS token (attempt $attempt/10): $e');
        await Future.delayed(Duration(seconds: attempt));
      }
    }

    debugPrint('⚠️ APNS token not available after 10 attempts');
  }

  /// Get FCM token with retry logic
  Future<String?> _getTokenWithRetry() async {
    for (int attempt = 1; attempt <= 5; attempt++) {
      try {
        debugPrint('🎯 Getting FCM token (attempt $attempt/5)...');

        // On iOS, check APNS token first
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          final apnsToken = await _messaging.getAPNSToken();
          debugPrint(
              '🍎 APNS token status: ${apnsToken != null ? "Available" : "Not available"}');

          if (apnsToken == null) {
            debugPrint('⚠️ APNS token not available, waiting...');
            await Future.delayed(Duration(seconds: 2));
            continue;
          }
        }

        final token = await _messaging.getToken();
        if (token != null) {
          debugPrint('✅ FCM token retrieved successfully');
          return token;
        }

        debugPrint('⚠️ FCM token is null (attempt $attempt/5)');
        await Future.delayed(Duration(seconds: 2));
      } catch (e) {
        debugPrint('🚨 Error getting FCM token (attempt $attempt/5): $e');
        if (attempt == 5) {
          debugPrint('🚨 Failed to get FCM token after 5 attempts');
          rethrow;
        }
        await Future.delayed(Duration(seconds: 2));
      }
    }

    return null;
  }

  /// Debug method to check current token status
  Future<Map<String, dynamic>> getTokenStatus() async {
    final status = <String, dynamic>{};

    try {
      // Check notification settings
      final settings = await _messaging.getNotificationSettings();
      status['authorizationStatus'] = settings.authorizationStatus.toString();
      status['alertSetting'] = settings.alert.toString();
      status['badgeSetting'] = settings.badge.toString();
      status['soundSetting'] = settings.sound.toString();

      // Check APNS token (iOS only)
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        try {
          final apnsToken = await _messaging.getAPNSToken();
          status['apnsTokenAvailable'] = apnsToken != null;
          status['apnsTokenLength'] = apnsToken?.length ?? 0;
        } catch (e) {
          status['apnsTokenError'] = e.toString();
        }
      }

      // Check FCM token
      try {
        final fcmToken = await _messaging.getToken();
        status['fcmTokenAvailable'] = fcmToken != null;
        status['fcmTokenLength'] = fcmToken?.length ?? 0;
      } catch (e) {
        status['fcmTokenError'] = e.toString();
      }
    } catch (e) {
      status['error'] = e.toString();
    }

    return status;
  }

  /// Force refresh FCM token
  Future<String?> refreshToken() async {
    try {
      debugPrint('🔄 Forcing FCM token refresh...');
      await _messaging.deleteToken();
      await Future.delayed(const Duration(seconds: 1));
      return await _getTokenWithRetry();
    } catch (e) {
      debugPrint('🚨 Error refreshing token: $e');
      return null;
    }
  }
}
