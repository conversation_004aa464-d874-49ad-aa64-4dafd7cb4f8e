// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_notification_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminNotificationServiceHash() =>
    r'110fe2ba9a95348c0cca6918049b70490dc9ba09';

/// See also [adminNotificationService].
@ProviderFor(adminNotificationService)
final adminNotificationServiceProvider =
    AutoDisposeProvider<AdminNotificationService>.internal(
  adminNotificationService,
  name: r'adminNotificationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminNotificationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminNotificationServiceRef
    = AutoDisposeProviderRef<AdminNotificationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
