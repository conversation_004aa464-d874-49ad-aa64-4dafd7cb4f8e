import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/services/push_notification_service.dart';
import 'package:vocadex/src/features/notifications/services/local_notification_service.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';

part 'notification_service.g.dart';

@riverpod
NotificationService notificationService(Ref ref) {
  return NotificationService(
    pushService: ref.watch(pushNotificationServiceProvider),
    localService: ref.watch(localNotificationServiceProvider),
  );
}

class NotificationService {
  final PushNotificationService _pushService;
  final LocalNotificationService _localService;

  NotificationService({
    required PushNotificationService pushService,
    required LocalNotificationService localService,
  })  : _pushService = pushService,
        _localService = localService;

  /// Initialize notification services
  Future<void> initialize() async {
    try {
      await _pushService.initialize();
      await _localService.initialize();
      debugPrint('Notification services initialized successfully');
    } catch (e) {
      debugPrint('Error initializing notification services: $e');
      rethrow;
    }
  }

  /// Send a push notification
  Future<void> sendPushNotification(NotificationModel notification) async {
    try {
      await _pushService.sendNotification(notification);
    } catch (e) {
      debugPrint('Error sending push notification: $e');
      rethrow;
    }
  }

  /// Schedule a local notification
  Future<void> scheduleLocalNotification(
    NotificationModel notification,
    DateTime scheduledTime,
  ) async {
    try {
      await _localService.scheduleNotification(notification, scheduledTime);
    } catch (e) {
      debugPrint('Error scheduling local notification: $e');
      rethrow;
    }
  }

  /// Cancel a scheduled local notification
  Future<void> cancelLocalNotification(String notificationId) async {
    try {
      await _localService.cancelNotification(notificationId);
    } catch (e) {
      debugPrint('Error canceling local notification: $e');
      rethrow;
    }
  }

  /// Cancel all local notifications
  Future<void> cancelAllLocalNotifications() async {
    try {
      await _localService.cancelAllNotifications();
    } catch (e) {
      debugPrint('Error canceling all local notifications: $e');
      rethrow;
    }
  }

  /// Set up trigger events for local notifications
  Future<void> setupTriggerEvents(List<TriggerEvent> triggers) async {
    try {
      await _localService.setupTriggerEvents(triggers);
    } catch (e) {
      debugPrint('Error setting up trigger events: $e');
      rethrow;
    }
  }

  /// Get FCM token for push notifications
  Future<String?> getFCMToken() async {
    try {
      return await _pushService.getToken();
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      final pushEnabled = await _pushService.areNotificationsEnabled();
      final localEnabled = await _localService.areNotificationsEnabled();
      return pushEnabled && localEnabled;
    } catch (e) {
      debugPrint('Error checking notification permissions: $e');
      return false;
    }
  }

  /// Debug method to get comprehensive token status
  Future<Map<String, dynamic>> getDebugTokenStatus() async {
    try {
      debugPrint('🔍 Getting comprehensive token status...');
      final status = await _pushService.getTokenStatus();
      debugPrint('📊 Token Status: $status');
      return status;
    } catch (e) {
      debugPrint('🚨 Error getting token status: $e');
      return {'error': e.toString()};
    }
  }

  /// Force refresh FCM token
  Future<String?> refreshFCMToken() async {
    try {
      debugPrint('🔄 Refreshing FCM token...');
      final token = await _pushService.refreshToken();
      debugPrint(
          '✅ Token refresh result: ${token != null ? "Success" : "Failed"}');
      return token;
    } catch (e) {
      debugPrint('🚨 Error refreshing FCM token: $e');
      return null;
    }
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    try {
      final pushPermission = await _pushService.requestPermission();
      final localPermission = await _localService.requestPermission();
      return pushPermission && localPermission;
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Handle notification tap
  void handleNotificationTap(Map<String, dynamic> data) {
    try {
      // Handle navigation based on notification data
      debugPrint('Notification tapped with data: $data');
      // TODO: Implement navigation logic based on notification type
    } catch (e) {
      debugPrint('Error handling notification tap: $e');
    }
  }
}
