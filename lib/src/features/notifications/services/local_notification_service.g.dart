// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_notification_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$localNotificationServiceHash() =>
    r'de89fee4ec0940a94077976a66e6dcc4702c5b27';

/// See also [localNotificationService].
@ProviderFor(localNotificationService)
final localNotificationServiceProvider =
    AutoDisposeProvider<LocalNotificationService>.internal(
  localNotificationService,
  name: r'localNotificationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$localNotificationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocalNotificationServiceRef
    = AutoDisposeProviderRef<LocalNotificationService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
