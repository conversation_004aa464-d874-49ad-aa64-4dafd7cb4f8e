// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trigger_event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TriggerEventImpl _$$TriggerEventImplFromJson(Map<String, dynamic> json) =>
    _$TriggerEventImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$TriggerTypeEnumMap, json['type']),
      enabled: json['enabled'] as bool? ?? true,
      title: json['title'] as String,
      message: json['message'] as String,
      schedule: json['schedule'] == null
          ? null
          : TriggerSchedule.fromJson(json['schedule'] as Map<String, dynamic>),
      conditions: json['conditions'] as Map<String, dynamic>?,
      lastTriggered: json['lastTriggered'] == null
          ? null
          : DateTime.parse(json['lastTriggered'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$TriggerEventImplToJson(_$TriggerEventImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': _$TriggerTypeEnumMap[instance.type]!,
      'enabled': instance.enabled,
      'title': instance.title,
      'message': instance.message,
      'schedule': instance.schedule,
      'conditions': instance.conditions,
      'lastTriggered': instance.lastTriggered?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$TriggerTypeEnumMap = {
  TriggerType.dailyReminder: 'dailyReminder',
  TriggerType.weeklyGoal: 'weeklyGoal',
  TriggerType.streakReminder: 'streakReminder',
  TriggerType.dailyWord: 'dailyWord',
  TriggerType.lowDiamonds: 'lowDiamonds',
  TriggerType.masteryProgress: 'masteryProgress',
  TriggerType.inactivityReminder: 'inactivityReminder',
  TriggerType.achievementUnlock: 'achievementUnlock',
};

_$TriggerScheduleImpl _$$TriggerScheduleImplFromJson(
        Map<String, dynamic> json) =>
    _$TriggerScheduleImpl(
      type: $enumDecode(_$ScheduleTypeEnumMap, json['type']),
      time: json['time'] as String?,
      daysOfWeek: (json['daysOfWeek'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      intervalHours: (json['intervalHours'] as num?)?.toInt(),
      intervalDays: (json['intervalDays'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$TriggerScheduleImplToJson(
        _$TriggerScheduleImpl instance) =>
    <String, dynamic>{
      'type': _$ScheduleTypeEnumMap[instance.type]!,
      'time': instance.time,
      'daysOfWeek': instance.daysOfWeek,
      'intervalHours': instance.intervalHours,
      'intervalDays': instance.intervalDays,
    };

const _$ScheduleTypeEnumMap = {
  ScheduleType.daily: 'daily',
  ScheduleType.weekly: 'weekly',
  ScheduleType.interval: 'interval',
  ScheduleType.once: 'once',
};
