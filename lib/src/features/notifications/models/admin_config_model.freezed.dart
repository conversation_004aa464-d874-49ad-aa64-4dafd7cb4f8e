// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdminConfig _$AdminConfigFromJson(Map<String, dynamic> json) {
  return _AdminConfig.fromJson(json);
}

/// @nodoc
mixin _$AdminConfig {
  List<String> get adminEmails => throw _privateConstructorUsedError;
  bool get notificationsEnabled => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this AdminConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdminConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdminConfigCopyWith<AdminConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminConfigCopyWith<$Res> {
  factory $AdminConfigCopyWith(
          AdminConfig value, $Res Function(AdminConfig) then) =
      _$AdminConfigCopyWithImpl<$Res, AdminConfig>;
  @useResult
  $Res call(
      {List<String> adminEmails,
      bool notificationsEnabled,
      DateTime? lastUpdated});
}

/// @nodoc
class _$AdminConfigCopyWithImpl<$Res, $Val extends AdminConfig>
    implements $AdminConfigCopyWith<$Res> {
  _$AdminConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? adminEmails = null,
    Object? notificationsEnabled = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      adminEmails: null == adminEmails
          ? _value.adminEmails
          : adminEmails // ignore: cast_nullable_to_non_nullable
              as List<String>,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdminConfigImplCopyWith<$Res>
    implements $AdminConfigCopyWith<$Res> {
  factory _$$AdminConfigImplCopyWith(
          _$AdminConfigImpl value, $Res Function(_$AdminConfigImpl) then) =
      __$$AdminConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> adminEmails,
      bool notificationsEnabled,
      DateTime? lastUpdated});
}

/// @nodoc
class __$$AdminConfigImplCopyWithImpl<$Res>
    extends _$AdminConfigCopyWithImpl<$Res, _$AdminConfigImpl>
    implements _$$AdminConfigImplCopyWith<$Res> {
  __$$AdminConfigImplCopyWithImpl(
      _$AdminConfigImpl _value, $Res Function(_$AdminConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? adminEmails = null,
    Object? notificationsEnabled = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$AdminConfigImpl(
      adminEmails: null == adminEmails
          ? _value._adminEmails
          : adminEmails // ignore: cast_nullable_to_non_nullable
              as List<String>,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdminConfigImpl extends _AdminConfig {
  const _$AdminConfigImpl(
      {final List<String> adminEmails = const [],
      this.notificationsEnabled = true,
      this.lastUpdated})
      : _adminEmails = adminEmails,
        super._();

  factory _$AdminConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdminConfigImplFromJson(json);

  final List<String> _adminEmails;
  @override
  @JsonKey()
  List<String> get adminEmails {
    if (_adminEmails is EqualUnmodifiableListView) return _adminEmails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_adminEmails);
  }

  @override
  @JsonKey()
  final bool notificationsEnabled;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'AdminConfig(adminEmails: $adminEmails, notificationsEnabled: $notificationsEnabled, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdminConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._adminEmails, _adminEmails) &&
            (identical(other.notificationsEnabled, notificationsEnabled) ||
                other.notificationsEnabled == notificationsEnabled) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_adminEmails),
      notificationsEnabled,
      lastUpdated);

  /// Create a copy of AdminConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdminConfigImplCopyWith<_$AdminConfigImpl> get copyWith =>
      __$$AdminConfigImplCopyWithImpl<_$AdminConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdminConfigImplToJson(
      this,
    );
  }
}

abstract class _AdminConfig extends AdminConfig {
  const factory _AdminConfig(
      {final List<String> adminEmails,
      final bool notificationsEnabled,
      final DateTime? lastUpdated}) = _$AdminConfigImpl;
  const _AdminConfig._() : super._();

  factory _AdminConfig.fromJson(Map<String, dynamic> json) =
      _$AdminConfigImpl.fromJson;

  @override
  List<String> get adminEmails;
  @override
  bool get notificationsEnabled;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of AdminConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdminConfigImplCopyWith<_$AdminConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
