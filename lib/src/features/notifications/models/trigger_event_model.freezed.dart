// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'trigger_event_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TriggerEvent _$TriggerEventFromJson(Map<String, dynamic> json) {
  return _TriggerEvent.fromJson(json);
}

/// @nodoc
mixin _$TriggerEvent {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  TriggerType get type => throw _privateConstructorUsedError;
  bool get enabled => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  TriggerSchedule? get schedule => throw _privateConstructorUsedError;
  Map<String, dynamic>? get conditions => throw _privateConstructorUsedError;
  DateTime? get lastTriggered => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this TriggerEvent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TriggerEventCopyWith<TriggerEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TriggerEventCopyWith<$Res> {
  factory $TriggerEventCopyWith(
          TriggerEvent value, $Res Function(TriggerEvent) then) =
      _$TriggerEventCopyWithImpl<$Res, TriggerEvent>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      TriggerType type,
      bool enabled,
      String title,
      String message,
      TriggerSchedule? schedule,
      Map<String, dynamic>? conditions,
      DateTime? lastTriggered,
      DateTime? createdAt,
      DateTime? updatedAt});

  $TriggerScheduleCopyWith<$Res>? get schedule;
}

/// @nodoc
class _$TriggerEventCopyWithImpl<$Res, $Val extends TriggerEvent>
    implements $TriggerEventCopyWith<$Res> {
  _$TriggerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? enabled = null,
    Object? title = null,
    Object? message = null,
    Object? schedule = freezed,
    Object? conditions = freezed,
    Object? lastTriggered = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TriggerType,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      schedule: freezed == schedule
          ? _value.schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as TriggerSchedule?,
      conditions: freezed == conditions
          ? _value.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      lastTriggered: freezed == lastTriggered
          ? _value.lastTriggered
          : lastTriggered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TriggerScheduleCopyWith<$Res>? get schedule {
    if (_value.schedule == null) {
      return null;
    }

    return $TriggerScheduleCopyWith<$Res>(_value.schedule!, (value) {
      return _then(_value.copyWith(schedule: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TriggerEventImplCopyWith<$Res>
    implements $TriggerEventCopyWith<$Res> {
  factory _$$TriggerEventImplCopyWith(
          _$TriggerEventImpl value, $Res Function(_$TriggerEventImpl) then) =
      __$$TriggerEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      TriggerType type,
      bool enabled,
      String title,
      String message,
      TriggerSchedule? schedule,
      Map<String, dynamic>? conditions,
      DateTime? lastTriggered,
      DateTime? createdAt,
      DateTime? updatedAt});

  @override
  $TriggerScheduleCopyWith<$Res>? get schedule;
}

/// @nodoc
class __$$TriggerEventImplCopyWithImpl<$Res>
    extends _$TriggerEventCopyWithImpl<$Res, _$TriggerEventImpl>
    implements _$$TriggerEventImplCopyWith<$Res> {
  __$$TriggerEventImplCopyWithImpl(
      _$TriggerEventImpl _value, $Res Function(_$TriggerEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? type = null,
    Object? enabled = null,
    Object? title = null,
    Object? message = null,
    Object? schedule = freezed,
    Object? conditions = freezed,
    Object? lastTriggered = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$TriggerEventImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TriggerType,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      schedule: freezed == schedule
          ? _value.schedule
          : schedule // ignore: cast_nullable_to_non_nullable
              as TriggerSchedule?,
      conditions: freezed == conditions
          ? _value._conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      lastTriggered: freezed == lastTriggered
          ? _value.lastTriggered
          : lastTriggered // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TriggerEventImpl extends _TriggerEvent {
  const _$TriggerEventImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.type,
      this.enabled = true,
      required this.title,
      required this.message,
      this.schedule,
      final Map<String, dynamic>? conditions,
      this.lastTriggered,
      this.createdAt,
      this.updatedAt})
      : _conditions = conditions,
        super._();

  factory _$TriggerEventImpl.fromJson(Map<String, dynamic> json) =>
      _$$TriggerEventImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final TriggerType type;
  @override
  @JsonKey()
  final bool enabled;
  @override
  final String title;
  @override
  final String message;
  @override
  final TriggerSchedule? schedule;
  final Map<String, dynamic>? _conditions;
  @override
  Map<String, dynamic>? get conditions {
    final value = _conditions;
    if (value == null) return null;
    if (_conditions is EqualUnmodifiableMapView) return _conditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime? lastTriggered;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'TriggerEvent(id: $id, name: $name, description: $description, type: $type, enabled: $enabled, title: $title, message: $message, schedule: $schedule, conditions: $conditions, lastTriggered: $lastTriggered, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TriggerEventImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.schedule, schedule) ||
                other.schedule == schedule) &&
            const DeepCollectionEquality()
                .equals(other._conditions, _conditions) &&
            (identical(other.lastTriggered, lastTriggered) ||
                other.lastTriggered == lastTriggered) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      type,
      enabled,
      title,
      message,
      schedule,
      const DeepCollectionEquality().hash(_conditions),
      lastTriggered,
      createdAt,
      updatedAt);

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TriggerEventImplCopyWith<_$TriggerEventImpl> get copyWith =>
      __$$TriggerEventImplCopyWithImpl<_$TriggerEventImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TriggerEventImplToJson(
      this,
    );
  }
}

abstract class _TriggerEvent extends TriggerEvent {
  const factory _TriggerEvent(
      {required final String id,
      required final String name,
      required final String description,
      required final TriggerType type,
      final bool enabled,
      required final String title,
      required final String message,
      final TriggerSchedule? schedule,
      final Map<String, dynamic>? conditions,
      final DateTime? lastTriggered,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$TriggerEventImpl;
  const _TriggerEvent._() : super._();

  factory _TriggerEvent.fromJson(Map<String, dynamic> json) =
      _$TriggerEventImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  TriggerType get type;
  @override
  bool get enabled;
  @override
  String get title;
  @override
  String get message;
  @override
  TriggerSchedule? get schedule;
  @override
  Map<String, dynamic>? get conditions;
  @override
  DateTime? get lastTriggered;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of TriggerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TriggerEventImplCopyWith<_$TriggerEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TriggerSchedule _$TriggerScheduleFromJson(Map<String, dynamic> json) {
  return _TriggerSchedule.fromJson(json);
}

/// @nodoc
mixin _$TriggerSchedule {
  ScheduleType get type => throw _privateConstructorUsedError;
  String? get time => throw _privateConstructorUsedError; // HH:mm format
  List<int>? get daysOfWeek =>
      throw _privateConstructorUsedError; // 1-7 (Monday-Sunday)
  int? get intervalHours => throw _privateConstructorUsedError;
  int? get intervalDays => throw _privateConstructorUsedError;

  /// Serializes this TriggerSchedule to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TriggerSchedule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TriggerScheduleCopyWith<TriggerSchedule> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TriggerScheduleCopyWith<$Res> {
  factory $TriggerScheduleCopyWith(
          TriggerSchedule value, $Res Function(TriggerSchedule) then) =
      _$TriggerScheduleCopyWithImpl<$Res, TriggerSchedule>;
  @useResult
  $Res call(
      {ScheduleType type,
      String? time,
      List<int>? daysOfWeek,
      int? intervalHours,
      int? intervalDays});
}

/// @nodoc
class _$TriggerScheduleCopyWithImpl<$Res, $Val extends TriggerSchedule>
    implements $TriggerScheduleCopyWith<$Res> {
  _$TriggerScheduleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TriggerSchedule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? time = freezed,
    Object? daysOfWeek = freezed,
    Object? intervalHours = freezed,
    Object? intervalDays = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ScheduleType,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String?,
      daysOfWeek: freezed == daysOfWeek
          ? _value.daysOfWeek
          : daysOfWeek // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      intervalHours: freezed == intervalHours
          ? _value.intervalHours
          : intervalHours // ignore: cast_nullable_to_non_nullable
              as int?,
      intervalDays: freezed == intervalDays
          ? _value.intervalDays
          : intervalDays // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TriggerScheduleImplCopyWith<$Res>
    implements $TriggerScheduleCopyWith<$Res> {
  factory _$$TriggerScheduleImplCopyWith(_$TriggerScheduleImpl value,
          $Res Function(_$TriggerScheduleImpl) then) =
      __$$TriggerScheduleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ScheduleType type,
      String? time,
      List<int>? daysOfWeek,
      int? intervalHours,
      int? intervalDays});
}

/// @nodoc
class __$$TriggerScheduleImplCopyWithImpl<$Res>
    extends _$TriggerScheduleCopyWithImpl<$Res, _$TriggerScheduleImpl>
    implements _$$TriggerScheduleImplCopyWith<$Res> {
  __$$TriggerScheduleImplCopyWithImpl(
      _$TriggerScheduleImpl _value, $Res Function(_$TriggerScheduleImpl) _then)
      : super(_value, _then);

  /// Create a copy of TriggerSchedule
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? time = freezed,
    Object? daysOfWeek = freezed,
    Object? intervalHours = freezed,
    Object? intervalDays = freezed,
  }) {
    return _then(_$TriggerScheduleImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ScheduleType,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as String?,
      daysOfWeek: freezed == daysOfWeek
          ? _value._daysOfWeek
          : daysOfWeek // ignore: cast_nullable_to_non_nullable
              as List<int>?,
      intervalHours: freezed == intervalHours
          ? _value.intervalHours
          : intervalHours // ignore: cast_nullable_to_non_nullable
              as int?,
      intervalDays: freezed == intervalDays
          ? _value.intervalDays
          : intervalDays // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TriggerScheduleImpl implements _TriggerSchedule {
  const _$TriggerScheduleImpl(
      {required this.type,
      this.time,
      final List<int>? daysOfWeek,
      this.intervalHours,
      this.intervalDays})
      : _daysOfWeek = daysOfWeek;

  factory _$TriggerScheduleImpl.fromJson(Map<String, dynamic> json) =>
      _$$TriggerScheduleImplFromJson(json);

  @override
  final ScheduleType type;
  @override
  final String? time;
// HH:mm format
  final List<int>? _daysOfWeek;
// HH:mm format
  @override
  List<int>? get daysOfWeek {
    final value = _daysOfWeek;
    if (value == null) return null;
    if (_daysOfWeek is EqualUnmodifiableListView) return _daysOfWeek;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 1-7 (Monday-Sunday)
  @override
  final int? intervalHours;
  @override
  final int? intervalDays;

  @override
  String toString() {
    return 'TriggerSchedule(type: $type, time: $time, daysOfWeek: $daysOfWeek, intervalHours: $intervalHours, intervalDays: $intervalDays)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TriggerScheduleImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.time, time) || other.time == time) &&
            const DeepCollectionEquality()
                .equals(other._daysOfWeek, _daysOfWeek) &&
            (identical(other.intervalHours, intervalHours) ||
                other.intervalHours == intervalHours) &&
            (identical(other.intervalDays, intervalDays) ||
                other.intervalDays == intervalDays));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      time,
      const DeepCollectionEquality().hash(_daysOfWeek),
      intervalHours,
      intervalDays);

  /// Create a copy of TriggerSchedule
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TriggerScheduleImplCopyWith<_$TriggerScheduleImpl> get copyWith =>
      __$$TriggerScheduleImplCopyWithImpl<_$TriggerScheduleImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TriggerScheduleImplToJson(
      this,
    );
  }
}

abstract class _TriggerSchedule implements TriggerSchedule {
  const factory _TriggerSchedule(
      {required final ScheduleType type,
      final String? time,
      final List<int>? daysOfWeek,
      final int? intervalHours,
      final int? intervalDays}) = _$TriggerScheduleImpl;

  factory _TriggerSchedule.fromJson(Map<String, dynamic> json) =
      _$TriggerScheduleImpl.fromJson;

  @override
  ScheduleType get type;
  @override
  String? get time; // HH:mm format
  @override
  List<int>? get daysOfWeek; // 1-7 (Monday-Sunday)
  @override
  int? get intervalHours;
  @override
  int? get intervalDays;

  /// Create a copy of TriggerSchedule
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TriggerScheduleImplCopyWith<_$TriggerScheduleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
