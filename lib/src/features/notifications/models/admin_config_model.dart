import 'package:freezed_annotation/freezed_annotation.dart';

part 'admin_config_model.freezed.dart';
part 'admin_config_model.g.dart';

@freezed
class AdminConfig with _$AdminConfig {
  const factory AdminConfig({
    @Default([]) List<String> adminEmails,
    @Default(true) bool notificationsEnabled,
    DateTime? lastUpdated,
  }) = _AdminConfig;

  const AdminConfig._();

  factory AdminConfig.fromJson(Map<String, dynamic> json) =>
      _$AdminConfigFromJson(json);

  factory AdminConfig.fromFirestore(Map<String, dynamic> data) {
    return AdminConfig(
      adminEmails: List<String>.from(data['adminEmails'] ?? []),
      notificationsEnabled: data['notificationsEnabled'] ?? true,
      lastUpdated: data['lastUpdated']?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'adminEmails': adminEmails,
      'notificationsEnabled': notificationsEnabled,
      'lastUpdated': DateTime.now(),
    };
  }
}
