// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdminConfigImpl _$$AdminConfigImplFromJson(Map<String, dynamic> json) =>
    _$AdminConfigImpl(
      adminEmails: (json['adminEmails'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$AdminConfigImplToJson(_$AdminConfigImpl instance) =>
    <String, dynamic>{
      'adminEmails': instance.adminEmails,
      'notificationsEnabled': instance.notificationsEnabled,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };
