import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    required String id,
    required String title,
    required String body,
    required NotificationType type,
    @Default(NotificationStatus.pending) NotificationStatus status,
    String? imageUrl,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
    DateTime? sentAt,
    DateTime? createdAt,
    String? createdBy,
  }) = _NotificationModel;

  const NotificationModel._();

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  factory NotificationModel.fromFirestore(
      Map<String, dynamic> data, String id) {
    return NotificationModel(
      id: id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => NotificationType.push,
      ),
      status: NotificationStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => NotificationStatus.pending,
      ),
      imageUrl: data['imageUrl'],
      data:
          data['data'] != null ? Map<String, dynamic>.from(data['data']) : null,
      scheduledAt: data['scheduledAt']?.toDate(),
      sentAt: data['sentAt']?.toDate(),
      createdAt: data['createdAt']?.toDate(),
      createdBy: data['createdBy'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'type': type.name,
      'status': status.name,
      'imageUrl': imageUrl,
      'data': data,
      'scheduledAt': scheduledAt,
      'sentAt': sentAt,
      'createdAt': createdAt ?? DateTime.now(),
      'createdBy': createdBy,
    };
  }
}

enum NotificationType {
  push,
  local,
}

enum NotificationStatus {
  pending,
  sent,
  failed,
  cancelled,
}
