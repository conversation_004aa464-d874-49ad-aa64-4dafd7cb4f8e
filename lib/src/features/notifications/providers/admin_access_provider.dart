import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/features/notifications/models/admin_config_model.dart';

part 'admin_access_provider.g.dart';

@riverpod
class AdminAccessNotifier extends _$AdminAccessNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Future<bool> build() async {
    final authState = ref.watch(authStateNotifierProvider);

    return authState.when(
      authenticated: (user) => _checkAdminAccess(user.email),
      unauthenticated: () => false,
      loading: () => false,
      initial: () => false,
      error: (error) => false,
    );
  }

  Future<bool> _checkAdminAccess(String? userEmail) async {
    if (userEmail == null) return false;

    try {
      final adminConfigDoc =
          await _firestore.collection('admin_config').doc('settings').get();

      if (!adminConfigDoc.exists) {
        debugPrint('Admin config document does not exist');
        return false;
      }

      final adminConfig = AdminConfig.fromFirestore(adminConfigDoc.data()!);
      final isAdmin = adminConfig.adminEmails.contains(userEmail.toLowerCase());

      debugPrint('Checking admin access for $userEmail: $isAdmin');
      return isAdmin;
    } catch (e) {
      debugPrint('Error checking admin access: $e');
      return false;
    }
  }

  Future<void> refreshAdminStatus() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => build());
  }
}

@riverpod
Future<AdminConfig?> adminConfig(Ref ref) async {
  try {
    final adminConfigDoc = await FirebaseFirestore.instance
        .collection('admin_config')
        .doc('settings')
        .get();

    if (!adminConfigDoc.exists) {
      return null;
    }

    return AdminConfig.fromFirestore(adminConfigDoc.data()!);
  } catch (e) {
    debugPrint('Error fetching admin config: $e');
    return null;
  }
}

@riverpod
Stream<AdminConfig?> adminConfigStream(Ref ref) {
  return FirebaseFirestore.instance
      .collection('admin_config')
      .doc('settings')
      .snapshots()
      .map((doc) {
    if (!doc.exists) return null;
    return AdminConfig.fromFirestore(doc.data()!);
  });
}
