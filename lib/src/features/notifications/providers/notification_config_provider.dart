import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/services/admin_notification_service.dart';
import 'package:vocadex/src/features/notifications/services/notification_service.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';

part 'notification_config_provider.g.dart';

@riverpod
class NotificationConfigNotifier extends _$NotificationConfigNotifier {
  @override
  Future<List<TriggerEvent>> build() async {
    final adminService = ref.watch(adminNotificationServiceProvider);
    return await adminService.getTriggerEvents();
  }

  /// Refresh trigger events from Firestore
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => build());
  }

  /// Update trigger event and refresh local notifications
  Future<void> updateTriggerEvent(TriggerEvent trigger) async {
    try {
      final adminService = ref.read(adminNotificationServiceProvider);
      await adminService.saveTriggerEvent(trigger);
      
      // Refresh the list
      await refresh();
      
      // Update local notifications
      await _updateLocalNotifications();
      
      debugPrint('Trigger event updated: ${trigger.id}');
    } catch (e) {
      debugPrint('Error updating trigger event: $e');
      rethrow;
    }
  }

  /// Delete trigger event
  Future<void> deleteTriggerEvent(String triggerId) async {
    try {
      final adminService = ref.read(adminNotificationServiceProvider);
      await adminService.deleteTriggerEvent(triggerId);
      
      // Refresh the list
      await refresh();
      
      // Update local notifications
      await _updateLocalNotifications();
      
      debugPrint('Trigger event deleted: $triggerId');
    } catch (e) {
      debugPrint('Error deleting trigger event: $e');
      rethrow;
    }
  }

  /// Setup default trigger events
  Future<void> setupDefaultTriggers() async {
    try {
      final adminService = ref.read(adminNotificationServiceProvider);
      await adminService.createDefaultTriggerEvents();
      
      // Refresh the list
      await refresh();
      
      // Update local notifications
      await _updateLocalNotifications();
      
      debugPrint('Default trigger events created');
    } catch (e) {
      debugPrint('Error creating default trigger events: $e');
      rethrow;
    }
  }

  /// Update local notifications based on current trigger events
  Future<void> _updateLocalNotifications() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final currentTriggers = state.valueOrNull ?? [];
      
      // Setup trigger events in local notification service
      await notificationService.setupTriggerEvents(currentTriggers);
      
      debugPrint('Local notifications updated with ${currentTriggers.length} triggers');
    } catch (e) {
      debugPrint('Error updating local notifications: $e');
    }
  }
}

@riverpod
Stream<List<TriggerEvent>> triggerEventsStream(Ref ref) {
  final adminService = ref.watch(adminNotificationServiceProvider);
  return adminService.watchTriggerEvents();
}

@riverpod
Future<bool> notificationPermissions(Ref ref) async {
  try {
    final notificationService = ref.watch(notificationServiceProvider);
    return await notificationService.areNotificationsEnabled();
  } catch (e) {
    debugPrint('Error checking notification permissions: $e');
    return false;
  }
}

@riverpod
class NotificationPermissionNotifier extends _$NotificationPermissionNotifier {
  @override
  Future<bool> build() async {
    final notificationService = ref.watch(notificationServiceProvider);
    return await notificationService.areNotificationsEnabled();
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final granted = await notificationService.requestPermissions();
      
      // Update state
      state = AsyncData(granted);
      
      return granted;
    } catch (e) {
      debugPrint('Error requesting notification permissions: $e');
      state = const AsyncData(false);
      return false;
    }
  }

  /// Refresh permission status
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => build());
  }
}
