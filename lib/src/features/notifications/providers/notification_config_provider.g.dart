// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_config_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$triggerEventsStreamHash() =>
    r'b9debf9238647518473cb68ca709e6eb861628a5';

/// See also [triggerEventsStream].
@ProviderFor(triggerEventsStream)
final triggerEventsStreamProvider =
    AutoDisposeStreamProvider<List<TriggerEvent>>.internal(
  triggerEventsStream,
  name: r'triggerEventsStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$triggerEventsStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TriggerEventsStreamRef
    = AutoDisposeStreamProviderRef<List<TriggerEvent>>;
String _$notificationPermissionsHash() =>
    r'ec21105a3c92792eb80bedbdad3d64595f24e903';

/// See also [notificationPermissions].
@ProviderFor(notificationPermissions)
final notificationPermissionsProvider =
    AutoDisposeFutureProvider<bool>.internal(
  notificationPermissions,
  name: r'notificationPermissionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationPermissionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationPermissionsRef = AutoDisposeFutureProviderRef<bool>;
String _$notificationConfigNotifierHash() =>
    r'598990464294e0c23effe319f7fbf0697cfa7ee8';

/// See also [NotificationConfigNotifier].
@ProviderFor(NotificationConfigNotifier)
final notificationConfigNotifierProvider = AutoDisposeAsyncNotifierProvider<
    NotificationConfigNotifier, List<TriggerEvent>>.internal(
  NotificationConfigNotifier.new,
  name: r'notificationConfigNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationConfigNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationConfigNotifier
    = AutoDisposeAsyncNotifier<List<TriggerEvent>>;
String _$notificationPermissionNotifierHash() =>
    r'7b5e8733f9d663bb4e49175936f2f169d7c5ec5c';

/// See also [NotificationPermissionNotifier].
@ProviderFor(NotificationPermissionNotifier)
final notificationPermissionNotifierProvider = AutoDisposeAsyncNotifierProvider<
    NotificationPermissionNotifier, bool>.internal(
  NotificationPermissionNotifier.new,
  name: r'notificationPermissionNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationPermissionNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationPermissionNotifier = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
