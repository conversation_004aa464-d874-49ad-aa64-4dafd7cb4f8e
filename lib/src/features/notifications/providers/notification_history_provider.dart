import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/notifications/services/admin_notification_service.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';

part 'notification_history_provider.g.dart';

@riverpod
class NotificationHistoryNotifier extends _$NotificationHistoryNotifier {
  @override
  Future<List<NotificationModel>> build() async {
    final adminService = ref.watch(adminNotificationServiceProvider);
    return await adminService.getNotificationHistory();
  }

  /// Refresh notification history
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => build());
  }

  /// Get notifications by status
  List<NotificationModel> getNotificationsByStatus(NotificationStatus status) {
    final notifications = state.valueOrNull ?? [];
    return notifications.where((n) => n.status == status).toList();
  }

  /// Get notifications from last N days
  List<NotificationModel> getNotificationsFromLastDays(int days) {
    final notifications = state.valueOrNull ?? [];
    final cutoffDate = DateTime.now().subtract(Duration(days: days));

    return notifications.where((n) {
      final date = n.sentAt ?? n.createdAt;
      return date != null && date.isAfter(cutoffDate);
    }).toList();
  }

  /// Get notification analytics
  Map<String, dynamic> getAnalytics() {
    final notifications = state.valueOrNull ?? [];

    if (notifications.isEmpty) {
      return {
        'totalNotifications': 0,
        'sentNotifications': 0,
        'failedNotifications': 0,
        'pendingNotifications': 0,
        'successRate': 0.0,
        'thisWeekCount': 0,
        'todayCount': 0,
      };
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekAgo = now.subtract(const Duration(days: 7));

    final totalNotifications = notifications.length;
    final sentNotifications =
        notifications.where((n) => n.status == NotificationStatus.sent).length;
    final failedNotifications = notifications
        .where((n) => n.status == NotificationStatus.failed)
        .length;
    final pendingNotifications = notifications
        .where((n) => n.status == NotificationStatus.pending)
        .length;

    final successRate = totalNotifications > 0
        ? (sentNotifications / totalNotifications) * 100
        : 0.0;

    final thisWeekCount = notifications.where((n) {
      final date = n.sentAt ?? n.createdAt;
      return date != null && date.isAfter(weekAgo);
    }).length;

    final todayCount = notifications.where((n) {
      final date = n.sentAt ?? n.createdAt;
      return date != null &&
          date.year == today.year &&
          date.month == today.month &&
          date.day == today.day;
    }).length;

    return {
      'totalNotifications': totalNotifications,
      'sentNotifications': sentNotifications,
      'failedNotifications': failedNotifications,
      'pendingNotifications': pendingNotifications,
      'successRate': successRate.toStringAsFixed(1),
      'thisWeekCount': thisWeekCount,
      'todayCount': todayCount,
    };
  }

  /// Get daily notification counts for the last 7 days
  List<Map<String, dynamic>> getDailyNotificationCounts() {
    final notifications = state.valueOrNull ?? [];
    final now = DateTime.now();
    final dailyCounts = <Map<String, dynamic>>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final dayNotifications = notifications.where((n) {
        final notificationDate = n.sentAt ?? n.createdAt;
        return notificationDate != null &&
            notificationDate.isAfter(dayStart) &&
            notificationDate.isBefore(dayEnd);
      }).toList();

      dailyCounts.add({
        'date': dayStart,
        'count': dayNotifications.length,
        'sent': dayNotifications
            .where((n) => n.status == NotificationStatus.sent)
            .length,
        'failed': dayNotifications
            .where((n) => n.status == NotificationStatus.failed)
            .length,
      });
    }

    return dailyCounts;
  }

  /// Get most recent notifications
  List<NotificationModel> getRecentNotifications({int limit = 5}) {
    final notifications = state.valueOrNull ?? [];
    final sortedNotifications = List<NotificationModel>.from(notifications);

    sortedNotifications.sort((a, b) {
      final dateA = a.sentAt ?? a.createdAt ?? DateTime.now();
      final dateB = b.sentAt ?? b.createdAt ?? DateTime.now();
      return dateB.compareTo(dateA);
    });

    return sortedNotifications.take(limit).toList();
  }
}

@riverpod
Stream<List<NotificationModel>> notificationHistoryStream(Ref ref) {
  final adminService = ref.watch(adminNotificationServiceProvider);
  return adminService.watchNotificationHistory();
}

@riverpod
Future<Map<String, dynamic>> notificationAnalytics(Ref ref) async {
  final historyNotifier =
      ref.watch(notificationHistoryNotifierProvider.notifier);
  return historyNotifier.getAnalytics();
}

@riverpod
Future<List<Map<String, dynamic>>> dailyNotificationCounts(Ref ref) async {
  final historyNotifier =
      ref.watch(notificationHistoryNotifierProvider.notifier);
  return historyNotifier.getDailyNotificationCounts();
}
