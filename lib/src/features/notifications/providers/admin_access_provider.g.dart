// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_access_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminConfigHash() => r'92cee9f4b76e34f0983698eb95b3dd10818605e2';

/// See also [adminConfig].
@ProviderFor(adminConfig)
final adminConfigProvider = AutoDisposeFutureProvider<AdminConfig?>.internal(
  adminConfig,
  name: r'adminConfigProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$adminConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminConfigRef = AutoDisposeFutureProviderRef<AdminConfig?>;
String _$adminConfigStreamHash() => r'80d830359e86e600b29829d6ec7dc2af7396dff7';

/// See also [adminConfigStream].
@ProviderFor(adminConfigStream)
final adminConfigStreamProvider =
    AutoDisposeStreamProvider<AdminConfig?>.internal(
  adminConfigStream,
  name: r'adminConfigStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminConfigStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminConfigStreamRef = AutoDisposeStreamProviderRef<AdminConfig?>;
String _$adminAccessNotifierHash() =>
    r'e7312d2df3e0ceadbd11ecde7e6f9ec3967e5f34';

/// See also [AdminAccessNotifier].
@ProviderFor(AdminAccessNotifier)
final adminAccessNotifierProvider =
    AutoDisposeAsyncNotifierProvider<AdminAccessNotifier, bool>.internal(
  AdminAccessNotifier.new,
  name: r'adminAccessNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminAccessNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AdminAccessNotifier = AutoDisposeAsyncNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
