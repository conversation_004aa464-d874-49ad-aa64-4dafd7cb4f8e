// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationHistoryStreamHash() =>
    r'e8c74d6e9aded4108ce828a84562ef423bff2dd4';

/// See also [notificationHistoryStream].
@ProviderFor(notificationHistoryStream)
final notificationHistoryStreamProvider =
    AutoDisposeStreamProvider<List<NotificationModel>>.internal(
  notificationHistoryStream,
  name: r'notificationHistoryStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationHistoryStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationHistoryStreamRef
    = AutoDisposeStreamProviderRef<List<NotificationModel>>;
String _$notificationAnalyticsHash() =>
    r'64536efa23163c3fde033c329331094f2bc16be0';

/// See also [notificationAnalytics].
@ProviderFor(notificationAnalytics)
final notificationAnalyticsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
  notificationAnalytics,
  name: r'notificationAnalyticsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationAnalyticsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationAnalyticsRef
    = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$dailyNotificationCountsHash() =>
    r'ee6b146c3128bb2e08cff3c1f08ab56c10238db8';

/// See also [dailyNotificationCounts].
@ProviderFor(dailyNotificationCounts)
final dailyNotificationCountsProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  dailyNotificationCounts,
  name: r'dailyNotificationCountsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dailyNotificationCountsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DailyNotificationCountsRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$notificationHistoryNotifierHash() =>
    r'c594081101dfe67ca3f2059028e4e5868720220f';

/// See also [NotificationHistoryNotifier].
@ProviderFor(NotificationHistoryNotifier)
final notificationHistoryNotifierProvider = AutoDisposeAsyncNotifierProvider<
    NotificationHistoryNotifier, List<NotificationModel>>.internal(
  NotificationHistoryNotifier.new,
  name: r'notificationHistoryNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationHistoryNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationHistoryNotifier
    = AutoDisposeAsyncNotifier<List<NotificationModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
