import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';

class TriggerEventFactory {
  /// Create all default trigger events for the Vocadex app
  static List<TriggerEvent> createAllDefaultTriggers() {
    return [
      createDailyStudyReminder(),
      createWeeklyGoalCheck(),
      createStreakReminder(),
      createDailyWordNotification(),
      createLowDiamondsAlert(),
      createMasteryProgressNotification(),
      createInactivityReminder(),
      createAchievementUnlockNotification(),
    ];
  }

  /// Daily study reminder - encourages daily practice
  static TriggerEvent createDailyStudyReminder() {
    return TriggerEvent(
      id: 'daily_study_reminder',
      name: 'Daily Study Reminder',
      description: 'Remind users to practice vocabulary daily',
      type: TriggerType.dailyReminder,
      enabled: true,
      title: 'Time to Study! 📚',
      message: 'Don\'t forget to practice your vocabulary today and keep your streak alive!',
      schedule: const TriggerSchedule(
        type: ScheduleType.daily,
        time: '19:00', // 7 PM
      ),
      conditions: {
        'requiresUserActivity': false,
        'skipIfStudiedToday': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Weekly goal progress check
  static TriggerEvent createWeeklyGoalCheck() {
    return TriggerEvent(
      id: 'weekly_goal_check',
      name: 'Weekly Goal Progress',
      description: 'Check progress on weekly vocabulary goals',
      type: TriggerType.weeklyGoal,
      enabled: true,
      title: 'Weekly Progress Check 🎯',
      message: 'How are you doing with your weekly vocabulary goals? Check your progress!',
      schedule: const TriggerSchedule(
        type: ScheduleType.weekly,
        daysOfWeek: [3], // Wednesday
        time: '18:00', // 6 PM
      ),
      conditions: {
        'requiresActiveGoals': true,
        'skipIfGoalsCompleted': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Streak maintenance reminder
  static TriggerEvent createStreakReminder() {
    return TriggerEvent(
      id: 'streak_reminder',
      name: 'Streak Maintenance',
      description: 'Remind users to maintain their learning streak',
      type: TriggerType.streakReminder,
      enabled: true,
      title: 'Keep Your Streak! 🔥',
      message: 'Don\'t break your learning streak - study today to keep it going!',
      schedule: const TriggerSchedule(
        type: ScheduleType.daily,
        time: '20:00', // 8 PM
      ),
      conditions: {
        'requiresActiveStreak': true,
        'skipIfStudiedToday': true,
        'minimumStreakDays': 3,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Daily word notification
  static TriggerEvent createDailyWordNotification() {
    return TriggerEvent(
      id: 'daily_word_notification',
      name: 'Daily Word Available',
      description: 'Notify when new daily word is available',
      type: TriggerType.dailyWord,
      enabled: true,
      title: 'New Daily Word! 🌟',
      message: 'A new daily word is available. Expand your vocabulary today!',
      schedule: const TriggerSchedule(
        type: ScheduleType.daily,
        time: '09:00', // 9 AM
      ),
      conditions: {
        'skipIfAlreadyViewed': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Low diamonds alert
  static TriggerEvent createLowDiamondsAlert() {
    return TriggerEvent(
      id: 'low_diamonds_alert',
      name: 'Low Diamonds Alert',
      description: 'Alert when user is running low on diamonds',
      type: TriggerType.lowDiamonds,
      enabled: true,
      title: 'Running Low on Diamonds! 💎',
      message: 'Your diamonds are running low. Complete lessons to earn more!',
      schedule: const TriggerSchedule(
        type: ScheduleType.interval,
        intervalHours: 6, // Check every 6 hours
      ),
      conditions: {
        'diamondThreshold': 5,
        'skipIfPremium': true,
        'maxAlertsPerDay': 2,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Mastery progress notification
  static TriggerEvent createMasteryProgressNotification() {
    return TriggerEvent(
      id: 'mastery_progress_notification',
      name: 'Mastery Progress Update',
      description: 'Notify about vocabulary mastery progress',
      type: TriggerType.masteryProgress,
      enabled: true,
      title: 'Great Progress! 📈',
      message: 'You\'re making excellent progress! Keep studying to master more words.',
      schedule: const TriggerSchedule(
        type: ScheduleType.weekly,
        daysOfWeek: [7], // Sunday
        time: '17:00', // 5 PM
      ),
      conditions: {
        'requiresProgress': true,
        'minimumWordsLearned': 5,
        'skipIfNoActivity': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Inactivity reminder
  static TriggerEvent createInactivityReminder() {
    return TriggerEvent(
      id: 'inactivity_reminder',
      name: 'Inactivity Reminder',
      description: 'Remind inactive users to return to learning',
      type: TriggerType.inactivityReminder,
      enabled: true,
      title: 'We Miss You! 👋',
      message: 'Come back and continue your vocabulary journey. Your words are waiting!',
      schedule: const TriggerSchedule(
        type: ScheduleType.interval,
        intervalDays: 3, // After 3 days of inactivity
      ),
      conditions: {
        'inactivityDays': 3,
        'maxReminders': 3,
        'skipIfRecentActivity': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Achievement unlock notification
  static TriggerEvent createAchievementUnlockNotification() {
    return TriggerEvent(
      id: 'achievement_unlock_notification',
      name: 'Achievement Unlocked',
      description: 'Celebrate when users unlock achievements',
      type: TriggerType.achievementUnlock,
      enabled: true,
      title: 'Achievement Unlocked! 🏆',
      message: 'Congratulations! You\'ve unlocked a new achievement. Keep up the great work!',
      schedule: const TriggerSchedule(
        type: ScheduleType.once, // Triggered by events, not scheduled
      ),
      conditions: {
        'triggerOnAchievement': true,
        'celebrateImmediately': true,
      },
      createdAt: DateTime.now(),
    );
  }

  /// Create a custom trigger event
  static TriggerEvent createCustomTrigger({
    required String id,
    required String name,
    required String description,
    required TriggerType type,
    required String title,
    required String message,
    bool enabled = true,
    TriggerSchedule? schedule,
    Map<String, dynamic>? conditions,
  }) {
    return TriggerEvent(
      id: id,
      name: name,
      description: description,
      type: type,
      enabled: enabled,
      title: title,
      message: message,
      schedule: schedule,
      conditions: conditions,
      createdAt: DateTime.now(),
    );
  }

  /// Get trigger event by type
  static TriggerEvent? getTriggerByType(TriggerType type) {
    switch (type) {
      case TriggerType.dailyReminder:
        return createDailyStudyReminder();
      case TriggerType.weeklyGoal:
        return createWeeklyGoalCheck();
      case TriggerType.streakReminder:
        return createStreakReminder();
      case TriggerType.dailyWord:
        return createDailyWordNotification();
      case TriggerType.lowDiamonds:
        return createLowDiamondsAlert();
      case TriggerType.masteryProgress:
        return createMasteryProgressNotification();
      case TriggerType.inactivityReminder:
        return createInactivityReminder();
      case TriggerType.achievementUnlock:
        return createAchievementUnlockNotification();
    }
  }

  /// Get user-friendly description for trigger type
  static String getTriggerTypeDescription(TriggerType type) {
    switch (type) {
      case TriggerType.dailyReminder:
        return 'Daily study reminders to maintain learning habits';
      case TriggerType.weeklyGoal:
        return 'Weekly progress checks on vocabulary goals';
      case TriggerType.streakReminder:
        return 'Streak maintenance reminders to keep momentum';
      case TriggerType.dailyWord:
        return 'Notifications for new daily vocabulary words';
      case TriggerType.lowDiamonds:
        return 'Alerts when diamond count is running low';
      case TriggerType.masteryProgress:
        return 'Progress updates on vocabulary mastery';
      case TriggerType.inactivityReminder:
        return 'Gentle reminders for inactive users';
      case TriggerType.achievementUnlock:
        return 'Celebrations for unlocked achievements';
    }
  }

  /// Get recommended schedule for trigger type
  static TriggerSchedule getRecommendedSchedule(TriggerType type) {
    switch (type) {
      case TriggerType.dailyReminder:
        return const TriggerSchedule(type: ScheduleType.daily, time: '19:00');
      case TriggerType.weeklyGoal:
        return const TriggerSchedule(
          type: ScheduleType.weekly,
          daysOfWeek: [3],
          time: '18:00',
        );
      case TriggerType.streakReminder:
        return const TriggerSchedule(type: ScheduleType.daily, time: '20:00');
      case TriggerType.dailyWord:
        return const TriggerSchedule(type: ScheduleType.daily, time: '09:00');
      case TriggerType.lowDiamonds:
        return const TriggerSchedule(
          type: ScheduleType.interval,
          intervalHours: 6,
        );
      case TriggerType.masteryProgress:
        return const TriggerSchedule(
          type: ScheduleType.weekly,
          daysOfWeek: [7],
          time: '17:00',
        );
      case TriggerType.inactivityReminder:
        return const TriggerSchedule(
          type: ScheduleType.interval,
          intervalDays: 3,
        );
      case TriggerType.achievementUnlock:
        return const TriggerSchedule(type: ScheduleType.once);
    }
  }
}
