import 'package:flutter/foundation.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:vocadex/src/features/notifications/services/local_notification_service.dart';

class NotificationScheduler {
  final LocalNotificationService _localService;

  NotificationScheduler(this._localService);

  /// Schedule all trigger events
  Future<void> scheduleAllTriggers(List<TriggerEvent> triggers) async {
    try {
      // Cancel all existing notifications first
      await _localService.cancelAllNotifications();

      // Schedule each enabled trigger
      for (final trigger in triggers) {
        if (trigger.enabled && trigger.schedule != null) {
          await _scheduleTrigger(trigger);
        }
      }

      debugPrint('Scheduled ${triggers.where((t) => t.enabled).length} triggers');
    } catch (e) {
      debugPrint('Error scheduling triggers: $e');
      rethrow;
    }
  }

  /// Schedule a single trigger event
  Future<void> _scheduleTrigger(TriggerEvent trigger) async {
    final schedule = trigger.schedule!;
    final now = DateTime.now();

    switch (schedule.type) {
      case ScheduleType.daily:
        await _scheduleDailyTrigger(trigger, now);
        break;
      case ScheduleType.weekly:
        await _scheduleWeeklyTrigger(trigger, now);
        break;
      case ScheduleType.interval:
        await _scheduleIntervalTrigger(trigger, now);
        break;
      case ScheduleType.once:
        await _scheduleOnceTrigger(trigger, now);
        break;
    }
  }

  /// Schedule daily recurring trigger
  Future<void> _scheduleDailyTrigger(TriggerEvent trigger, DateTime now) async {
    final schedule = trigger.schedule!;
    if (schedule.time == null) return;

    final timeParts = schedule.time!.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    // Schedule for today if time hasn't passed, otherwise tomorrow
    var scheduledTime = DateTime(now.year, now.month, now.day, hour, minute);
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    final notification = NotificationModel(
      id: '${trigger.id}_daily',
      title: trigger.title,
      body: trigger.message,
      type: NotificationType.local,
      data: {
        'triggerId': trigger.id,
        'triggerType': trigger.type.name,
        'scheduleType': 'daily',
      },
    );

    await _localService.scheduleNotification(notification, scheduledTime);
    debugPrint('Scheduled daily trigger ${trigger.id} for $scheduledTime');
  }

  /// Schedule weekly recurring trigger
  Future<void> _scheduleWeeklyTrigger(TriggerEvent trigger, DateTime now) async {
    final schedule = trigger.schedule!;
    if (schedule.time == null || schedule.daysOfWeek == null || schedule.daysOfWeek!.isEmpty) {
      return;
    }

    final timeParts = schedule.time!.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    for (final dayOfWeek in schedule.daysOfWeek!) {
      // Calculate next occurrence of this day of week
      final daysUntilTarget = (dayOfWeek - now.weekday) % 7;
      var targetDate = now.add(Duration(days: daysUntilTarget));
      
      // Set the specific time
      var scheduledTime = DateTime(
        targetDate.year,
        targetDate.month,
        targetDate.day,
        hour,
        minute,
      );

      // If it's today but time has passed, schedule for next week
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 7));
      }

      final notification = NotificationModel(
        id: '${trigger.id}_weekly_$dayOfWeek',
        title: trigger.title,
        body: trigger.message,
        type: NotificationType.local,
        data: {
          'triggerId': trigger.id,
          'triggerType': trigger.type.name,
          'scheduleType': 'weekly',
          'dayOfWeek': dayOfWeek,
        },
      );

      await _localService.scheduleNotification(notification, scheduledTime);
      debugPrint('Scheduled weekly trigger ${trigger.id} for $scheduledTime (day $dayOfWeek)');
    }
  }

  /// Schedule interval-based trigger
  Future<void> _scheduleIntervalTrigger(TriggerEvent trigger, DateTime now) async {
    final schedule = trigger.schedule!;
    
    Duration interval;
    if (schedule.intervalHours != null) {
      interval = Duration(hours: schedule.intervalHours!);
    } else if (schedule.intervalDays != null) {
      interval = Duration(days: schedule.intervalDays!);
    } else {
      return; // No valid interval
    }

    // Schedule first occurrence
    final scheduledTime = now.add(interval);

    final notification = NotificationModel(
      id: '${trigger.id}_interval',
      title: trigger.title,
      body: trigger.message,
      type: NotificationType.local,
      data: {
        'triggerId': trigger.id,
        'triggerType': trigger.type.name,
        'scheduleType': 'interval',
        'intervalHours': schedule.intervalHours,
        'intervalDays': schedule.intervalDays,
      },
    );

    await _localService.scheduleNotification(notification, scheduledTime);
    debugPrint('Scheduled interval trigger ${trigger.id} for $scheduledTime');
  }

  /// Schedule one-time trigger
  Future<void> _scheduleOnceTrigger(TriggerEvent trigger, DateTime now) async {
    // For one-time triggers, we might schedule them based on specific conditions
    // For now, schedule them for 1 hour from now as an example
    final scheduledTime = now.add(const Duration(hours: 1));

    final notification = NotificationModel(
      id: '${trigger.id}_once',
      title: trigger.title,
      body: trigger.message,
      type: NotificationType.local,
      data: {
        'triggerId': trigger.id,
        'triggerType': trigger.type.name,
        'scheduleType': 'once',
      },
    );

    await _localService.scheduleNotification(notification, scheduledTime);
    debugPrint('Scheduled one-time trigger ${trigger.id} for $scheduledTime');
  }

  /// Cancel specific trigger
  Future<void> cancelTrigger(String triggerId) async {
    try {
      // Cancel all variations of this trigger
      await _localService.cancelNotification('${triggerId}_daily');
      await _localService.cancelNotification('${triggerId}_interval');
      await _localService.cancelNotification('${triggerId}_once');
      
      // Cancel weekly variations (days 1-7)
      for (int day = 1; day <= 7; day++) {
        await _localService.cancelNotification('${triggerId}_weekly_$day');
      }
      
      debugPrint('Cancelled all notifications for trigger: $triggerId');
    } catch (e) {
      debugPrint('Error cancelling trigger $triggerId: $e');
    }
  }

  /// Get next scheduled time for a trigger (for display purposes)
  DateTime? getNextScheduledTime(TriggerEvent trigger) {
    if (!trigger.enabled || trigger.schedule == null) return null;

    final schedule = trigger.schedule!;
    final now = DateTime.now();

    switch (schedule.type) {
      case ScheduleType.daily:
        if (schedule.time == null) return null;
        final timeParts = schedule.time!.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        
        var nextTime = DateTime(now.year, now.month, now.day, hour, minute);
        if (nextTime.isBefore(now)) {
          nextTime = nextTime.add(const Duration(days: 1));
        }
        return nextTime;

      case ScheduleType.weekly:
        if (schedule.time == null || schedule.daysOfWeek == null || schedule.daysOfWeek!.isEmpty) {
          return null;
        }
        
        final timeParts = schedule.time!.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        
        // Find the next occurrence
        DateTime? nextTime;
        for (final dayOfWeek in schedule.daysOfWeek!) {
          final daysUntilTarget = (dayOfWeek - now.weekday) % 7;
          var targetDate = now.add(Duration(days: daysUntilTarget));
          var candidateTime = DateTime(
            targetDate.year,
            targetDate.month,
            targetDate.day,
            hour,
            minute,
          );
          
          if (candidateTime.isBefore(now)) {
            candidateTime = candidateTime.add(const Duration(days: 7));
          }
          
          if (nextTime == null || candidateTime.isBefore(nextTime)) {
            nextTime = candidateTime;
          }
        }
        return nextTime;

      case ScheduleType.interval:
        Duration interval;
        if (schedule.intervalHours != null) {
          interval = Duration(hours: schedule.intervalHours!);
        } else if (schedule.intervalDays != null) {
          interval = Duration(days: schedule.intervalDays!);
        } else {
          return null;
        }
        return now.add(interval);

      case ScheduleType.once:
        // For one-time triggers, return 1 hour from now as example
        return now.add(const Duration(hours: 1));
    }
  }
}
