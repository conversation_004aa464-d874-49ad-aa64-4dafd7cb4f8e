import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/notifications/services/admin_notification_service.dart';
import 'package:vocadex/src/features/notifications/models/notification_model.dart';
import 'package:intl/intl.dart';

class NotificationAnalyticsCard extends ConsumerWidget {
  const NotificationAnalyticsCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final adminService = ref.watch(adminNotificationServiceProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Colors.purple,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Notification Analytics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            StreamBuilder<List<NotificationModel>>(
              stream: adminService.watchNotificationHistory(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Error loading analytics: ${snapshot.error}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  );
                }

                final notifications = snapshot.data ?? [];
                final analytics = _calculateAnalytics(notifications);

                return Column(
                  children: [
                    // Summary Cards
                    Row(
                      children: [
                        Expanded(
                          child: _AnalyticsCard(
                            title: 'Total Sent',
                            value: analytics['totalSent'].toString(),
                            icon: Icons.send,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _AnalyticsCard(
                            title: 'Success Rate',
                            value: '${analytics['successRate']}%',
                            icon: Icons.check_circle,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _AnalyticsCard(
                            title: 'This Week',
                            value: analytics['thisWeek'].toString(),
                            icon: Icons.calendar_view_week,
                            color: Colors.orange,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _AnalyticsCard(
                            title: 'Failed',
                            value: analytics['failed'].toString(),
                            icon: Icons.error,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Recent Activity
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Recent Activity',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    if (notifications.isEmpty)
                      const Center(
                        child: Text(
                          'No recent activity',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                        ),
                      )
                    else
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: notifications.take(3).length,
                        itemBuilder: (context, index) {
                          final notification = notifications[index];
                          return _RecentActivityItem(
                              notification: notification);
                        },
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _calculateAnalytics(
      List<NotificationModel> notifications) {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));

    final totalSent =
        notifications.where((n) => n.status == NotificationStatus.sent).length;
    final failed = notifications
        .where((n) => n.status == NotificationStatus.failed)
        .length;
    final thisWeek = notifications.where((n) {
      final createdAt = n.createdAt ?? n.sentAt;
      return createdAt != null && createdAt.isAfter(weekAgo);
    }).length;

    final successRate = notifications.isEmpty
        ? 0
        : ((totalSent / notifications.length) * 100).round();

    return {
      'totalSent': totalSent,
      'failed': failed,
      'thisWeek': thisWeek,
      'successRate': successRate,
    };
  }
}

class _AnalyticsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _AnalyticsCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

class _RecentActivityItem extends StatelessWidget {
  final NotificationModel notification;

  const _RecentActivityItem({
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM dd, HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: _getStatusColor().withValues(alpha: 0.2),
            child: Icon(
              _getStatusIcon(),
              color: _getStatusColor(),
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  notification.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  notification.sentAt != null
                      ? dateFormat.format(notification.sentAt!)
                      : dateFormat
                          .format(notification.createdAt ?? DateTime.now()),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 6,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor().withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              notification.status.name.toUpperCase(),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: _getStatusColor(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (notification.status) {
      case NotificationStatus.sent:
        return Colors.green;
      case NotificationStatus.pending:
        return Colors.orange;
      case NotificationStatus.failed:
        return Colors.red;
      case NotificationStatus.cancelled:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (notification.status) {
      case NotificationStatus.sent:
        return Icons.check_circle;
      case NotificationStatus.pending:
        return Icons.schedule;
      case NotificationStatus.failed:
        return Icons.error;
      case NotificationStatus.cancelled:
        return Icons.cancel;
    }
  }
}
