// ignore_for_file: prefer_const_constructors, prefer_const_literals_to_create_immutables

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/points/points_provider.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_provider.dart';
import 'package:vocadex/src/features/dashboard/widgets/dashboard_greeting.dart';
import 'package:vocadex/src/features/dashboard/widgets/total_cards.dart';
import 'package:vocadex/src/features/dashboard/widgets/word_of_the_day.dart';
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/weekly_target/weekly_targets_widget.dart';
import 'package:vocadex/src/services/firebase_service.dart';

// Define providers for XP and blank cards outside the widget
final xpProvider = userPointsProvider;

final blankCardsProvider = FutureProvider<int>((ref) async {
  // Watch the refresh provider to update when it changes
  ref.watch(dashboardRefreshProvider);
  final firebaseService = FirebaseService();
  final isPremium = ref.watch(subscriptionStateProvider);

  // For premium users, return -1 to represent infinity
  if (isPremium) return -1;

  // For free users, calculate remaining cards
  final details = await firebaseService.getUserAllocationDetails();
  final used = details['allocation_used_today'] as int;
  final limit = details['allocation_limit'] as int;
  return limit - used;
});

// Override the diamonds provider to refresh when dashboardRefreshProvider changes
final enhancedDiamondsProvider = FutureProvider<int>((ref) async {
  // Watch the refresh provider to update when it changes
  ref.watch(dashboardRefreshProvider);
  final firebaseService = FirebaseService();
  await firebaseService.resetDiamondsIfNeeded(); // Ensure daily reset
  return await firebaseService.getDiamonds();
});

class Dashboard extends ConsumerStatefulWidget {
  const Dashboard({super.key});

  @override
  ConsumerState<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends ConsumerState<Dashboard> {
  @override
  void initState() {
    super.initState();
    // Record daily visit when dashboard loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recordDailyVisitProvider);
    });
  }

  Future<void> _refreshDashboard() async {
    if (!mounted) return;
    
    try {
      // Update the refresh timestamp to trigger a refresh of all dependent providers
      ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
      
      // Force refresh the blank cards provider
      final newValue = await ref.refresh(blankCardsProvider.future);
      debugPrint('Refreshed blank cards: $newValue');
      
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error refreshing dashboard: $e');
    }
  }

  // Build dashboard greeting with profile icon
  Widget _buildDashboardGreetingWithProfile(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Expanded(
          child: DashboardGreeting(),
        ),
        GestureDetector(
          onTap: () => _navigateToProfile(context),
          child: Container(
            padding: const EdgeInsets.all(10),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppColors.getPrimaryColor(Theme.of(context).brightness).withAlpha(26),shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.getPrimaryColor(Theme.of(context).brightness).withAlpha(204),width: 1,
              ),
            ),
            child: Icon(
              Icons.person,
              color: AppColors.getTextColor(Theme.of(context).brightness),
              size: 28,
            ),
          ),
        ),
      ],
    );
  }

  // Navigate to profile screen
  void _navigateToProfile(BuildContext context) {
    context.pushNamed(RouteNames.profile);
  }

  @override
  Widget build(BuildContext context) {
    return GradientScaffold(
      body: RefreshIndicator(
        onRefresh: _refreshDashboard,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 40),
            child: Column(
              spacing: 24,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CompactResourceBar(),
                _buildDashboardGreetingWithProfile(context),
                TotalCardsWidget(),
                const WeeklyTargetsWidget(),
                const WordOfTheDay(),
                const StreakSummaryWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Replace this with your actual streak provider
final currentStreakProvider = FutureProvider<int>((ref) async {
  await Future.delayed(const Duration(milliseconds: 300)); // Simulated delay
  return 2; // Simulated streak value
});

// class StreakSummaryWidget extends ConsumerWidget {
//   const StreakSummaryWidget({super.key});

//   @override
//   Widget build(BuildContext context) {
//     // This is a placeholder for your streak summary UI
//     return const SizedBox(); // Replace with your actual streak UI
//   }
// }

// // Replace this with your actual streak provider
// final currentStreakProvider = FutureProvider<int>((ref) async {
//   await Future.delayed(const Duration(milliseconds: 300)); // Simulated delay
//   return 2; // Simulated streak value
// });

class DashboardGreetingCard extends ConsumerWidget {
  const DashboardGreetingCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Greeting text (outside the card)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Hi, Joshua!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        const SizedBox(height: 12),

        // Card with streak score and animated mascot
        Stack(
          clipBehavior: Clip.none,
          children: [
            // Main streak card
            Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              decoration: BoxDecoration(
                color: AppColors.levelUnknown, // Light grey card
                borderRadius: BorderRadius.circular(16),
              ),
              child: ref.watch(currentStreakProvider).when(
                    data: (streakScore) => Row(
                      children: [
                        SvgPicture.asset(
                          'assets/icons/fire.svg',
                          width: 32,
                          height: 32,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Your streak score: $streakScore',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                    loading: () => const SizedBox(
                        height: 24, child: CircularProgressIndicator()),
                    error: (error, _) => Text(
                      'Your streak score: 0',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
            ),

            // Mascot animation positioned top-right
            Positioned(
              top: -20,
              right: 24,
              child: Image.asset(
                'assets/animations/chippyidle.gif',
                width: 70,
                height: 70,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class CompactResourceBar extends ConsumerWidget {
  const CompactResourceBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPremium = ref.watch(subscriptionStateProvider);
    final diamondsAsync = ref.watch(enhancedDiamondsProvider);
    final xp = ref.watch(xpProvider);
    final blankCardsData = ref.watch(blankCardsProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Diamonds
          GestureDetector(
            onTap: () =>
                _handleResourceTap(context, 'diamonds', isPremium, ref),
            child: diamondsAsync.when(
              data: (diamonds) => _assetIconWithValue(
                  'assets/icons/diamond.svg',
                  isPremium ? '∞' : diamonds.toString(),
                  context),
              loading: () => _assetIconWithValue(
                  'assets/icons/diamond.svg', '...', context),
              error: (_, __) =>
                  _assetIconWithValue('assets/icons/diamond.svg', '?', context),
            ),
          ),
          // Cards
          GestureDetector(
            onTap: () => _handleResourceTap(context, 'cards', isPremium, ref),
            child: blankCardsData.when(
              data: (cards) => _assetIconWithValue('assets/icons/card.svg',
                  cards < 0 ? '∞' : cards.toString(), context),
              loading: () => _assetIconWithValue(
                  'assets/icons/card.svg', isPremium ? '∞' : '5', context),
              error: (_, __) => _assetIconWithValue(
                  'assets/icons/card.svg', isPremium ? '∞' : '5', context),
            ),
          ),
          // Stars (XP)
          GestureDetector(
            onTap: () => _handleResourceTap(context, 'xp', isPremium, ref),
            child: xp.when(
              data: (xp) => _assetIconWithValue(
                  'assets/icons/star.svg', xp.toString(), context),
              loading: () =>
                  _assetIconWithValue('assets/icons/star.svg', '0', context),
              error: (_, __) =>
                  _assetIconWithValue('assets/icons/star.svg', '0', context),
            ),
          ),
        ],
      ),
    );
  }



  // Handle tap on resource bar items
  void _handleResourceTap(BuildContext context, String resourceType,
      bool isPremium, WidgetRef ref) {
    if (!isPremium) {
      // Show paywall for non-premium users
      ShowPaywall().presentPaywall();
    } else {
      // Show a dialog with information about the resource for premium users
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            resourceType == 'diamonds'
                ? 'Diamonds'
                : resourceType == 'xp'
                    ? 'Experience Points'
                    : 'Blank Cards',
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                resourceType == 'diamonds'
                    ? 'Diamonds are used for premium features'
                    : resourceType == 'xp'
                        ? 'XP is earned by completing exercises and quizzes'
                        : 'Blank cards are used to create new vocabulary cards',
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  Widget _assetIconWithValue(
      String assetPath, String value, BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.centerLeft,
      children: [
        // Pill with value (background)
        Container(
          margin: const EdgeInsets.only(
              left: 12), // shift pill right so icon overlaps
          padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 6)
              .copyWith(left: 40), // extra left padding for icon
          decoration: BoxDecoration(
            color: AppColors.primaryLight
                .withValues(alpha: 0.1), // pill background
            borderRadius: BorderRadius.circular(22),
            border: Border.all(
              color: AppColors.primaryLight.withValues(alpha: 0.8),
              width: 1,
            ),
          ),
          child: Text(
            value,
            style: TextStyle(
              color: AppColors.black,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        // Icon, visually on top of the pill
        Positioned(
          left: 0,
          child: SvgPicture.asset(
            assetPath,
            width: 40,
            height: 40,
          ),
        ),
      ],
    );
  }
}
