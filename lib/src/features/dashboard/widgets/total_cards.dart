import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/dashboard/widgets/buttons.dart';
import 'package:vocadex/src/features/dashboard/widgets/mastery.dart';
import 'dart:math';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Provider for total cards count and type breakdown - modified to be auto-refreshing
final totalCardsProvider =
    FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final firebaseService = FirebaseService();

  // Listen to changes in the vocabulary collection
  // This will ensure we refresh when cards are added
  ref.watch(vocabCollectionChangesProvider);

  try {
    // Get all vocabulary cards
    final cards = await firebaseService.fetchVocabulary();

    // Count cards by type
    final Map<String, int> typeCount = {};

    for (final card in cards) {
      for (final type in card.type) {
        final normalizedType = type.toLowerCase();
        typeCount[normalizedType] = (typeCount[normalizedType] ?? 0) + 1;
      }
    }

    // Get mastery status counts
    int wildCount = 0;
    int tamedCount = 0;
    int masteredCount = 0;

    for (final card in cards) {
      final status = card.getMasteryStatus();
      switch (status) {
        case MasteryStatus.wild:
          wildCount++;
          break;
        case MasteryStatus.tamed:
          tamedCount++;
          break;
        case MasteryStatus.mastered:
          masteredCount++;
          break;
      }
    }

    // Return the data
    return {
      'totalCards': cards.length,
      'typeCount': typeCount,
      'masteryCount': {
        'wild': wildCount,
        'tamed': tamedCount,
        'mastered': masteredCount,
      },
    };
  } catch (e) {
    // Return empty data on error
    return {
      'totalCards': 0,
      'typeCount': {},
      'masteryCount': {
        'wild': 0,
        'tamed': 0,
        'mastered': 0,
      },
    };
  }
});

/// Provider to track changes in vocabulary collection
final vocabCollectionChangesProvider = StreamProvider<void>((ref) {
  return FirebaseService().watchVocabularyChanges();
});

class TotalCardsWidget extends ConsumerStatefulWidget {
  const TotalCardsWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _TotalCardsWidgetState();
}

class _TotalCardsWidgetState extends ConsumerState<TotalCardsWidget> {
  @override
  Widget build(BuildContext context) {
    // Watch the data from the provider
    final cardsDataAsync = ref.watch(totalCardsProvider);

    return SizedBox(
      // height: 550, // Fixed height
      child:
          // Full-width card

          Center(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    AppColors.white,
                    AppColors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  const SizedBox(height: 24),
                  // Use the async data for the arc
                  cardsDataAsync.when(
                    data: (data) {
                      final totalCards = data['totalCards'] as int;
                      final wildCount = data['masteryCount']['wild'] as int;
                      final tamedCount = data['masteryCount']['tamed'] as int;
                      final masteredCount =
                          data['masteryCount']['mastered'] as int;

                      return AnimatedTotalCardsArc(
                        totalCards: totalCards,
                        wildCount: wildCount,
                        tamedCount: tamedCount,
                        masteredCount: masteredCount,
                      );
                    },
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => const Text('Error loading data'),
                  ),
                  const SizedBox(height: 16),
                  const MasteryDashboardCard(),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const VocabActionButtons(),
          ],
        ),
      ),
    );
  }
}

class AnimatedTotalCardsArc extends StatefulWidget {
  final int totalCards;
  final int wildCount;
  final int tamedCount;
  final int masteredCount;

  const AnimatedTotalCardsArc({
    super.key,
    required this.totalCards,
    required this.wildCount,
    required this.tamedCount,
    required this.masteredCount,
  });

  @override
  State<AnimatedTotalCardsArc> createState() => _AnimatedTotalCardsArcState();
}

class _AnimatedTotalCardsArcState extends State<AnimatedTotalCardsArc>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (_, __) {
        return CustomPaint(
          painter: SemiCircularSegmentPainter(
            wildCount: widget.wildCount,
            tamedCount: widget.tamedCount,
            masteredCount: widget.masteredCount,
            total: widget.totalCards,
            progress: _animation.value,
          ),
          size: const Size(250, 100),
        );
      },
    );
  }
}

class SemiCircularSegmentPainter extends CustomPainter {
  final int wildCount;
  final int tamedCount;
  final int masteredCount;
  final int total;
  final double progress;

  final double strokeWidth = 40.0;
  final double gapAngle = 3.0;

  SemiCircularSegmentPainter({
    required this.wildCount,
    required this.tamedCount,
    required this.masteredCount,
    required this.total,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height);
    final radius = size.width / 2 - strokeWidth;
    final rect = Rect.fromCircle(center: center, radius: radius);

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.butt;

    final totalValue = wildCount + tamedCount + masteredCount;
    if (totalValue == 0) {
      // If there are no cards, draw a gray arc
      paint.color = AppColors.grey;
      canvas.drawArc(rect, pi, pi, false, paint);

      // Draw the "0" in the center
      final countText = TextPainter(
        text: const TextSpan(
          text: '0',
          style: TextStyle(
            fontSize: 26,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryLight,
          ),
        ),
        textDirection: TextDirection.ltr,
      )..layout();

      final labelText = TextPainter(
        text: const TextSpan(
          text: 'Total Cards',
          style: TextStyle(fontSize: 14, color: AppColors.black),
        ),
        textDirection: TextDirection.ltr,
      )..layout();

      final totalTextY = center.dy - radius / 2;
      countText.paint(
          canvas, Offset(center.dx - countText.width / 2, totalTextY));
      labelText.paint(
          canvas,
          Offset(center.dx - labelText.width / 2,
              totalTextY + countText.height + 4));

      return;
    }

    double totalArc = 180 - (gapAngle * 2);
    double wildAngle = (wildCount / totalValue) * totalArc * progress;
    double tamedAngle = (tamedCount / totalValue) * totalArc * progress;
    double masteredAngle = (masteredCount / totalValue) * totalArc * progress;

    double startAngle = pi;

    void drawSegment(
      double angle,
      Color color,
    ) {
      if (angle <= 0) return;

      paint.color = color;
      final sweepRadian = radians(angle);
      canvas.drawArc(rect, startAngle, sweepRadian, false, paint);

      final labelAngle = startAngle + sweepRadian / 2;
      final labelRadius = radius - strokeWidth / 2 - 5;
      final dx = center.dx + labelRadius * cos(labelAngle);
      final dy = center.dy + labelRadius * sin(labelAngle);

      canvas.save();
      canvas.translate(dx, dy);
      canvas.rotate(labelAngle - pi / 2);
      canvas.restore();

      startAngle += sweepRadian + radians(gapAngle);
    }

    drawSegment(
      wildAngle,
      AppColors.wild,
    );
    drawSegment(
      tamedAngle,
      AppColors.tamed,
    );
    drawSegment(
      masteredAngle,
      AppColors.mastered,
    );

    final countText = TextPainter(
      text: TextSpan(
        text: '$total',
        style: const TextStyle(
          fontSize: 26,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryLight,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    final labelText = TextPainter(
      text: const TextSpan(
        text: 'Total Cards',
        style: TextStyle(fontSize: 14, color: AppColors.black),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    final totalTextY = center.dy - radius / 2;
    countText.paint(
        canvas, Offset(center.dx - countText.width / 2, totalTextY));
    labelText.paint(
        canvas,
        Offset(center.dx - labelText.width / 2,
            totalTextY + countText.height + 4));
  }

  double radians(double deg) => deg * pi / 180;

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
