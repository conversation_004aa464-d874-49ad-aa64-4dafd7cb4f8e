// lib/src/features/dashboard/widgets/dashboard_greeting.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_provider.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/features/user/models/user_model.dart';

/// Provider for fetching the current user's profile data
final userProfileProvider = FutureProvider<UserModel?>((ref) async {
  // Watch the authentication state
  final authState = ref.watch(authStateNotifierProvider);

  // Only fetch profile if user is authenticated
  return authState.maybeWhen(
    authenticated: (authUser) async {
      try {
        final firebaseService = FirebaseService();

        // Use cache by default to avoid excessive fetches
        final userData = await firebaseService.getUserData(forceRefresh: false);

        if (userData != null) {
          // Only log when debugging
          // debugPrint('Fetched user profile: ${userData.firstName} (${userData.auth.uid})');

          // If the firstName is empty but we have auth data, try to update it from auth
          if (userData.firstName.isEmpty && userData.auth.displayName != null) {
            // Update the firstName from auth displayName
            await firebaseService.updateUserField(
                'firstName', userData.auth.displayName!);
            // Fetch updated data with force refresh since we just updated it
            return await firebaseService.getUserData(forceRefresh: true);
          }
        } else {
          debugPrint(
              'User data is null, user might not be properly created in Firestore');
        }

        return userData;
      } catch (e) {
        debugPrint('Error fetching user profile: $e');
        return null;
      }
    },
    orElse: () => null,
  );
});

/// Provider that forces a refresh of the user profile
final refreshUserProfileProvider = StateProvider<int>((ref) => 0);

/// Function to refresh the user profile data
void refreshUserProfile(WidgetRef ref) {
  // Increment the refresh counter to invalidate the provider
  ref.read(refreshUserProfileProvider.notifier).state++;
  // Invalidate the userProfileProvider
  ref.invalidate(userProfileProvider);
}

/// A widget that displays a greeting with the user's name and streak information
class DashboardGreeting extends ConsumerWidget {
  const DashboardGreeting({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the current streak from the provider
    final currentStreakAsync = ref.watch(currentStreakProvider);

    // Watch authentication state for user login status
    final authState = ref.watch(authStateNotifierProvider);

    // Watch the refresh counter to trigger refreshes
    final _ = ref.watch(refreshUserProfileProvider);

    // Watch user profile data if authenticated
    final userProfileAsync = ref.watch(userProfileProvider);

    // Refresh the profile data when the widget builds
    // This ensures we always have the latest data
    Future.microtask(() {
      if (authState.maybeWhen(
        authenticated: (_) => true,
        orElse: () => false,
      )) {
        ref.invalidate(userProfileProvider);
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // Use min size instead of Expanded
      children: [
        // Greeting with name
        authState.maybeWhen(
          authenticated: (_) => userProfileAsync.when(
            data: (userModel) {
              final displayName = _getDisplayName(userModel);
              return Text(
                'Hi, $displayName!',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              );
            },
            loading: () => const _LoadingGreeting(),
            error: (_, __) => const _ErrorGreeting(),
          ),
          orElse: () => Text(
            'Hi, Guest!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),

        // Optional streak display
        currentStreakAsync.when(
          data: (streakCount) => streakCount > 0
              ? Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/fire.svg',
                        width: 24,
                        height: 24,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$streakCount day streak!',
                        style: const TextStyle(
                          fontSize: 18,
                          color: AppColors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : const SizedBox(),
          loading: () => const SizedBox(),
          error: (_, __) => const SizedBox(),
        ),
      ],
    );
  }

  /// Gets display name from user model, falling back to first name or "there"
  String _getDisplayName(UserModel? userModel) {
    if (userModel == null) return 'Guest';

    // Use first name if available
    if (userModel.firstName.isNotEmpty) {
      return userModel.firstName;
    }

    // Fallback to display name from auth if available
    if (userModel.auth.displayName?.isNotEmpty == true) {
      // Extract first name from display name
      final nameParts = userModel.auth.displayName!.split(' ');
      if (nameParts.isNotEmpty) {
        return nameParts.first;
      }
      return userModel.auth.displayName!;
    }

    // Last resort - use generic greeting
    return 'Guest';
  }
}

/// Placeholder while loading user data
class _LoadingGreeting extends StatelessWidget {
  const _LoadingGreeting();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          'Hi ',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        Text(
          'Loading...',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
      ],
    );
  }
}

/// Placeholder for error loading user data
class _ErrorGreeting extends StatelessWidget {
  const _ErrorGreeting();

  @override
  Widget build(BuildContext context) {
    return Text(
      'Hi there!',
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
    );
  }
}

/// A widget that displays a week calendar strip with streak indicators
class StreakCalendar extends ConsumerWidget {
  const StreakCalendar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the streak calendar data
    final streakDataAsync = ref.watch(streakCalendarProvider);

    return streakDataAsync.when(
      data: (data) {
        final streakDates =
            List<String>.from(data['streak_dates'] as List? ?? []);

        // Get the current week days
        final weekDays = _getCurrentWeekDays();

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 12),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.grey.withAlpha(26),spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: weekDays.map((day) {
              final isToday = day['isToday'] as bool;
              final dateStr = day['dateStr'] as String;
              final hasStreak = streakDates.contains(dateStr);

              return _buildDayIndicator(
                context: context,
                dayName: day['shortName'] as String,
                dayNumber: day['dayNumber'] as String,
                isToday: isToday,
                hasStreak: hasStreak,
              );
            }).toList(),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const SizedBox(), // Hide on error
    );
  }

  Widget _buildDayIndicator({
    required BuildContext context,
    required String dayName,
    required String dayNumber,
    required bool isToday,
    required bool hasStreak,
  }) {
    return Container(
      width: 40,
      decoration: isToday
          ? BoxDecoration(
              color: AppColors.black,
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            dayName,
            style: TextStyle(
              fontSize: 12,
              color: isToday ? AppColors.white : AppColors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            dayNumber,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isToday ? AppColors.white : AppColors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: hasStreak ? AppColors.green : AppColors.transparent,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getCurrentWeekDays() {
    final now = DateTime.now();
    final days = <Map<String, dynamic>>[];

    // Find the start of the week (Monday)
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    // Create a list of the 7 days
    for (int i = 0; i < 7; i++) {
      final day = startOfWeek.add(Duration(days: i));
      days.add({
        'dayNumber': day.day.toString(),
        'shortName': _getShortDayName(day.weekday),
        'dateStr': _formatDateToString(day),
        'isToday': day.day == now.day &&
            day.month == now.month &&
            day.year == now.year,
      });
    }

    return days;
  }

  String _getShortDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Mon';
      case 2:
        return 'Tue';
      case 3:
        return 'Wed';
      case 4:
        return 'Thu';
      case 5:
        return 'Fri';
      case 6:
        return 'Sat';
      case 7:
        return 'Sun';
      default:
        return '';
    }
  }

  String _formatDateToString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
