// // lib/src/features/dashboard/widgets/recent_activity.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:intl/intl.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart';

// /// Model class for activity items
// class ActivityItem {
//   final String id;
//   final String title;
//   final String subtitle;
//   final IconData icon;
//   final Color iconColor;
//   final DateTime timestamp;
//   final ActivityType type;

//   ActivityItem({
//     required this.id,
//     required this.title,
//     required this.subtitle,
//     required this.icon,
//     required this.iconColor,
//     required this.timestamp,
//     required this.type,
//   });
// }

// /// Types of activities
// enum ActivityType {
//   added,
//   mastered,
//   quiz,
//   streak,
//   achievement,
// }

// /// Provider for recent activity data
// final recentActivityProvider = Provider<List<ActivityItem>>((ref) {
//   // In a real app, this would be fetched from a service
//   // This is placeholder data
//   return [
//     ActivityItem(
//       id: 'act1',
//       title: 'Added "Ephemeral"',
//       subtitle: 'New C1 level word added to your collection',
//       icon: Icons.add_circle,
//       iconColor: AppColors.blue,
//       timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
//       type: ActivityType.added,
//     ),
//     ActivityItem(
//       id: 'act2',
//       title: 'Quiz Completed',
//       subtitle: 'You scored 8/10 on Adjectives quiz',
//       icon: Icons.quiz,
//       iconColor: AppColors.purple,
//       timestamp: DateTime.now().subtract(const Duration(hours: 2)),
//       type: ActivityType.quiz,
//     ),
//     ActivityItem(
//       id: 'act3',
//       title: 'Mastered "Benevolent"',
//       subtitle: 'You mastered this B2 level word',
//       icon: Icons.stars,
//       iconColor: AppColors.orange,
//       timestamp: DateTime.now().subtract(const Duration(hours: 5)),
//       type: ActivityType.mastered,
//     ),
//     ActivityItem(
//       id: 'act4',
//       title: '3-Day Streak!',
//       subtitle: 'You\'ve been learning for 3 days in a row',
//       icon: Icons.local_fire_department,
//       iconColor: AppColors.red,
//       timestamp: DateTime.now().subtract(const Duration(days: 1)),
//       type: ActivityType.streak,
//     ),
//   ];
// });

// /// Widget to display recent activities
// class RecentActivityWidget extends ConsumerWidget {
//   const RecentActivityWidget({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final activities = ref.watch(recentActivityProvider);

//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Header
//             Text(
//               'Recent Activity',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),

//             const SizedBox(height: 16),

//             // Activity list
//             if (activities.isEmpty)
//               const Center(
//                 child: Padding(
//                   padding: EdgeInsets.all(16.0),
//                   child: Text('No recent activities'),
//                 ),
//               )
//             else
//               ...activities
//                   .take(3)
//                   .map((activity) => _buildActivityItem(context, activity))
//                   .toList(),

//             // View more button
//             if (activities.length > 3)
//               Padding(
//                 padding: const EdgeInsets.only(top: 8.0),
//                 child: Center(
//                   child: TextButton.icon(
//                     onPressed: () {
//                       // Navigate to full activity history
//                     },
//                     icon: const Icon(Icons.history),
//                     label: const Text('View more'),
//                     style: TextButton.styleFrom(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 16,
//                         vertical: 8,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildActivityItem(BuildContext context, ActivityItem activity) {
//     return Container(
//       margin: const EdgeInsets.only(bottom: 16),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Activity icon
//           Container(
//             width: 40,
//             height: 40,
//             decoration: BoxDecoration(
//               color: activity.iconColor.withAlpha(26),//               shape: BoxShape.circle,
//             ),
//             child: Center(
//               child: Icon(
//                 activity.icon,
//                 color: activity.iconColor,
//                 size: 20,
//               ),
//             ),
//           ),

//           const SizedBox(width: 12),

//           // Activity content
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   activity.title,
//                   style: const TextStyle(
//                     fontWeight: FontWeight.bold,
//                     fontSize: 15,
//                   ),
//                 ),
//                 const SizedBox(height: 2),
//                 Text(
//                   activity.subtitle,
//                   style: TextStyle(
//                     fontSize: 13,
//                     color: AppColors.grey600,
//                   ),
//                 ),
//                 const SizedBox(height: 4),
//                 Text(
//                   _formatTimestamp(activity.timestamp),
//                   style: TextStyle(
//                     fontSize: 12,
//                     color: AppColors.grey500,
//                     fontStyle: FontStyle.italic,
//                   ),
//                 ),
//               ],
//             ),
//           ),

//           // Action icon
//           IconButton(
//             onPressed: () {
//               // Handle action based on activity type
//               _handleActivityAction(context, activity);
//             },
//             icon: const Icon(Icons.chevron_right),
//             iconSize: 18,
//             constraints: const BoxConstraints(),
//             padding: EdgeInsets.zero,
//             color: AppColors.grey400,
//           ),
//         ],
//       ),
//     );
//   }

//   // Format timestamp to relative time (e.g. "5h ago", "Just now")
//   String _formatTimestamp(DateTime timestamp) {
//     final now = DateTime.now();
//     final difference = now.difference(timestamp);

//     if (difference.inSeconds < 60) {
//       return 'Just now';
//     } else if (difference.inMinutes < 60) {
//       return '${difference.inMinutes}m ago';
//     } else if (difference.inHours < 24) {
//       return '${difference.inHours}h ago';
//     } else if (difference.inDays < 7) {
//       return '${difference.inDays}d ago';
//     } else {
//       return DateFormat('MMM d').format(timestamp);
//     }
//   }

//   // Handle action for different activity types
//   void _handleActivityAction(BuildContext context, ActivityItem activity) {
//     switch (activity.type) {
//       case ActivityType.added:
//         // Navigate to the word details
//         break;
//       case ActivityType.mastered:
//         // Navigate to the word details
//         break;
//       case ActivityType.quiz:
//         // Navigate to quiz history
//         break;
//       case ActivityType.streak:
//         // Navigate to streak details
//         break;
//       case ActivityType.achievement:
//         // Navigate to achievement details
//         break;
//     }
//   }
// }
