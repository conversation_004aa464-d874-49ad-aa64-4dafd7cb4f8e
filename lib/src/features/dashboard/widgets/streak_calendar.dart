// lib/src/features/dashboard/widgets/streak_calendar.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_provider.dart';

/// Widget to display a week calendar strip with streak indicators
class StreakCalendar extends ConsumerWidget {
  const StreakCalendar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the streak calendar data
    final streakDataAsync = ref.watch(streakCalendarProvider);

    return streakDataAsync.when(
      data: (data) {
        final streakDates =
            List<String>.from(data['streak_dates'] as List? ?? []);

        // Get the current week days
        final weekDays = _getCurrentWeekDays();

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 12),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.grey.withAlpha(26),spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: weekDays.map((day) {
              final isToday = day['isToday'] as bool;
              final dateStr = day['dateStr'] as String;
              final hasStreak = streakDates.contains(dateStr);

              return _buildDayIndicator(
                context: context,
                dayName: day['shortName'] as String,
                dayNumber: day['dayNumber'] as String,
                isToday: isToday,
                hasStreak: hasStreak,
              );
            }).toList(),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const SizedBox(), // Hide on error
    );
  }

  Widget _buildDayIndicator({
    required BuildContext context,
    required String dayName,
    required String dayNumber,
    required bool isToday,
    required bool hasStreak,
  }) {
    return Container(
      width: 40,
      decoration: isToday
          ? BoxDecoration(
              color: AppColors.black,
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            dayName,
            style: TextStyle(
              fontSize: 12,
              color: isToday ? AppColors.white : AppColors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            dayNumber,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isToday ? AppColors.white : AppColors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: hasStreak ? AppColors.primaryLight : AppColors.transparent,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getCurrentWeekDays() {
    final now = DateTime.now();
    final days = <Map<String, dynamic>>[];

    // Find the start of the week (Monday)
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    // Create a list of the 7 days
    for (int i = 0; i < 7; i++) {
      final day = startOfWeek.add(Duration(days: i));
      days.add({
        'dayNumber': day.day.toString(),
        'shortName': _getShortDayName(day.weekday),
        'dateStr': _formatDateToString(day),
        'isToday': day.day == now.day &&
            day.month == now.month &&
            day.year == now.year,
      });
    }

    return days;
  }

  String _getShortDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Mon';
      case 2:
        return 'Tue';
      case 3:
        return 'Wed';
      case 4:
        return 'Thu';
      case 5:
        return 'Fri';
      case 6:
        return 'Sat';
      case 7:
        return 'Sun';
      default:
        return '';
    }
  }

  String _formatDateToString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
