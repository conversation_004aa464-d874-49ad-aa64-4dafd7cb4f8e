// // lib/src/features/dashboard/widgets/quick_actions.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
// import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_button.dart';

// /// Widget to display quick action buttons for common tasks
// class QuickActionsWidget extends ConsumerWidget {
//   const QuickActionsWidget({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               'Quick Actions',
//               style: Theme.of(context).textTheme.titleLarge,
//             ),

//             const SizedBox(height: 16),

//             // Grid of action buttons
//             GridView.count(
//               crossAxisCount: 2,
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               mainAxisSpacing: 16,
//               crossAxisSpacing: 16,
//               childAspectRatio: 1.5,
//               children: [
//                 _buildActionCard(
//                   context: context,
//                   title: 'Add New Word',
//                   icon: Icons.add_circle,
//                   color: AppColors.blue,
//                   onTap: () {
//                     // Navigate to add word screen
//                   },
//                 ),
//                 _buildActionCard(
//                   context: context,
//                   title: 'Take a Quiz',
//                   icon: Icons.quiz,
//                   color: AppColors.purple,
//                   onTap: () {
//                     // Start a quiz
//                     StartQuizButton.startQuiz(context, ref, QuizMode.train);
//                   },
//                 ),
//                 _buildActionCard(
//                   context: context,
//                   title: 'Practice Cards',
//                   icon: Icons.flip,
//                   color: AppColors.orange,
//                   onTap: () {
//                     // Navigate to flashcards screen
//                   },
//                 ),
//                 _buildActionCard(
//                   context: context,
//                   title: 'Achievements',
//                   icon: Icons.emoji_events,
//                   color: Colors.amber,
//                   onTap: () {
//                     // Navigate to achievements screen
//                   },
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildActionCard({
//     required BuildContext context,
//     required String title,
//     required IconData icon,
//     required Color color,
//     required VoidCallback onTap,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       borderRadius: BorderRadius.circular(12),
//       child: Container(
//         padding: const EdgeInsets.all(16),
//         decoration: BoxDecoration(
//           color: color.withAlpha(26),//           borderRadius: BorderRadius.circular(12),
//           border: Border.all(
//             color: color.withAlpha(76),//             width: 1,
//           ),
//         ),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(
//               icon,
//               color: color,
//               size: 32,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               title,
//               style: TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: color.withAlpha(204),//               ),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
