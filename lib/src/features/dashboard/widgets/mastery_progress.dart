// // lib/src/features/dashboard/widgets/mastery_progress.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';

// /// Provider for overall vocabulary mastery statistics
// final masteryStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
//   // In a real app, fetch this data from your Firebase service
//   // This is placeholder data
//   return {
//     'totalWords': 253,
//     'mastered': 91,
//     'tamed': 102,
//     'wild': 60,
//     'masteryPercentage': 36, // (mastered / total) * 100
//     'dailyNorm': 300,
//     'levels': {
//       'A1': 24,
//       'A2': 37,
//       'B1': 63,
//       'B2': 80,
//       'C1': 40,
//       'C2': 9,
//     }
//   };
// });

// /// Widget to display overall vocabulary mastery statistics
// class MasteryProgressOverview extends ConsumerWidget {
//   const MasteryProgressOverview({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final masteryStatsAsync = ref.watch(masteryStatsProvider);

//     return masteryStatsAsync.when(
//       data: (stats) => _buildStats(context, stats),
//       loading: () => const Center(child: CircularProgressIndicator()),
//       error: (_, __) => const Center(child: Text('Failed to load stats')),
//     );
//   }

//   Widget _buildStats(BuildContext context, Map<String, dynamic> stats) {
//     final masteryPercentage = stats['masteryPercentage'] as int;
//     final totalWords = stats['totalWords'] as int;
//     final mastered = stats['mastered'] as int;
//     final tamed = stats['tamed'] as int;
//     final wild = stats['wild'] as int;

//     return Card(
//       elevation: 2,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           children: [
//             // Mastery ring and counts
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 // Vocabulary counts
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       'Daily Goal',
//                       style: Theme.of(context).textTheme.titleMedium,
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       '$totalWords',
//                       style:
//                           Theme.of(context).textTheme.headlineMedium?.copyWith(
//                                 fontWeight: FontWeight.bold,
//                               ),
//                     ),
//                     Text(
//                       'words total',
//                       style: TextStyle(
//                         fontSize: 14,
//                         color: AppColors.grey600,
//                       ),
//                     ),
//                   ],
//                 ),

//                 // Mastery progress ring
//                 SizedBox(
//                   width: 120,
//                   height: 120,
//                   child: Stack(
//                     alignment: Alignment.center,
//                     children: [
//                       // Progress ring
//                       SizedBox(
//                         width: 120,
//                         height: 120,
//                         child: CircularProgressIndicator(
//                           value: masteryPercentage / 100,
//                           strokeWidth: 10,
//                           backgroundColor: AppColors.grey200,
//                           valueColor: AlwaysStoppedAnimation<Color>(
//                             Theme.of(context).primaryColor,
//                           ),
//                         ),
//                       ),

//                       // Percentage text
//                       Column(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Text(
//                             '$masteryPercentage%',
//                             style: const TextStyle(
//                               fontSize: 24,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           const Text(
//                             'Mastered',
//                             style: TextStyle(
//                               fontSize: 12,
//                               color: AppColors.grey,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),

//                 // Daily goal norm
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.end,
//                   children: [
//                     Text(
//                       'Target',
//                       style: Theme.of(context).textTheme.titleMedium,
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       '${stats['dailyNorm']}',
//                       style:
//                           Theme.of(context).textTheme.headlineMedium?.copyWith(
//                                 fontWeight: FontWeight.bold,
//                               ),
//                     ),
//                     Text(
//                       'words goal',
//                       style: TextStyle(
//                         fontSize: 14,
//                         color: AppColors.grey600,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),

//             const SizedBox(height: 24),

//             // Mastery levels breakdown
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 _buildMasteryLevel(
//                   context: context,
//                   label: 'Mastered',
//                   count: mastered,
//                   total: totalWords,
//                   percentage: (mastered / totalWords * 100).round(),
//                   color: AppColors.green,
//                 ),
//                 _buildMasteryLevel(
//                   context: context,
//                   label: 'Tamed',
//                   count: tamed,
//                   total: totalWords,
//                   percentage: (tamed / totalWords * 100).round(),
//                   color: AppColors.orange,
//                 ),
//                 _buildMasteryLevel(
//                   context: context,
//                   label: 'Wild',
//                   count: wild,
//                   total: totalWords,
//                   percentage: (wild / totalWords * 100).round(),
//                   color: AppColors.red,
//                 ),
//               ],
//             ),

//             const SizedBox(height: 8),

//             // Progress bars for each mastery level
//             Row(
//               children: [
//                 Expanded(
//                   flex: mastered,
//                   child: Container(height: 4, color: AppColors.green),
//                 ),
//                 Expanded(
//                   flex: tamed,
//                   child: Container(height: 4, color: AppColors.orange),
//                 ),
//                 Expanded(
//                   flex: wild,
//                   child: Container(height: 4, color: AppColors.red),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildMasteryLevel({
//     required BuildContext context,
//     required String label,
//     required int count,
//     required int total,
//     required int percentage,
//     required Color color,
//   }) {
//     return Column(
//       children: [
//         Text(
//           label,
//           style: TextStyle(
//             fontSize: 14,
//             fontWeight: FontWeight.bold,
//             color: color,
//           ),
//         ),
//         const SizedBox(height: 8),
//         Container(
//           width: 60,
//           height: 60,
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             color: color.withAlpha(26),//             border: Border.all(color: color, width: 2),
//           ),
//           child: Center(
//             child: Text(
//               '$percentage%',
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: color,
//               ),
//             ),
//           ),
//         ),
//         const SizedBox(height: 4),
//         Text('$count/$total', style: const TextStyle(fontSize: 12)),
//       ],
//     );
//   }
// }
