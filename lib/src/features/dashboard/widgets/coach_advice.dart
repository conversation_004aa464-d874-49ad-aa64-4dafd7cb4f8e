// lib/src/features/dashboard/widgets/coach_advice.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Provides personalized advice messages based on user progress
final coachAdviceProvider = Provider<String>((ref) {
  // In a real app, this would be based on user data analysis
  return "Try reviewing words with a 'Tamed' mastery status to progress them to 'Mastered' level.";
});

/// A widget that displays advice from a virtual coach character
class CoachAdviceCard extends ConsumerWidget {
  const CoachAdviceCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final advice = ref.watch(coachAdviceProvider);
    final brightness = Theme.of(context).brightness;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Coach content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Coach advices',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    advice,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.getTextColor(brightness),
                    ),
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton(
                    onPressed: () {
                      // Navigate to detailed advice page
                    },
                    style: OutlinedButton.styleFrom(
                      minimumSize: const Size(0, 36),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                    ),
                    child: const Text('Read All'),
                  ),
                ],
              ),
            ),

            // Coach avatar
            Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(left: 12),
              decoration: BoxDecoration(
                color: AppColors.getInfoColor(brightness).withAlpha(51),borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // Main character body
                  Positioned.fill(
                    child: Center(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: AppColors.getInfoColor(brightness).withAlpha(102),borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),

                  // Eyes
                  Positioned(
                    top: 20,
                    left: 18,
                    child: Container(
                      width: 14,
                      height: 14,
                      decoration: BoxDecoration(
                        color: AppColors.getPrimaryColor(brightness),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: AppColors.getTextColor(brightness),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),

                  Positioned(
                    top: 20,
                    right: 18,
                    child: Container(
                      width: 14,
                      height: 14,
                      decoration: BoxDecoration(
                        color: AppColors.getPrimaryColor(brightness),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: AppColors.getTextColor(brightness),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Mouth
                  Positioned(
                    bottom: 20,
                    left: 25,
                    right: 25,
                    child: Container(
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppColors.getPrimaryColor(brightness),
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),

                  // Antenna
                  Positioned(
                    top: 0,
                    left: 30,
                    child: Container(
                      width: 2,
                      height: 10,
                      color: AppColors.getInfoColor(brightness).withAlpha(178),),
                  ),

                  Positioned(
                    top: -4,
                    left: 26,
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: AppColors.getInfoColor(brightness).withAlpha(76),shape: BoxShape.circle,
                      ),
                    ),
                  ),

                  // Hand waving
                  Positioned(
                    bottom: 10,
                    right: 2,
                    child: Transform.rotate(
                      angle: 0.5,
                      child: Container(
                        width: 14,
                        height: 3,
                        decoration: BoxDecoration(
                          color: AppColors.getInfoColor(brightness).withAlpha(102),borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
