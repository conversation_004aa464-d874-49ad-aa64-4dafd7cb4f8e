// lib/src/features/dashboard/widgets/daily_goals.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Provider for daily goals data
final dailyGoalsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  // In a real app, this would fetch from a service
  return [
    {
      'id': 'goal_1',
      'title': 'Learn 5 new words',
      'description': 'Add new vocabulary to your collection',
      'icon': Icons.add_circle_outline,
      'isCompleted': false,
      'progress': 3, // Current progress
      'target': 5, // Target value
    },
    {
      'id': 'goal_2',
      'title': 'Review mastery cards',
      'description': 'Practice words you\'re learning',
      'icon': Icons.replay,
      'isCompleted': true,
      'progress': 10,
      'target': 10,
    },
    {
      'id': 'goal_3',
      'title': 'Complete 1 quiz',
      'description': 'Test your vocabulary knowledge',
      'icon': Icons.quiz,
      'isCompleted': false,
      'progress': 0,
      'target': 1,
    },
  ];
});

/// Widget to display daily goals and progress
class DailyGoalsWidget extends ConsumerWidget {
  const DailyGoalsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goals = ref.watch(dailyGoalsProvider);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and "See All" button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Daily Goals',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to detailed goals screen
                  },
                  child: Text(
                    'SEE ALL',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Goals list
            ...goals.map((goal) => _buildGoalItem(context, goal)).toList(),

            // Summary at the bottom
            const SizedBox(height: 12),
            _buildGoalsSummary(goals),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalItem(BuildContext context, Map<String, dynamic> goal) {
    final bool isCompleted = goal['isCompleted'] as bool;
    final int progress = goal['progress'] as int;
    final int target = goal['target'] as int;
    final double progressPercentage = progress / target;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Status icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCompleted
                  ? AppColors.getSuccessColor(Theme.of(context).brightness).withAlpha(26): AppColors.getPrimaryColor(Theme.of(context).brightness).withAlpha(26),shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                isCompleted ? Icons.check : goal['icon'] as IconData,
                color:
                    isCompleted ? AppColors.getSuccessColor(Theme.of(context).brightness) : AppColors.getPrimaryColor(Theme.of(context).brightness),
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Goal information
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      goal['title'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '$progress/$target',
                      style: TextStyle(
                        color:
                            isCompleted ? AppColors.getSuccessColor(Theme.of(context).brightness) : AppColors.getTextColor(Theme.of(context).brightness).withAlpha(153),fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  goal['description'] as String,
                  style: TextStyle(
                    color: AppColors.getTextColor(Theme.of(context).brightness).withAlpha(153),fontSize: 12,
                  ),
                ),
                const SizedBox(height: 6),

                // Progress bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(2),
                  child: LinearProgressIndicator(
                    value: progressPercentage,
                    backgroundColor: AppColors.getTextColor(Theme.of(context).brightness).withAlpha(51),valueColor: AlwaysStoppedAnimation<Color>(
                      isCompleted
                          ? AppColors.getSuccessColor(Theme.of(context).brightness)
                          : AppColors.getPrimaryColor(Theme.of(context).brightness),
                    ),
                    minHeight: 4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsSummary(List<Map<String, dynamic>> goals) {
    final completedGoals =
        goals.where((goal) => goal['isCompleted'] as bool).length;
    final totalGoals = goals.length;
    final completionPercentage = (completedGoals / totalGoals * 100).round();

    return Builder(
      builder: (context) => Row(
        children: [
          // Overall progress bar
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: completedGoals / totalGoals,
                backgroundColor: AppColors.getTextColor(Theme.of(context).brightness).withAlpha(51),valueColor: AlwaysStoppedAnimation<Color>(AppColors.getSuccessColor(Theme.of(context).brightness)),
                minHeight: 8,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Completion percentage
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.getSuccessColor(Theme.of(context).brightness).withAlpha(26),borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$completionPercentage% Complete',
              style: TextStyle(
                color: AppColors.getSuccessColor(Theme.of(context).brightness),
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
