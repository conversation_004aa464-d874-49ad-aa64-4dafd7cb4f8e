import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/vocab_capture_entry.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_button.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_creation_sheet.dart';

class VocabActionButtons extends ConsumerWidget {
  const VocabActionButtons({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Add New Card Button
          Expanded(
            child: ActionButton(
              icon: Icons.add_circle_outline,
              label: 'Add New Card',
              color1: AppColors.gradientButton1,
              color2: AppColors.gradientButton2,
              iconColor: AppColors.mastered,
              onTap: () => showVocabEntryOptions(context),
            ),
          ),
          const SizedBox(width: 16),
          // Train Your Vocab Button
          Expanded(
            child: ActionButton(
              icon: Icons.fitness_center,
              label: 'Train Your Deck',
              color1: AppColors.gradientButton3,
              color2: AppColors.gradientButton4,
              iconColor: AppColors.mastered,
              onTap: () => showQuizCreationSheet(context, ref, QuizMode.train),
            ),
          ),
        ],
      ),
    );
  }
}

class ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color1;
  final Color color2;
  final Color iconColor;
  final VoidCallback onTap;

  const ActionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.color1,
    required this.color2,
    required this.iconColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 3,
      shadowColor: AppColors.black.withAlpha(76),borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color1.withAlpha(150),
                color2.withAlpha(150),
              ],
            ),
          ),
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.black, size: 24),
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
