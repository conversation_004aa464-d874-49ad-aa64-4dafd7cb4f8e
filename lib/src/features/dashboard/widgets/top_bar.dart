import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/user/user_providers.dart';
import 'package:vocadex/src/features/achievements/points/points_manager.dart';

class TopBarWidget extends ConsumerWidget {
  const TopBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isPremium = ref.watch(subscriptionStateProvider);
    final diamondsAsync = ref.watch(diamondsProvider);
    final xpAsync = FutureProvider<int>((ref) async {
      final pointsManager = PointsManager();
      return await pointsManager.getUserPoints();
    });
    final xp = ref.watch(xpAsync);

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            // Blank Cards
            _ResourceItem(
              icon: Icons.crop_square,
              label: 'Blank Cards',
              value: isPremium ? '30' : '5',
              color: AppColors.getInfoColor(Theme.of(context).brightness),
              subLabel: isPremium ? 'Premium' : 'Free',
            ),
            // Diamonds
            diamondsAsync.when(
              data: (diamonds) => _ResourceItem(
                icon: Icons.diamond,
                label: 'Diamonds',
                value: isPremium ? '∞' : diamonds.toString(),
                color: AppColors.getPrimaryColor(Theme.of(context).brightness),
                subLabel: isPremium ? 'Unlimited' : 'Daily',
              ),
              loading: () => _ResourceItem(
                icon: Icons.diamond,
                label: 'Diamonds',
                value: '...',
                color: AppColors.getPrimaryColor(Theme.of(context).brightness),
              ),
              error: (_, __) => _ResourceItem(
                icon: Icons.diamond,
                label: 'Diamonds',
                value: '?',
                color: AppColors.getPrimaryColor(Theme.of(context).brightness),
              ),
            ),
            // XP
            xp.when(
              data: (xp) => _ResourceItem(
                icon: Icons.star,
                label: 'XP',
                value: xp.toString(),
                color: AppColors.getWarningColor(Theme.of(context).brightness),
              ),
              loading: () => _ResourceItem(
                icon: Icons.star,
                label: 'XP',
                value: '...',
                color: AppColors.getWarningColor(Theme.of(context).brightness),
              ),
              error: (_, __) => _ResourceItem(
                icon: Icons.star,
                label: 'XP',
                value: '?',
                color: AppColors.getWarningColor(Theme.of(context).brightness),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ResourceItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;
  final String? subLabel;

  const _ResourceItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
    this.subLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircleAvatar(
          backgroundColor: color.withAlpha(38),
          radius: 24,
          child: Icon(icon, color: color, size: 28),
        ),
        const SizedBox(height: 6),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.getTextColor(Theme.of(context).brightness)
                .withAlpha(222),
          ),
        ),
        if (subLabel != null)
          Text(
            subLabel!,
            style: TextStyle(
              fontSize: 10,
              color: color.withAlpha(178),
            ),
          ),
      ],
    );
  }
}
