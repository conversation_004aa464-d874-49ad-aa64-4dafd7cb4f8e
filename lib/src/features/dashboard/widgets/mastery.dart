// lib/src/features/achievements/mastery/mastery_dashboard_card.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/mastery/mastery_level_manager.dart';
import 'package:vocadex/src/features/dashboard/widgets/total_cards.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/presentation/vocab_deck_screen.dart';

/// Provider for the MasteryLevelManager
final masteryManagerProvider = Provider<MasteryLevelManager>((ref) {
  return MasteryLevelManager();
});

/// Provider for mastery status counts
final masteryStatusCountsProvider =
    FutureProvider.autoDispose<Map<MasteryStatus, int>>((ref) async {
  final manager = ref.read(masteryManagerProvider);

  // Listen to changes in the vocabulary collection
  ref.watch(vocabCollectionChangesProvider);

  return await manager.getMasteryStatusCounts();
});

/// A widget that displays mastery statistics in a dashboard card format
class MasteryDashboardCard extends ConsumerWidget {
  const MasteryDashboardCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final masteryCountsAsync = ref.watch(masteryStatusCountsProvider);

    return Padding(
      padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          masteryCountsAsync.when(
            data: (counts) => _buildMasteryCards(context, counts, ref),
            loading: () => const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            ),
            error: (error, stackTrace) => Center(
              child: Text(
                'Error loading mastery data: $error',
                style: TextStyle(
                    color: AppColors.getFailureColor(
                        Theme.of(context).brightness)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMasteryCards(
      BuildContext context, Map<MasteryStatus, int> counts, WidgetRef ref) {
    final total = counts.values.fold(0, (prev, curr) => prev + curr);

    if (total == 0) {
      return const Padding(
        padding: EdgeInsets.all(24.0),
        child: Center(
          child: Text(
            'No vocabulary cards yet.\nAdd some to start tracking mastery!',
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Column(
      children: [
        Row(
          children: [
            _buildMasteryMetricCard(
              context: context,
              label: 'Wild',
              count: counts[MasteryStatus.wild] ?? 0,
              total: total,
              color: AppColors.wild,
              icon: Icons.pets,
            ),
            _buildMasteryMetricCard(
              context: context,
              label: 'Tamed',
              count: counts[MasteryStatus.tamed] ?? 0,
              total: total,
              color: AppColors.tamed,
              icon: Icons.lightbulb,
            ),
            _buildMasteryMetricCard(
              context: context,
              label: 'Mastered',
              count: counts[MasteryStatus.mastered] ?? 0,
              total: total,
              color: AppColors.mastered,
              icon: Icons.military_tech,
            ),
          ],
        ),

        const SizedBox(height: 8),

        // //  Progress bar showing mastery distribution
        // const Text(
        //   'Overall Progress',
        //   style: TextStyle(fontWeight: FontWeight.bold),
        // ),
        // const SizedBox(height: 8),
        // ClipRRect(
        //   borderRadius: BorderRadius.circular(8),
        //   child: SizedBox(
        //     height: 20,
        //     child: Row(
        //       children: [
        //         // Mastered section
        //         Expanded(
        //           flex: counts[MasteryStatus.mastered] ?? 0,
        //           child: Container(
        //             color: AppColors.green,
        //             height: double.infinity,
        //           ),
        //         ),

        //         // Tamed section
        //         Expanded(
        //           // flex: 3,
        //           flex: counts[MasteryStatus.tamed] ?? 0,
        //           child: Container(
        //             color: AppColors.orange,
        //             height: double.infinity,
        //           ),
        //         ),

        //         // Wild section
        //         Expanded(
        //           flex: counts[MasteryStatus.wild] ?? 0,
        //           child: Container(
        //             color: AppColors.red,
        //             height: double.infinity,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),

        const SizedBox(height: 4),

        // Next goal message
        _buildNextGoalMessage(counts),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildMasteryMetricCard({
    required BuildContext context,
    required String label,
    required int count,
    required int total,
    required Color color,
    required IconData icon,
  }) {
    // final percentage = total > 0 ? (count / total * 100).round() : 0;

    // Map the icon names to SVG asset paths
    String svgAsset;
    switch (label) {
      case 'Wild':
        svgAsset = 'assets/icons/paws.svg';
        break;
      case 'Tamed':
        svgAsset = 'assets/icons/bulb.svg';
        break;
      case 'Mastered':
        svgAsset = 'assets/icons/medal.svg';
        break;
      default:
        svgAsset = 'assets/icons/star.svg';
    }
    final darkColor = darkenColor(color, 0.3);
    return Expanded(
      child: Card(
        elevation: 0,
        color: color.withAlpha(26),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: color.withAlpha(153),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                svgAsset,
                height: 24,
                width: 24,
                colorFilter: ColorFilter.mode(darkColor, BlendMode.srcIn),
              ),
              const SizedBox(height: 8),
              Text(
                '$count',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: darkColor,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: darkColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNextGoalMessage(Map<MasteryStatus, int> counts) {
    final wildCount = counts[MasteryStatus.wild] ?? 0;
    final tamedCount = counts[MasteryStatus.tamed] ?? 0;
    final masteredCount = counts[MasteryStatus.mastered] ?? 0;

    // Determine what goal to suggest next
    String message;
    String svgAsset;
    Color color;

    if (wildCount > 0) {
      message = 'Try to tame $wildCount wild words!';
      svgAsset = 'assets/icons/paws.svg';
      color = AppColors.tamed;
    } else if (tamedCount > 0) {
      message = 'Master your $tamedCount tamed words!';
      svgAsset = 'assets/icons/trophy.svg';
      color = AppColors.tamed;
    } else if (masteredCount > 0) {
      message = 'All words mastered! Great job!';
      svgAsset = 'assets/icons/medal.svg';
      color = AppColors.mastered;
    } else {
      message = 'Add vocabulary to start your journey!';
      svgAsset = 'assets/icons/pen_fill.svg';
      color = AppColors.primaryLight;
    }
    final darkColor = darkenColor(color, 0.3);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withAlpha(153),
        ),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            svgAsset,
            height: 24,
            width: 24,
            colorFilter: ColorFilter.mode(darkColor, BlendMode.srcIn),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: darkColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A mini version of the mastery dashboard for limited spaces
class MasteryMiniCard extends ConsumerWidget {
  const MasteryMiniCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final masteryCountsAsync = ref.watch(masteryStatusCountsProvider);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: masteryCountsAsync.when(
          data: (counts) => _buildMiniStats(context, counts),
          loading: () => const SizedBox(
            height: 50,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stackTrace) => Text(
            'Error: $error',
            style: TextStyle(
                color: AppColors.getFailureColor(Theme.of(context).brightness),
                fontSize: 12),
          ),
        ),
      ),
    );
  }

  Widget _buildMiniStats(BuildContext context, Map<MasteryStatus, int> counts) {
    final total = counts.values.fold(0, (prev, curr) => prev + curr);

    if (total == 0) {
      return const Text(
        'No cards yet',
        textAlign: TextAlign.center,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Vocabulary Mastery',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            Text(
              '$total words',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.getTextColor(Theme.of(context).brightness)
                    .withAlpha(153),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Progress bar
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: SizedBox(
            height: 12,
            child: Row(
              children: [
                // Mastered section
                Expanded(
                  flex: counts[MasteryStatus.mastered] ?? 0,
                  child: Container(color: AppColors.mastered),
                ),

                // Tamed section
                Expanded(
                  flex: counts[MasteryStatus.tamed] ?? 0,
                  child: Container(color: AppColors.tamed),
                ),

                // Wild section
                Expanded(
                  flex: counts[MasteryStatus.wild] ?? 0,
                  child: Container(color: AppColors.wild),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Legend
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildMiniLegendItem(
              color: AppColors.mastered,
              label: 'Mastered',
              count: counts[MasteryStatus.mastered] ?? 0,
            ),
            _buildMiniLegendItem(
              color: AppColors.tamed,
              label: 'Tamed',
              count: counts[MasteryStatus.tamed] ?? 0,
            ),
            _buildMiniLegendItem(
              color: AppColors.wild,
              label: 'Wild',
              count: counts[MasteryStatus.wild] ?? 0,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMiniLegendItem({
    required Color color,
    required String label,
    required int count,
  }) {
    return Builder(
      builder: (context) => Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '$label: $count',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.getTextColor(Theme.of(context).brightness)
                  .withAlpha(178),
            ),
          ),
        ],
      ),
    );
  }
}
