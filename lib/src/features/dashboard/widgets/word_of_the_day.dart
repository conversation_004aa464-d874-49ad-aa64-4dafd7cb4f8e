import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/bottom_navbar/navigation_providers.dart';
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/features/dashboard/widgets/buttons.dart';

import 'package:vocadex/src/features/vocab_capture/services/ai_generation_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

/// Provider for the vocabulary generation service
final vocabGenerationServiceProvider = Provider<VocabGenerationService>((ref) {
  return VocabGenerationService();
});

/// Provider for word of the day service
final wordOfTheDayServiceProvider = Provider<WordOfTheDayService>((ref) {
  final aiService = ref.read(vocabGenerationServiceProvider);
  return WordOfTheDayService(aiService: aiService);
});

/// Provider for word of the day
final wordOfTheDayProvider = FutureProvider<VocabCard>((ref) async {
  final service = ref.read(wordOfTheDayServiceProvider);
  return await service.getWordOfTheDay();
});

/// Service class to manage Word of the Day operations
class WordOfTheDayService {
  final VocabGenerationService _vertexAI;
  final FirebaseService _firebaseService = FirebaseService();
  final FirebaseVertexAI _directVertexAI = FirebaseVertexAI.instance;

  WordOfTheDayService({required VocabGenerationService aiService})
      : _vertexAI = aiService;

  // Cache key for storing the word of the day
  static const String _cacheKey = 'word_of_the_day_cache';
  static const String _cacheDateKey = 'word_of_the_day_date';

  /// Get the word of the day, generating a new one if needed
  Future<VocabCard> getWordOfTheDay() async {
    // Check if we have a cached word for today
    final prefs = await SharedPreferences.getInstance();
    final cachedData = prefs.getString(_cacheKey);
    final cachedDate = prefs.getString(_cacheDateKey);

    // Get today's date as a string
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    // If we have cached data from today, use it
    if (cachedData != null && cachedDate == today) {
      try {
        // Parse the cached json into a map
        final Map<String, dynamic> wordData = jsonDecode(cachedData);

        // Create a VocabCard from the cached data
        return VocabCard(
          id: 'wotd-$today',
          word: wordData['word'] as String,
          definition: wordData['definition'] as String,
          examples: List<String>.from(wordData['examples'] as List),
          type: List<String>.from(wordData['type'] as List),
          pronunciation: wordData['pronunciation'] as String,
          level: wordData['level'] as String,
          frequency: wordData['frequency'] is int
              ? wordData['frequency'] as int
              : (wordData['frequency'] is String
                  ? int.tryParse(wordData['frequency']) ?? 3
                  : 3),
          color: 'Red',
          masteryLevel: 1,
        );
      } catch (e) {
        // If parsing fails, generate a new word
        return await _generateNewWordOfTheDay(today);
      }
    } else {
      // No cached data for today, generate a new word
      return await _generateNewWordOfTheDay(today);
    }
  }

  /// Generate a new word of the day
  Future<VocabCard> _generateNewWordOfTheDay(String dateStr) async {
    try {
      // Try to fetch from Firebase first if available
      try {
        final dailyWord = await _firebaseService.fetchDailyWord();
        if (dailyWord.isNotEmpty) {
          final card = VocabCard(
            id: 'wotd-$dateStr',
            word: dailyWord['word'] as String,
            definition: dailyWord['definition'] as String,
            examples: [dailyWord['example'] as String],
            type: ['noun'], // Default type, should be updated
            pronunciation: dailyWord['pronunciation'] as String,
            level: 'B2', // Default level
            color: 'Red',
            masteryLevel: 1,
          );

          // Cache this word
          await _cacheWordOfTheDay(card, dateStr);
          return card;
        }
      } catch (e) {
        // Fallback to AI-generated word if Firebase fetch fails
        debugPrint('Failed to fetch from Firebase: $e');
      }

      // Generate word choices using AI
      final wordChoices = await _generateWordChoices();

      // Select random word from choices or default to "serendipity" if generation fails
      if (wordChoices.isNotEmpty) {
        final wordMap = wordChoices.first;

        // Generate a full vocabulary card for the selected word
        final generatedCard = await _vertexAI.generateVocabCardFromDefinition(
          wordMap['word'] as String,
          wordMap,
        );

        if (generatedCard != null) {
          // Update the ID to indicate it's a word of the day
          final card = generatedCard.copyWith(id: 'wotd-$dateStr');

          // Cache this word
          await _cacheWordOfTheDay(card, dateStr);
          return card;
        }
      }

      // Fallback word if all else fails
      final fallbackCard = VocabCard(
        id: 'wotd-$dateStr',
        word: 'Serendipity',
        definition:
            'The occurrence of events by chance in a happy or beneficial way',
        examples: [
          'The serendipity of meeting an old friend at the airport led to a wonderful collaboration',
          'Their discovery of the medicine was a case of pure serendipity'
        ],
        type: ['noun'],
        pronunciation: 'ˌserənˈdipədē',
        level: 'C1',
        color: 'Red',
        masteryLevel: 1,
      );

      // Cache the fallback word
      await _cacheWordOfTheDay(fallbackCard, dateStr);
      return fallbackCard;
    } catch (e) {
      debugPrint('Error generating Word of the Day: $e');

      // Return fallback word with error indication
      return VocabCard(
        id: 'wotd-error',
        word: 'Serendipity',
        definition:
            'The occurrence of events by chance in a happy or beneficial way',
        examples: [
          'Unable to generate a new word today. Please try again later.'
        ],
        type: ['noun'],
        pronunciation: 'ˌserənˈdipədē',
        level: 'C1',
        color: 'Red',
        masteryLevel: 1,
      );
    }
  }

  /// Generate word choices using the AI service
  Future<List<Map<String, dynamic>>> _generateWordChoices() async {
    try {
      // Define a set of interesting words across different difficulty levels
      final wordLevels = ['B1', 'B2', 'C1'];
      final selectedLevel = wordLevels[DateTime.now().day % wordLevels.length];

      // Generate a custom prompt for the AI
      final model = _directVertexAI.generativeModel(model: 'gemini-2.0-flash');
      final prompt = [
        Content.text('''
Generate a JSON object with an interesting, somewhat uncommon English word for today's Word of the Day:
{
  "word": "<a ${selectedLevel} level English word that is interesting but not extremely rare>",
  "definition": "<simple, concise definition at B2 level max>",
  "type": "<part of speech>",
  "example": "<natural example sentence>"
}

Please choose words that are useful in everyday English, but not extremely common like "the" or "good".
Choose words that will help expand vocabulary. The word should be at CEFR level ${selectedLevel}.
Do not include very obscure or technical terms, offensive words, or slang.

Return only the JSON, no additional text.
'''),
      ];

      final response = model.generateContentStream(prompt);
      StringBuffer buffer = StringBuffer();
      await for (final chunk in response) {
        buffer.write(chunk.text);
      }

      // Parse response to extract JSON
      final responseText = buffer.toString();
      final jsonStart = responseText.indexOf('{');
      final jsonEnd = responseText.lastIndexOf('}') + 1;

      if (jsonStart >= 0 && jsonEnd > jsonStart) {
        final jsonStr = responseText.substring(jsonStart, jsonEnd);
        // Parse the JSON response
        final Map<String, dynamic> wordData =
            jsonDecode(jsonStr) as Map<String, dynamic>;

        return [wordData];
      }

      return [];
    } catch (e) {
      debugPrint('Error generating word choices: $e');
      return [];
    }
  }

  /// Cache the word of the day to SharedPreferences
  Future<void> _cacheWordOfTheDay(VocabCard card, String dateStr) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Create a map from the card
      final wordData = {
        'word': card.word,
        'definition': card.definition,
        'examples': card.examples,
        'type': card.type,
        'pronunciation': card.pronunciation,
        'level': card.level,
        'frequency': card.frequency ?? 3,
      };

      // Save to SharedPreferences
      await prefs.setString(_cacheKey, jsonEncode(wordData));
      await prefs.setString(_cacheDateKey, dateStr);
    } catch (e) {
      debugPrint('Error caching Word of the Day: $e');
    }
  }

  /// Add the current word of the day to user's collection
  Future<bool> addToCollection(VocabCard card) async {
    try {
      // First check if the word already exists in the collection
      final existingCards = await _firebaseService.fetchVocabulary();
      final wordExists = existingCards.any((existingCard) =>
          existingCard.word.toLowerCase() == card.word.toLowerCase());

      if (wordExists) {
        return false; // Word already exists
      }

      // Clone the card to ensure it has a unique ID
      final userCard = card.copyWith(
        id: '', // Empty ID will be assigned by Firebase
      );

      // Add to user's collection
      await _firebaseService.addVocabCard(userCard);
      return true;
    } catch (e) {
      debugPrint('Error adding word to collection: $e');
      return false;
    }
  }
}

/// Widget to display the word of the day
class WordOfTheDay extends ConsumerWidget {
  const WordOfTheDay({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final wordOfTheDayAsync = ref.watch(wordOfTheDayProvider);

    return wordOfTheDayAsync.when(
      data: (wordCard) => _buildWordCard(context, wordCard, ref),
      loading: () => _buildLoadingCard(),
      error: (error, stackTrace) => _buildErrorCard(ref, error),
    );
  }

  Widget _buildWordCard(
      BuildContext context, VocabCard wordCard, WidgetRef ref) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Word of the Day',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withAlpha(26),borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  DateFormat('MMM d, yyyy').format(DateTime.now()),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).primaryColor.withValues(alpha: 1),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Use the VocabularyCard widget for the UI
        VocabularyCard(
          card: wordCard,
          isFlippable: false,
          showActions: false,
        ),

        // Action buttons below the card
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Add New Card Button
              Expanded(
                child: ActionButton(
                    icon: Icons.add,
                    label: 'Add to Your Deck',
                    color1: AppColors.gradientButton1,
                    color2: AppColors.gradientButton2,
                    iconColor: AppColors.mastered,
                    onTap: () => _addToCollection(context, wordCard, ref)),
              ),
              const SizedBox(width: 16),
              // Train Your Vocab Button
              Expanded(
                child: ActionButton(
                    icon: Icons.fitness_center,
                    label: 'See Your Deck',
                    color1: AppColors.gradientButton3,
                    color2: AppColors.gradientButton4,
                    iconColor: AppColors.mastered,
                    onTap: () {
                      ref.read(navBarIndexProvider.notifier).state = 1;
                      context.goNamed(RouteNames.vocabDeck);
                    }),
              ),
            ],
          ),
        )
        // ActionButton(
        //   icon: Icons.add,
        //   label: 'Add to My Deck',
        //   color: Theme.of(context).primaryColor,
        //   iconColor: AppColors.white,
        //   onTap: () => _addToCollection(context, wordCard, ref),
        // ),
        // const SizedBox(height: 8),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        //   child: Expanded(
        //     child: ElevatedButton.icon(
        //       onPressed: () => _addToCollection(context, wordCard, ref),
        //       icon: const Icon(Icons.add),
        //       label: const Text('Add to Deck'),
        //       style: ElevatedButton.styleFrom(
        //         shape: RoundedRectangleBorder(
        //           borderRadius: BorderRadius.circular(12),
        //         ),
        //         backgroundColor: Theme.of(context).primaryColor,
        //         foregroundColor: AppColors.white,
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }

  Future<void> _addToCollection(
      BuildContext context, VocabCard wordCard, WidgetRef ref) async {
    // Show loading indicator
    showInfoToast(
      context,
      title: 'Processing',
      description: 'Adding to your collection...',
    );

    // Add to collection
    final service = ref.read(wordOfTheDayServiceProvider);
    final success = await service.addToCollection(wordCard);

    // Show success or error message
    if (context.mounted) {
      if (success) {
        showSuccessToast(
          context,
          title: 'Word Added',
          description: 'Word of the day added to your vocabulary!',
        );

        // If successfully added, refresh the dashboard data
        ref.read(dashboardRefreshProvider.notifier).state = DateTime.now();
      } else {
        // Check if the word exists in the collection
        final existingCards = await FirebaseService().fetchVocabulary();
        final wordExists = existingCards.any((existingCard) =>
            existingCard.word.toLowerCase() == wordCard.word.toLowerCase());

        if (wordExists) {
          showFailureToast(
            context,
            title: 'Already in Your Deck',
            description: 'This word is already in your vocabulary deck!',
          );
        } else {
          showFailureToast(
            context,
            title: 'Error',
            description: 'Failed to add word to vocabulary',
          );
        }
      }
    }
  }

  Widget _buildLoadingCard() {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: const SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading Word of the Day...'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorCard(WidgetRef ref, Object error) {
    return Card(
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, size: 48, color: AppColors.wild),
              const SizedBox(height: 16),
              const Text(
                'Failed to load Word of the Day',
                style: TextStyle(fontSize: 16),
              ),
              Text(
                error.toString().length > 100
                    ? '${error.toString().substring(0, 100)}...'
                    : error.toString(),
                style:
                    const TextStyle(fontSize: 12, color: AppColors.textLight),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextButton.icon(
                onPressed: () {
                  // Refresh the provider to try again
                  ref.refresh(wordOfTheDayProvider);
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
