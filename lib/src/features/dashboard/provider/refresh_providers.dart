import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A provider that holds a timestamp of the last data refresh
/// This can be updated to trigger a refresh of dashboard data
final dashboardRefreshProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

/// A provider for the total cards that depends on the refresh provider
/// to ensure it updates when requested
final totalCardsRefreshableProvider = Provider<DateTime>((ref) {
  return ref.watch(dashboardRefreshProvider);
});
