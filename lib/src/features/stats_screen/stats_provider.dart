// // lib/src/features/stats/provider/stats_provider.dart

// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
// import 'package:vocadex/src/services/firebase_service.dart';
// import 'package:vocadex/src/features/achievements/points/points_manager.dart';
// import 'package:vocadex/src/features/achievements/streaks/streak_provider.dart';

// /// Provider for the PointsManager to ensure we use a single instance
// final pointsManagerProvider = Provider<PointsManager>((ref) {
//   return PointsManager();
// });

// /// Provider for fetching user statistics
// final userStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
//   final firebaseService = FirebaseService();

//   try {
//     // Get all vocabulary cards
//     final cards = await firebaseService.fetchVocabulary();

//     // Calculate basic stats
//     final totalCards = cards.length;

//     // Count by mastery status
//     int wildCount = 0;
//     int tamedCount = 0;
//     int masteredCount = 0;

//     // Count by word type and calculate average mastery by type
//     Map<String, int> typeCount = {};
//     Map<String, double> typeAvgMastery = {};
//     Map<String, int> typeTotalMastery = {};

//     // Count by CEFR level
//     Map<String, int> levelCount = {};

//     // Process cards
//     for (final card in cards) {
//       // Count by mastery status
//       final status = card.getMasteryStatus();
//       switch (status) {
//         case MasteryStatus.wild:
//           wildCount++;
//           break;
//         case MasteryStatus.tamed:
//           tamedCount++;
//           break;
//         case MasteryStatus.mastered:
//           masteredCount++;
//           break;
//       }

//       // Count by word type
//       for (final type in card.type) {
//         final normalizedType = type.toLowerCase();
//         typeCount[normalizedType] = (typeCount[normalizedType] ?? 0) + 1;

//         // Sum mastery levels by type for average calculation
//         typeTotalMastery[normalizedType] =
//             (typeTotalMastery[normalizedType] ?? 0) + card.masteryLevel;
//       }

//       // Count by CEFR level
//       final level = card.level;
//       levelCount[level] = (levelCount[level] ?? 0) + 1;
//     }

//     // Calculate average mastery by type
//     typeAvgMastery = Map.fromEntries(typeTotalMastery.entries.map((entry) =>
//         MapEntry(entry.key, entry.value / (typeCount[entry.key] ?? 1))));

//     // Get streak data from the streak provider
//     final streakService = ref.read(streakProviderProvider);
//     final currentStreak = await streakService.getCurrentStreak();
//     final longestStreak = await streakService.getLongestStreak();
//     final totalDays = await streakService.getTotalDays();

//     // Get points data from the points manager
//     final pointsManager = ref.read(pointsManagerProvider);
//     final totalPoints = await pointsManager.getTotalPoints();
//     final quizPoints = await pointsManager.getQuizPoints();
//     final masteryPoints = await pointsManager.getMasteryPoints();

//     // Combine all stats
//     return {
//       'totalCards': totalCards,
//       'masteryCount': {
//         'wild': wildCount,
//         'tamed': tamedCount,
//         'mastered': masteredCount,
//       },
//       'typeCount': typeCount,
//       'typeAvgMastery': typeAvgMastery,
//       'levelCount': levelCount,
//       'streakData': {
//         'currentStreak': currentStreak,
//         'longestStreak': longestStreak,
//         'totalDays': totalDays,
//       },
//       'pointsData': {
//         'totalPoints': totalPoints,
//         'quizPoints': quizPoints,
//         'masteryPoints': masteryPoints,
//       },
//       'quizPerformance': {
//         'totalQuizzes': 0, // Placeholder - would come from actual quiz history
//         'avgScore': 0.0,
//         'bestScore': 0,
//         'totalCorrect': 0,
//         'totalQuestions': 0,
//       },
//     };
//   } catch (e) {
//     return {};
//   }
// });

// /// Provider for daily activity data
// final dailyActivityProvider = FutureProvider<Map<String, int>>((ref) async {
//   final firebaseService = FirebaseService();

//   try {
//     // Get all vocabulary cards
//     final cards = await firebaseService.fetchVocabulary();

//     // Initialize daily activity map for last 30 days
//     Map<String, int> dailyActivity = {};
//     final now = DateTime.now();
//     for (int i = 0; i < 30; i++) {
//       final date = now.subtract(Duration(days: i));
//       final dateStr =
//           '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
//       dailyActivity[dateStr] = 0;
//     }

//     // Count cards reviewed/created on each day
//     for (final card in cards) {
//       if (card.lastReviewedAt != null) {
//         final dateStr =
//             '${card.lastReviewedAt!.year}-${card.lastReviewedAt!.month.toString().padLeft(2, '0')}-${card.lastReviewedAt!.day.toString().padLeft(2, '0')}';
//         if (dailyActivity.containsKey(dateStr)) {
//           dailyActivity[dateStr] = (dailyActivity[dateStr] ?? 0) + 1;
//         }
//       }
//     }

//     return dailyActivity;
//   } catch (e) {
//     return {};
//   }
// });

// /// Provider for word type mastery for radar chart
// final wordTypeMasteryProvider =
//     FutureProvider<Map<String, dynamic>>((ref) async {
//   final statsAsync = await ref.watch(userStatsProvider.future);

//   final typeAvgMastery =
//       statsAsync['typeAvgMastery'] as Map<String, double>? ?? {};
//   final typeCount = statsAsync['typeCount'] as Map<String, int>? ?? {};

//   // Filter to only include types with at least one word
//   final validTypes =
//       typeAvgMastery.keys.where((type) => (typeCount[type] ?? 0) > 0).toList();

//   // Sort alphabetically for consistency
//   validTypes.sort();

//   return {
//     'typeLabels': validTypes,
//     'masteryValues':
//         validTypes.map((type) => typeAvgMastery[type] ?? 0).toList(),
//   };
// });
