import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/features/vocab_deck/utils/level_utils.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_tracker.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_provider.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_analysis_screen.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'dart:math' show cos, sin, pi, min;

// Provider for statistics data
final vocabStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final firebaseService = FirebaseService();

  try {
    // Get all vocabulary cards
    final cards = await firebaseService.fetchVocabulary();

    // Count total cards
    final totalCards = cards.length;

    // Calculate mastery status counts
    int wildCount = 0;
    int tamedCount = 0;
    int masteredCount = 0;

    // Word type counts
    Map<String, int> typeCount = {};

    // CEFR level counts
    Map<String, int> cefrCount = {
      'A1': 0,
      'A2': 0,
      'B1': 0,
      'B2': 0,
      'C1': 0,
      'C2': 0
    };

    // Word type mastery data for radar chart
    Map<String, Map<String, dynamic>> wordTypeMastery = {
      'Noun': {'count': 0, 'masteredCount': 0},
      'Verb': {'count': 0, 'masteredCount': 0},
      'Adjective': {'count': 0, 'masteredCount': 0},
      'Adverb': {'count': 0, 'masteredCount': 0},
      'Pronoun': {'count': 0, 'masteredCount': 0},
      'Preposition': {'count': 0, 'masteredCount': 0},
      'Conjunction': {'count': 0, 'masteredCount': 0},
      'Interjection': {'count': 0, 'masteredCount': 0},
    };

    for (final card in cards) {
      // Count by mastery status
      final status = card.getMasteryStatus();
      switch (status) {
        case MasteryStatus.wild:
          wildCount++;
          break;
        case MasteryStatus.tamed:
          tamedCount++;
          break;
        case MasteryStatus.mastered:
          masteredCount++;
          break;
      }

      // Count by CEFR level
      if (cefrCount.containsKey(card.level)) {
        cefrCount[card.level] = (cefrCount[card.level] ?? 0) + 1;
      }

      // Process word types
      for (final type in card.type) {
        // Normalize the type text to match keys in wordTypeMastery
        final normalizedType = _normalizeWordType(type);
        typeCount[type] = (typeCount[type] ?? 0) + 1;

        // Update type mastery data if it's a recognized type
        if (wordTypeMastery.containsKey(normalizedType)) {
          wordTypeMastery[normalizedType]!['count'] =
              (wordTypeMastery[normalizedType]!['count'] ?? 0) + 1;

          if (card.getMasteryStatus() == MasteryStatus.mastered) {
            wordTypeMastery[normalizedType]!['masteredCount'] =
                (wordTypeMastery[normalizedType]!['masteredCount'] ?? 0) + 1;
          }
        }
      }
    }

    // Calculate mastery percentages for radar chart
    final List<Map<String, dynamic>> radarData = [];
    wordTypeMastery.forEach((type, data) {
      final count = data['count'] ?? 0;
      final masteredCount = data['masteredCount'] ?? 0;

      // Calculate mastery percentage (avoid division by zero)
      double masteryPercentage = 0;
      if (count > 0) {
        masteryPercentage = (masteredCount / count) * 100;
      }

      radarData.add({
        'subject': type,
        'mastery': masteryPercentage.round(),
        'total': count
      });
    });

    // Get quiz statistics using the same service as the quiz stats screen
    final quizService = ref.watch(quizServiceProvider);
    final quizStats = await quizService.getQuizStatistics();

    // Get user streak data (you might need to implement this)
    final streakData = await _getUserStreakData();

    return {
      'totalCards': totalCards,
      'masteryBreakdown': {
        'wild': wildCount,
        'tamed': tamedCount,
        'mastered': masteredCount,
      },
      'cefrLevels': cefrCount,
      'wordTypeRadarData': radarData,
      'typeCount': typeCount,
      'quizStats': quizStats,
      'streak': streakData,
    };
  } catch (e) {
    debugPrint('Error loading vocabulary statistics: $e');
    // Return empty data on error
    return {
      'totalCards': 0,
      'masteryBreakdown': {
        'wild': 0,
        'tamed': 0,
        'mastered': 0,
      },
      'cefrLevels': {'A1': 0, 'A2': 0, 'B1': 0, 'B2': 0, 'C1': 0, 'C2': 0},
      'wordTypeRadarData': [],
      'typeCount': {},
      'quizStats': {},
      'streak': {'current': 0, 'longest': 0},
    };
  }
});

// Helper function for user streak data - connected to actual streak provider
Future<Map<String, dynamic>> _getUserStreakData() async {
  try {
    // Import the streak provider in your actual implementation
    // This connects to your existing streak tracker
    final streakProvider = StreakTracker();

    // Get current streak
    final currentStreak = await streakProvider.getCurrentStreak();

    // Get longest streak
    final longestStreak = await streakProvider.getLongestStreak();

    return {'current': currentStreak, 'longest': longestStreak};
  } catch (e) {
    debugPrint('Error getting streak data: $e');
    return {'current': 0, 'longest': 0};
  }
}

// Helper to normalize word type strings
String _normalizeWordType(String type) {
  // Capitalize first letter
  final normalized = type.trim().toLowerCase();
  if (normalized.isEmpty) return 'Other';

  return normalized[0].toUpperCase() + normalized.substring(1);
}

// class StatsAndAchievementsScreen extends ConsumerStatefulWidget {
//   const StatsAndAchievementsScreen({super.key});

//   @override
//   ConsumerState<StatsAndAchievementsScreen> createState() =>
//       _StatsAndAchievementsScreenState();
// }

// class _StatsAndAchievementsScreenState
//     extends ConsumerState<StatsAndAchievementsScreen>
//     with SingleTickerProviderStateMixin {
//   late TabController _tabController;

//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 3, vsync: this);
//   }

//   @override
//   void dispose() {
//     _tabController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GradientScaffold(
//       appBar: AppBar(
//         title: const Text('Statistics'),
//         backgroundColor: AppColors.transparent,
//         elevation: 0,
//       ),
//       body: _buildSummaryTab(),
//     );
//   }

//   Widget _buildSummaryTab() {
//     final statsAsync = ref.watch(vocabStatsProvider);

//     return statsAsync.when(
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stack) => Center(
//               child: Text('Error loading statistics: $error'),
//             ),
//         data: (stats) {
//           return SingleChildScrollView(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   _buildStatsCard(
//                     title: 'Vocabulary Overview',
//                     child: Column(
//                       children: [
//                         _buildMainStat(
//                           title: 'Total Words',
//                           value: stats['totalCards'].toString(),
//                           icon: Icons.menu_book,
//                         ),
//                         const SizedBox(height: 16),
//                         _buildMasteryDistribution(
//                           wild: stats['masteryBreakdown']['wild'],
//                           tamed: stats['masteryBreakdown']['tamed'],
//                           mastered: stats['masteryBreakdown']['mastered'],
//                         ),
//                       ],
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'Learning Streak',
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceAround,
//                       children: [
//                         _buildStatColumn(
//                           'Current Streak',
//                           '${stats['streak']['current']} days',
//                           Icons.local_fire_department,
//                           AppColors.warningLight,
//                         ),
//                         _buildStatColumn(
//                           'Longest Streak',
//                           '${stats['streak']['longest']} days',
//                           Icons.emoji_events,
//                           Colors.amber,
//                         ),
//                       ],
//                     ),
//                   ),

//                   // Add Quiz Stats Overview section
//                   if (stats['quizStats'] != null &&
//                       stats['quizStats']['totalQuizzes'] != null)
//                     const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'Quiz Performance',
//                     child: Column(
//                       children: [
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceAround,
//                           children: [
//                             _buildStatColumn(
//                               'Total Quizzes',
//                               '${stats['quizStats']['totalQuizzes'] ?? 0}',
//                               Icons.quiz,
//                               AppColors.infoLight,
//                             ),
//                             _buildStatColumn(
//                               'Best Score',
//                               '${stats['quizStats']['bestScore'] ?? 0}%',
//                               Icons.military_tech,
//                               AppColors.primaryLight,
//                             ),
//                           ],
//                         ),
//                         const SizedBox(height: 16),
//                         OutlinedButton.icon(
//                           onPressed: () {
//                             Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                 builder: (context) =>
//                                     const QuizAnalysisScreen(),
//                               ),
//                             );
//                           },
//                           icon: const Icon(Icons.analytics),
//                           label: const Text('View Detailed Quiz Analysis'),
//                         ),
//                       ],
//                     ),
//                   ),

//                   const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'CEFR Level Distribution',
//                     child: SizedBox(
//                       height: 200,
//                       child:
//                           CefrLevelDistributionChart(data: stats['cefrLevels']),
//                     ),
//                   ),
//                   _buildStatsCard(
//                     title: 'Word Type Mastery',
//                     child: SizedBox(
//                       height: 350,
//                       child: WordTypeMasteryRadarChart(
//                           data: stats['wordTypeRadarData']),
//                     ),
//                   ),
//                   _buildStatsCard(
//                     title: 'Mastery Details',
//                     child: Column(
//                       children: [
//                         _buildMasteryProgressBar(
//                             'Wild',
//                             stats['masteryBreakdown']['wild'],
//                             stats['totalCards'],
//                             AppColors.wild),
//                         _buildMasteryProgressBar(
//                             'Tamed',
//                             stats['masteryBreakdown']['tamed'],
//                             stats['totalCards'],
//                             AppColors.tamed),
//                         _buildMasteryProgressBar(
//                             'Mastered',
//                             stats['masteryBreakdown']['mastered'],
//                             stats['totalCards'],
//                             AppColors.mastered),
//                       ],
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'CEFR Level Distribution',
//                     child: Column(
//                       children: [
//                         for (var entry in stats['cefrLevels'].entries)
//                           _buildMasteryProgressBar(
//                             entry.key,
//                             entry.value,
//                             stats['totalCards'],
//                             LevelUtils.getLevelColor(entry.key),
//                             showPercentage: true,
//                           ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         });
//   }

//   Widget _buildMasteryTab() {
//     final statsAsync = ref.watch(vocabStatsProvider);

//     return statsAsync.when(
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stack) => Center(
//               child: Text('Error loading statistics: $error'),
//             ),
//         data: (stats) {
//           return SingleChildScrollView(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   _buildStatsCard(
//                     title: 'Word Type Mastery',
//                     child: SizedBox(
//                       height: 350,
//                       child: WordTypeMasteryRadarChart(
//                           data: stats['wordTypeRadarData']),
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'Mastery Details',
//                     child: Column(
//                       children: [
//                         _buildMasteryProgressBar(
//                             'Wild',
//                             stats['masteryBreakdown']['wild'],
//                             stats['totalCards'],
//                             AppColors.wild),
//                         _buildMasteryProgressBar(
//                             'Tamed',
//                             stats['masteryBreakdown']['tamed'],
//                             stats['totalCards'],
//                             AppColors.tamed),
//                         _buildMasteryProgressBar(
//                             'Mastered',
//                             stats['masteryBreakdown']['mastered'],
//                             stats['totalCards'],
//                             AppColors.green),
//                       ],
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   _buildStatsCard(
//                     title: 'CEFR Level Distribution',
//                     child: Column(
//                       children: [
//                         for (var entry in stats['cefrLevels'].entries)
//                           _buildMasteryProgressBar(
//                             entry.key,
//                             entry.value,
//                             stats['totalCards'],
//                             LevelUtils.getLevelColor(entry.key),
//                             showPercentage: true,
//                           ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         });
//   }

//   Widget _buildAchievementsTab() {
//     // This will be connected to your achievements system
//     return SingleChildScrollView(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             for (var achievement in _getAchievements())
//               _buildAchievementCard(achievement),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildStatsCard({required String title, required Widget child}) {
//     return Card(
//       elevation: 4,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(16),
//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               title,
//               style: const TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             const SizedBox(height: 16),
//             child,
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildMainStat({
//     required String title,
//     required String value,
//     required IconData icon,
//   }) {
//     return Row(
//       children: [
//         Container(
//           padding: const EdgeInsets.all(12),
//           decoration: BoxDecoration(
//             color: Theme.of(context).primaryColor.withAlpha(26),//             shape: BoxShape.circle,
//           ),
//           child: Icon(
//             icon,
//             size: 32,
//             color: Theme.of(context).primaryColor,
//           ),
//         ),
//         const SizedBox(width: 16),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(
//               title,
//               style: const TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),
//             Text(
//               value,
//               style: const TextStyle(
//                 fontSize: 28,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget _buildMasteryDistribution({
//     required int wild,
//     required int tamed,
//     required int mastered,
//   }) {
//     final total = wild + tamed + mastered;
//     final wildPercentage = total > 0 ? (wild / total * 100).toInt() : 0;
//     final tamedPercentage = total > 0 ? (tamed / total * 100).toInt() : 0;
//     final masteredPercentage = total > 0 ? (mastered / total * 100).toInt() : 0;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Text(
//           'Mastery Breakdown',
//           style: TextStyle(
//             fontSize: 16,
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//         const SizedBox(height: 8),
//         ClipRRect(
//           borderRadius: BorderRadius.circular(4),
//           child: SizedBox(
//             height: 20,
//             child: Row(
//               children: [
//                 Expanded(
//                   flex: wild,
//                   child: Container(color: AppColors.wild),
//                 ),
//                 Expanded(
//                   flex: tamed,
//                   child: Container(color: AppColors.tamed),
//                 ),
//                 Expanded(
//                   flex: mastered,
//                   child: Container(color: AppColors.mastered),
//                 ),
//               ],
//             ),
//           ),
//         ),
//         const SizedBox(height: 8),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             _buildLegendItem('Wild', wildPercentage, AppColors.wild),
//             _buildLegendItem('Tamed', tamedPercentage, AppColors.tamed),
//             _buildLegendItem(
//                 'Mastered', masteredPercentage, AppColors.mastered),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget _buildLegendItem(String label, int percentage, Color color) {
//     return Row(
//       children: [
//         Container(
//           width: 12,
//           height: 12,
//           decoration: BoxDecoration(
//             color: color,
//             shape: BoxShape.circle,
//           ),
//         ),
//         const SizedBox(width: 4),
//         Text(
//           '$label: $percentage%',
//           style: const TextStyle(fontSize: 12),
//         ),
//       ],
//     );
//   }

//   Widget _buildStatColumn(
//     String label,
//     String value,
//     IconData icon,
//     Color color,
//   ) {
//     return Column(
//       children: [
//         Container(
//           padding: const EdgeInsets.all(12),
//           decoration: BoxDecoration(
//             color: color.withAlpha(26),//             shape: BoxShape.circle,
//           ),
//           child: Icon(
//             icon,
//             size: 28,
//             color: color,
//           ),
//         ),
//         const SizedBox(height: 8),
//         Text(
//           value,
//           style: const TextStyle(
//             fontSize: 24,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         Text(
//           label,
//           style: TextStyle(
//             fontSize: 14,
//             color: AppColors.grey,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildMasteryProgressBar(
//     String label,
//     int value,
//     int total,
//     Color color, {
//     bool showPercentage = false,
//   }) {
//     final percentage = total > 0 ? (value / total * 100).toInt() : 0;
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 12.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(label),
//               Text(
//                 showPercentage ? '$value ($percentage%)' : '$value/$total',
//               ),
//             ],
//           ),
//           const SizedBox(height: 4),
//           LinearProgressIndicator(
//             value: total > 0 ? value / total : 0,
//             backgroundColor: AppColors.grey,
//             valueColor: AlwaysStoppedAnimation<Color>(color),
//             minHeight: 8,
//             borderRadius: BorderRadius.circular(4),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildAchievementCard(Achievement achievement) {
//     return Card(
//       margin: const EdgeInsets.only(bottom: 12),
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12),
//       ),
//       child: ListTile(
//         leading: Container(
//           padding: const EdgeInsets.all(8),
//           decoration: BoxDecoration(
//             color: achievement.isUnlocked
//                 ? achievement.color.withAlpha(51)//                 : AppColors.grey,
//             shape: BoxShape.circle,
//           ),
//           child: Icon(
//             achievement.icon,
//             color: achievement.isUnlocked ? achievement.color : AppColors.grey,
//           ),
//         ),
//         title: Text(
//           achievement.title,
//           style: TextStyle(
//             fontWeight: FontWeight.bold,
//             color: achievement.isUnlocked ? AppColors.black : AppColors.grey,
//           ),
//         ),
//         subtitle: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(achievement.description),
//             const SizedBox(height: 4),
//             ClipRRect(
//               borderRadius: BorderRadius.circular(2),
//               child: LinearProgressIndicator(
//                 value: achievement.progress / achievement.target,
//                 backgroundColor: AppColors.grey,
//                 valueColor: AlwaysStoppedAnimation<Color>(
//                   achievement.isUnlocked
//                       ? achievement.color
//                       : AppColors.grey,
//                 ),
//                 minHeight: 6,
//               ),
//             ),
//             const SizedBox(height: 2),
//             Text(
//               '${achievement.progress}/${achievement.target}',
//               style: const TextStyle(fontSize: 10),
//             ),
//           ],
//         ),
//         trailing: achievement.isUnlocked
//             ? Icon(Icons.check_circle, color: achievement.color)
//             : null,
//       ),
//     );
//   }

//   // This method connects to your actual achievements system
//   List<Achievement> _getAchievements() {
//     final firebaseService = FirebaseService();
//     final vocabProvider = ref.read(vocabListStreamProvider).value ?? [];
//     final stats = ref.read(vocabStatsProvider).value;
//     final streakData = stats?['streak'] ?? {'current': 0, 'longest': 0};
//     final quizStats = stats?['quizStats'] ?? {'total': 0, 'bestScore': 0};

//     // Count how many different word types the user has
//     final Set<String> uniqueTypes = {};
//     for (var card in vocabProvider) {
//       for (var type in card.type) {
//         uniqueTypes.add(type.toLowerCase());
//       }
//     }

//     // Count how many different CEFR levels the user has
//     final cefrLevels = stats?['cefrLevels'] as Map<String, dynamic>? ?? {};
//     final Set<String> usedLevels = {};
//     for (var entry in cefrLevels.entries) {
//       if (entry.value > 0) {
//         usedLevels.add(entry.key);
//       }
//     }

//     return [
//       Achievement(
//         title: 'Vocabulary Pioneer',
//         description: 'Add your first 10 vocabulary words',
//         icon: Icons.emoji_events,
//         color: Colors.amber,
//         isUnlocked: vocabProvider.length >= 10,
//         progress: min(vocabProvider.length, 10),
//         target: 10,
//       ),
//       Achievement(
//         title: 'Word Master',
//         description: 'Master 20 vocabulary words',
//         icon: Icons.psychology,
//         color: AppColors.green,
//         isUnlocked: (stats?['masteryBreakdown']['mastered'] ?? 0) >=
//             20, //if ((stats['totalCards'] ?? 0) >= 20) { ... }

//         progress: min(stats?['masteryBreakdown']['mastered'] ?? 0, 20),
//         target: 20,
//       ),
//       Achievement(
//         title: 'Perfect Quiz',
//         description: 'Score 100% on a quiz with at least 10 questions',
//         icon: Icons.task_alt,
//         color: AppColors.blue,
//         isUnlocked: quizStats['bestScore'] == 100,
//         progress: quizStats['bestScore'] == 100 ? 1 : 0,
//         target: 1,
//       ),
//       Achievement(
//         title: 'Streak Champion',
//         description: 'Maintain a 7-day learning streak',
//         icon: Icons.local_fire_department,
//         color: Colors.deepOrange,
//         isUnlocked: streakData['current'] >= 7,
//         progress: min(streakData['current'], 7),
//         target: 7,
//       ),
//       Achievement(
//         title: 'Type Collector',
//         description: 'Add words from all 8 word types',
//         icon: Icons.category,
//         color: AppColors.purple,
//         isUnlocked: uniqueTypes.length >= 8,
//         progress: min(uniqueTypes.length, 8),
//         target: 8,
//       ),
//       Achievement(
//         title: 'CEFR Explorer',
//         description: 'Add words from all 6 CEFR levels',
//         icon: Icons.school,
//         color: Colors.teal,
//         isUnlocked: usedLevels.length >= 6,
//         progress: min(usedLevels.length, 6),
//         target: 6,
//       ),
//     ];
//   }
// }

// Custom radar chart implementation
class WordTypeMasteryRadarChart extends StatelessWidget {
  final List<dynamic> data;

  const WordTypeMasteryRadarChart({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Size(double.infinity, 350),
      painter: RadarChartPainter(
        data: data
            .map((item) => RadarEntry(
                  value: (item['mastery'] ?? 0).toDouble(),
                  title: item['subject'] as String,
                  count: item['total'] as int,
                ))
            .toList(),
        baseColor: Theme.of(context).primaryColor,
        labelColor: AppColors.black,
        fillColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      ),
    );
  }
}

class RadarEntry {
  final double value;
  final String title;
  final int count;

  RadarEntry({
    required this.value,
    required this.title,
    this.count = 0,
  });
}

class RadarChartPainter extends CustomPainter {
  final List<RadarEntry> data;
  final Color baseColor;
  final Color labelColor;
  final Color fillColor;
  final int segments;

  RadarChartPainter({
    required this.data,
    required this.baseColor,
    required this.labelColor,
    required this.fillColor,
    this.segments = 5,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius =
        min(size.width / 2, size.height / 2) - 40; // Margin for labels
    final sides = data.length;

    if (sides == 0) return;

    // Draw axis lines and grid
    _drawAxis(canvas, center, radius, sides);

    // Draw data
    _drawData(canvas, center, radius, sides);

    // Draw labels
    _drawLabels(canvas, center, radius, sides);
  }

  void _drawAxis(Canvas canvas, Offset center, double radius, int sides) {
    final axisPaint = Paint()
      ..color = AppColors.grey
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw polygon grid
    for (int segment = 1; segment <= segments; segment++) {
      final currentRadius = radius * segment / segments;
      final path = Path();

      for (int i = 0; i < sides; i++) {
        final angle = 2 * pi * i / sides - pi / 2; // Start from top (270°)
        final point = Offset(
          center.dx + currentRadius * cos(angle),
          center.dy + currentRadius * sin(angle),
        );

        if (i == 0) {
          path.moveTo(point.dx, point.dy);
        } else {
          path.lineTo(point.dx, point.dy);
        }
      }

      path.close();
      canvas.drawPath(path, axisPaint);
    }

    // Draw axis lines
    for (int i = 0; i < sides; i++) {
      final angle = 2 * pi * i / sides - pi / 2; // Start from top (270°)
      final point = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );

      canvas.drawLine(center, point, axisPaint);
    }
  }

  void _drawData(Canvas canvas, Offset center, double radius, int sides) {
    final dataLinePaint = Paint()
      ..color = baseColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final dataFillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    final pointPaint = Paint()
      ..color = baseColor
      ..style = PaintingStyle.fill;

    final path = Path();

    for (int i = 0; i < sides; i++) {
      final angle = 2 * pi * i / sides - pi / 2;
      final value = data[i].value / 100; // Convert percentage to 0-1 scale
      final adjustedRadius = radius * value;

      final point = Offset(
        center.dx + adjustedRadius * cos(angle),
        center.dy + adjustedRadius * sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }

      // Draw data points
      canvas.drawCircle(point, 5, pointPaint);

      // Draw value labels
      final textPainter = TextPainter(
        text: TextSpan(
          text: '${data[i].value.toInt()}%',
          style: TextStyle(
            color: baseColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Position value labels slightly outside the data points
      final valueTextOffset = Offset(
        point.dx - textPainter.width / 2 + 8 * cos(angle),
        point.dy - textPainter.height / 2 + 8 * sin(angle),
      );

      textPainter.paint(canvas, valueTextOffset);
    }

    path.close();

    // Draw filled area first
    canvas.drawPath(path, dataFillPaint);

    // Then draw the outline
    canvas.drawPath(path, dataLinePaint);
  }

  void _drawLabels(Canvas canvas, Offset center, double radius, int sides) {
    final labelRadius = radius + 25; // Position labels outside the chart

    for (int i = 0; i < sides; i++) {
      if (i >= data.length) continue; // Skip if index is out of bounds

      final angle = 2 * pi * i / sides - pi / 2;

      final labelCenter = Offset(
        center.dx + labelRadius * cos(angle),
        center.dy + labelRadius * sin(angle),
      );

      // Create and configure the text painter for the label
      final textPainter = TextPainter(
        text: TextSpan(
          text: data[i].title,
          style: TextStyle(
            color: labelColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr, // Explicitly set text direction
        textAlign: TextAlign.center,
      );

      // Layout must be called after setting text direction
      textPainter.layout();

      // Adjust position to center the text around the point
      final labelOffset = Offset(
        labelCenter.dx - textPainter.width / 2,
        labelCenter.dy - textPainter.height / 2,
      );

      // Paint the main label
      textPainter.paint(canvas, labelOffset);

      // Add the word count below the label
      final countPainter = TextPainter(
        text: TextSpan(
          text: '(${data[i].count})',
          style: TextStyle(
            color: AppColors.grey,
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.ltr, // Explicitly set text direction
        textAlign: TextAlign.center,
      );

      // Layout must be called after setting text direction
      countPainter.layout();

      final countOffset = Offset(
        labelCenter.dx - countPainter.width / 2,
        labelCenter.dy + textPainter.height / 2,
      );

      countPainter.paint(canvas, countOffset);
    }
  }

  @override
  bool shouldRepaint(covariant RadarChartPainter oldDelegate) {
    return data != oldDelegate.data ||
        baseColor != oldDelegate.baseColor ||
        labelColor != oldDelegate.labelColor ||
        fillColor != oldDelegate.fillColor ||
        segments != oldDelegate.segments;
  }
}

class CefrLevelDistributionChart extends StatelessWidget {
  final Map<String, dynamic> data;

  const CefrLevelDistributionChart({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final sortedLevels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _calculateMaxY(),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            // tooltipColor: AppColors.white,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final level = sortedLevels[group.x.toInt()];
              final count = data[level] ?? 0;
              return BarTooltipItem(
                '$level: $count words',
                const TextStyle(
                  color: AppColors.black,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < sortedLevels.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      sortedLevels[value.toInt()],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: _calculateYAxisInterval(),
              getTitlesWidget: (value, meta) {
                if (value == 0) {
                  return const SizedBox.shrink();
                }
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.grey,
                    ),
                  ),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: List.generate(
          sortedLevels.length,
          (index) {
            final level = sortedLevels[index];
            final value = data[level] ?? 0;
            return BarChartGroupData(
              x: index,
              barRods: [
                BarChartRodData(
                  toY: value.toDouble(),
                  color: LevelUtils.getLevelColor(level),
                  width: 20,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }

  double _calculateMaxY() {
    final values = data.values.map((v) => v as int).toList();
    if (values.isEmpty) return 10;

    final maxValue = values.reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.2).ceilToDouble(); // Add 20% margin
  }

  double _calculateYAxisInterval() {
    final maxY = _calculateMaxY();
    if (maxY <= 20) return 5;
    if (maxY <= 50) return 10;
    if (maxY <= 100) return 20;
    if (maxY <= 200) return 50;
    return 100;
  }
}

class Achievement {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final bool isUnlocked;
  final int progress;
  final int target;

  const Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.isUnlocked,
    required this.progress,
    required this.target,
  });
}
