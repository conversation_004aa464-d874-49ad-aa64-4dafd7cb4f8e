import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/user/models/user_model.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:vocadex/src/features/achievements/streaks/streak_tracker.dart';
import 'package:vocadex/src/services/quiz_service.dart';
import 'package:vocadex/src/features/achievements/points/points_provider.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Provider for fetching user statistics
final userStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final firebaseService = FirebaseService();
  final streakTracker = StreakTracker();
  final quizService = QuizService();

  try {
    // Get all vocabulary cards
    final cards = await firebaseService.fetchVocabulary();

    // Calculate basic stats
    final totalCards = cards.length;

    // Count by mastery status
    int wildCount = 0;
    int tamedCount = 0;
    int masteredCount = 0;

    // Count by word type and calculate average mastery by type
    Map<String, int> typeCount = {};
    Map<String, double> typeAvgMastery = {};
    Map<String, int> typeTotalMastery = {};

    // Count by CEFR level
    Map<String, int> levelCount = {
      'A1': 0,
      'A2': 0,
      'B1': 0,
      'B2': 0,
      'C1': 0,
      'C2': 0
    };

    // Word type mastery data for radar chart
    Map<String, Map<String, dynamic>> wordTypeMastery = {
      'Noun': {'count': 0, 'masteredCount': 0},
      'Verb': {'count': 0, 'masteredCount': 0},
      'Adjective': {'count': 0, 'masteredCount': 0},
      'Adverb': {'count': 0, 'masteredCount': 0},
      'Pronoun': {'count': 0, 'masteredCount': 0},
      'Preposition': {'count': 0, 'masteredCount': 0},
      'Conjunction': {'count': 0, 'masteredCount': 0},
      'Interjection': {'count': 0, 'masteredCount': 0},
    };

    // Daily activity - last 30 days
    Map<String, int> dailyActivity = {};
    final now = DateTime.now();
    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      final dateStr = DateFormat('yyyy-MM-dd').format(date);
      dailyActivity[dateStr] = 0;
    }

    // Process cards
    for (final card in cards) {
      // Count by mastery status
      final status = card.getMasteryStatus();
      switch (status) {
        case MasteryStatus.wild:
          wildCount++;
          break;
        case MasteryStatus.tamed:
          tamedCount++;
          break;
        case MasteryStatus.mastered:
          masteredCount++;
          break;
      }

      // Count by word type
      for (final type in card.type) {
        final normalizedType = type.toLowerCase();
        typeCount[normalizedType] = (typeCount[normalizedType] ?? 0) + 1;

        // Sum mastery levels by type for average calculation
        typeTotalMastery[normalizedType] =
            (typeTotalMastery[normalizedType] ?? 0) + card.masteryLevel;

        // Update type mastery data for radar chart
        final normalizedTypeCapitalized =
            normalizedType[0].toUpperCase() + normalizedType.substring(1);
        if (wordTypeMastery.containsKey(normalizedTypeCapitalized)) {
          wordTypeMastery[normalizedTypeCapitalized]!['count'] =
              (wordTypeMastery[normalizedTypeCapitalized]!['count'] ?? 0) + 1;

          if (status == MasteryStatus.mastered) {
            wordTypeMastery[normalizedTypeCapitalized]!['masteredCount'] =
                (wordTypeMastery[normalizedTypeCapitalized]!['masteredCount'] ??
                        0) +
                    1;
          }
        }
      }

      // Count by CEFR level
      final level = card.level;
      if (levelCount.containsKey(level)) {
        levelCount[level] = (levelCount[level] ?? 0) + 1;
      }

      // Daily activity
      if (card.lastReviewedAt != null) {
        final dateStr = DateFormat('yyyy-MM-dd').format(card.lastReviewedAt!);
        if (dailyActivity.containsKey(dateStr)) {
          dailyActivity[dateStr] = (dailyActivity[dateStr] ?? 0) + 1;
        }
      }
    }

    // Calculate average mastery by type
    typeAvgMastery = Map.fromEntries(typeTotalMastery.entries.map((entry) =>
        MapEntry(entry.key, entry.value / (typeCount[entry.key] ?? 1))));

    // Calculate mastery percentages for radar chart
    final List<Map<String, dynamic>> radarData = [];
    wordTypeMastery.forEach((type, data) {
      final count = data['count'] ?? 0;
      final masteredCount = data['masteredCount'] ?? 0;

      // Calculate mastery percentage (avoid division by zero)
      double masteryPercentage = 0;
      if (count > 0) {
        masteryPercentage = (masteredCount / count) * 100;
      }

      radarData.add({
        'subject': type,
        'mastery': masteryPercentage.round(),
        'total': count
      });
    });

    // Get quiz performance data from the quiz service
    final quizStats = await quizService.getQuizStatistics();

    // Get real streak data from the streak tracker
    final currentStreak = await streakTracker.getCurrentStreak();
    final longestStreak = await streakTracker.getLongestStreak();
    final streakData = await streakTracker.getStreakData();

    // Points are now managed by the centralized provider
    // No need to store totalPoints here as it will be fetched by the provider

    // Get user's global rank
    final globalRank = await _getUserGlobalRank() ?? 0;

    // Combine all stats
    return {
      'totalCards': totalCards,
      // Points are now managed by the centralized provider
      'globalRank': globalRank,
      'masteryCount': {
        'wild': wildCount,
        'tamed': tamedCount,
        'mastered': masteredCount,
      },
      'typeCount': typeCount,
      'typeAvgMastery': typeAvgMastery,
      'levelCount': levelCount,
      'dailyActivity': dailyActivity,
      'quizPerformance': quizStats,
      'streakData': {
        'currentStreak': currentStreak,
        'longestStreak': longestStreak,
        'totalDays': streakData['streak_dates']?.length ?? 0,
        'streakDates': streakData['streak_dates'] ?? [],
      },
      'wordTypeRadarData': radarData,
      'cefrLevels': levelCount,
    };
  } catch (e) {
    debugPrint('Error fetching stats: $e');
    return {};
  }
});

/// Get the user's global rank
Future<int?> _getUserGlobalRank() async {
  try {
    final firestore = FirebaseFirestore.instance;
    final auth = FirebaseAuth.instance;
    final userId = auth.currentUser?.uid;

    if (userId == null) return null;

    // Get the current user's score
    final userDoc = await firestore.collection('leaderboard').doc(userId).get();
    if (!userDoc.exists) return null;

    final userScore = userDoc.data()?['score'] as int? ?? 0;

    // Count how many users have a higher score
    final higherScoresQuery = await firestore
        .collection('leaderboard')
        .where('score', isGreaterThan: userScore)
        .get();

    // Rank is the number of users with higher scores + 1
    return higherScoresQuery.docs.length + 1;
  } catch (e) {
    debugPrint('Error getting user global rank: $e');
    return null;
  }
}

/// Provider to fetch the current user data
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  final firebaseService = FirebaseService();
  try {
    return await firebaseService.getUserData();
  } catch (e) {
    debugPrint('Error fetching user data: $e');
    return null;
  }
});

class UserStatsScreen extends ConsumerStatefulWidget {
  const UserStatsScreen({super.key});

  @override
  ConsumerState<UserStatsScreen> createState() => _UserStatsScreenState();
}

class _UserStatsScreenState extends ConsumerState<UserStatsScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statsAsync = ref.watch(userStatsProvider);
    final userAsync = ref.watch(currentUserProvider);

    return GradientScaffold(
      appBar: AppBar(
        title: const Text('My Statistics'),
        backgroundColor: AppColors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to settings screen
              context.pushNamed(RouteNames.profile);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // User profile section
          _buildUserProfileSection(userAsync, statsAsync),

          // Stats view
          Expanded(
            child: _buildStatsView(statsAsync),
          ),
        ],
      ),
    );
  }

  Widget _buildUserProfileSection(AsyncValue<UserModel?> userAsync,
      AsyncValue<Map<String, dynamic>> statsAsync) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(178),borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(13),blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // User profile image
          userAsync.when(
            data: (user) => CircleAvatar(
              radius: 36,
              backgroundColor: AppColors.topRightGradientColor.withAlpha(51),child: user?.imageUrl != null && user!.imageUrl!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(36),
                      child: Image.network(
                        user.imageUrl!,
                        width: 72,
                        height: 72,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.person,
                          size: 36,
                          color: AppColors.topRightGradientColor,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: 36,
                      color: AppColors.topRightGradientColor,
                    ),
            ),
            loading: () => const CircleAvatar(
              radius: 36,
              child: CircularProgressIndicator(),
            ),
            error: (_, __) => CircleAvatar(
              radius: 36,
              backgroundColor: AppColors.topRightGradientColor.withAlpha(51),child: Icon(
                Icons.person,
                size: 36,
                color: AppColors.topRightGradientColor,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // User stats
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User name
                userAsync.when(
                  data: (user) => Text(
                    '${user?.firstName ?? 'User'} ${user?.lastName ?? ''}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  loading: () => const Text('Loading...'),
                  error: (_, __) => const Text('User'),
                ),

                const SizedBox(height: 8),

                // Stats grid
                statsAsync.when(
                  data: (stats) {
                    if (stats.isEmpty) {
                      return const Text('No statistics available');
                    }

                    final totalCards = stats['totalCards'] as int;
                    final totalPoints =
                        ref.watch(userPointsProvider).value ?? 0;
                    final globalRank = stats['globalRank'] as int;
                    final streakData =
                        stats['streakData'] as Map<String, dynamic>;
                    final currentStreak = streakData['currentStreak'] as int;

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildStatItem('Cards', totalCards.toString()),
                        _buildStatItem('Points', totalPoints.toString()),
                        _buildStatItem('Rank', '#$globalRank'),
                        _buildStatItem('Streak', '$currentStreak days'),
                      ],
                    );
                  },
                  loading: () => const LinearProgressIndicator(),
                  error: (_, __) => const Text('Error loading statistics'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppColors.grey.withAlpha(178),fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsView(AsyncValue<Map<String, dynamic>> statsAsync) {
    return statsAsync.when(
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Text('Error loading statistics: $error'),
      ),
      data: (stats) {
        if (stats.isEmpty) {
          return const Center(
            child:
                Text('No statistics available yet. Start adding vocabulary!'),
          );
        }

        return _buildStatsContent(context, stats);
      },
    );
  }

  Widget _buildStatsContent(BuildContext context, Map<String, dynamic> stats) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary cards
          _buildSummaryCards(context, stats),

          // const SizedBox(height: 24),

          // // Quiz Performance Trend (new section)
          // _buildQuizPerformanceTrend(context, stats),

          const SizedBox(height: 24),

          // Mastery distribution
          _buildMasteryDistribution(context, stats),

          const SizedBox(height: 24),

          // Word Type Mastery Radar Chart (new section)
          _buildWordTypeMasteryRadarChart(context, stats),

          const SizedBox(height: 24),

          // CEFR Level distribution
          _buildCefrDistribution(context, stats),

          const SizedBox(height: 24),

          // Quiz performance
          _buildQuizPerformance(context, stats),

          const SizedBox(height: 24),

          // Activity calendar
          _buildActivityCalendar(context, stats),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, Map<String, dynamic> stats) {
    final totalCards = stats['totalCards'] as int;
    final masteryMap = stats['masteryCount'] as Map<String, int>;
    final streakData = stats['streakData'] as Map<String, dynamic>;
    final totalPoints = ref.watch(userPointsProvider).value ?? 0;
    final masteredCount = masteryMap['mastered'] ?? 0;
    final masteryRate =
        totalCards > 0 ? (masteredCount * 100 / totalCards).round() : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Summary',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildSummaryCard(
              context,
              'Total Vocabulary',
              totalCards.toString(),
              Icons.menu_book,
              const Color(0xFF60A5FA),
            ),
            _buildSummaryCard(
              context,
              'Current Streak',
              '${streakData['currentStreak']} days',
              Icons.local_fire_department,
              const Color(0xFFFBBF24),
            ),
            _buildSummaryCard(
              context,
              'Mastery Rate',
              '$masteryRate%',
              Icons.stars,
              const Color(0xFF4ADE80),
            ),
            _buildSummaryCard(
              context,
              'Total Points',
              totalPoints.toString(),
              Icons.emoji_events,
              const Color(0xFFF87171),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMasteryDistribution(
      BuildContext context, Map<String, dynamic> stats) {
    final masteryMap = stats['masteryCount'] as Map<String, int>;
    final wildCount = masteryMap['wild'] ?? 0;
    final tamedCount = masteryMap['tamed'] ?? 0;
    final masteredCount = masteryMap['mastered'] ?? 0;
    final totalCards = wildCount + tamedCount + masteredCount;

    final wildPercent = totalCards > 0 ? wildCount * 100 / totalCards : 0;
    final tamedPercent = totalCards > 0 ? tamedCount * 100 / totalCards : 0;
    final masteredPercent =
        totalCards > 0 ? masteredCount * 100 / totalCards : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Mastery Distribution',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildMasteryLegendItem('Wild', wildCount, AppColors.wild),
                    _buildMasteryLegendItem(
                        'Tamed', tamedCount, AppColors.tamed),
                    _buildMasteryLegendItem(
                        'Mastered', masteredCount, AppColors.mastered),
                  ],
                ),
                const SizedBox(height: 16),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    height: 24,
                    child: Row(
                      children: [
                        Expanded(
                          flex: wildCount,
                          child: Container(color: AppColors.wild),
                        ),
                        Expanded(
                          flex: tamedCount,
                          child: Container(color: AppColors.tamed),
                        ),
                        Expanded(
                          flex: masteredCount,
                          child: Container(color: AppColors.mastered),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Total Cards: $totalCards',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Wild: ${wildPercent.toStringAsFixed(1)}% · Tamed: ${tamedPercent.toStringAsFixed(1)}% · Mastered: ${masteredPercent.toStringAsFixed(1)}%',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMasteryLegendItem(String label, int count, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            const SizedBox(width: 4),
            Text(label),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildWordTypeMasteryRadarChart(
      BuildContext context, Map<String, dynamic> stats) {
    final radarData = stats['wordTypeRadarData'] as List<dynamic>? ?? [];

    if (radarData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Word Type Mastery',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: WordTypeMasteryRadarChart(data: radarData),
          ),
        ),
      ],
    );
  }

  Widget _buildQuizPerformanceTrend(
      BuildContext context, Map<String, dynamic> stats) {
    final quizData = stats['quizPerformance'] as Map<String, dynamic>? ?? {};
    final history = quizData['quizHistory'] as List? ?? [];

    if (history.isEmpty) {
      return const SizedBox.shrink(); // Don't show chart if no data
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Quiz Performance Trend',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                SizedBox(
                  height: 200,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(show: false),
                      titlesData: FlTitlesData(
                        rightTitles: AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                        topTitles: AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              if (value < 0 || value >= history.length) {
                                return const SizedBox();
                              }
                              final dateValue = history[value.toInt()]['date'];
                              if (dateValue == null) {
                                return const SizedBox();
                              }
                              final date = dateValue as DateTime;
                              return Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(DateFormat('MM/dd').format(date)),
                              );
                            },
                          ),
                        ),
                      ),
                      borderData: FlBorderData(show: false),
                      lineBarsData: [
                        LineChartBarData(
                          spots: List.generate(history.length, (index) {
                            try {
                              // Handle both int and double values safely
                              final score = history[index]['score'];
                              final total = history[index]['total'];

                              // Handle null values
                              if (score == null || total == null) {
                                return FlSpot(index.toDouble(), 0);
                              }

                              final double scoreValue = score is int
                                  ? score.toDouble()
                                  : (score is double ? score : 0.0);

                              final double totalValue = total is int
                                  ? total.toDouble()
                                  : (total is double ? total : 1.0);

                              return FlSpot(
                                  index.toDouble(),
                                  totalValue > 0
                                      ? (scoreValue / totalValue * 100)
                                      : 0.0);
                            } catch (e) {
                              // Fallback for any parsing errors
                              return FlSpot(index.toDouble(), 0);
                            }
                          }),
                          isCurved: true,
                          color: AppColors.infoLight,
                          barWidth: 3,
                          isStrokeCapRound: true,
                          dotData: FlDotData(show: true),
                          belowBarData: BarAreaData(
                            show: true,
                            color: AppColors.infoLight.withAlpha(51),),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Quizzes: ${quizData['totalQuizzes'] ?? 0}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Best Score: ${quizData['bestScore'] ?? 0}%',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCefrDistribution(
      BuildContext context, Map<String, dynamic> stats) {
    final levelCount = stats['levelCount'] as Map<String, dynamic>;

    if (levelCount.isEmpty) {
      return const SizedBox.shrink();
    }

    // CEFR Levels
    const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];
    final levelColors = [
      AppColors.levelA1,
      AppColors.levelA2,
      AppColors.levelB1,
      AppColors.levelB2,
      AppColors.levelC1,
      AppColors.levelC2,
    ];

    // Find max count for scaling
    int maxCount = 0;
    for (final level in levels) {
      final count = levelCount[level] as int? ?? 0;
      if (count > maxCount) maxCount = count;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'CEFR Level Distribution',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    levels.length,
                    (i) => _buildLevelLegendItem(
                      levels[i],
                      levelCount[levels[i]] ?? 0,
                      levelColors[i],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 150,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: List.generate(
                      levels.length,
                      (i) {
                        final count = (levelCount[levels[i]] ?? 0) as int;
                        final barHeight =
                            maxCount > 0 ? (count / maxCount * 100) : 0.0;

                        return Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  count.toString(),
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  height: barHeight,
                                  decoration: BoxDecoration(
                                    color: levelColors[i],
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(4),
                                      topRight: Radius.circular(4),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  levels[i],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLevelLegendItem(String label, int count, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 2),
            Text(
              label,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildQuizPerformance(
      BuildContext context, Map<String, dynamic> stats) {
    final quizPerformance =
        stats['quizPerformance'] as Map<String, dynamic>? ?? {};

    // Add null safety with default values
    final totalQuizzes = quizPerformance['totalQuizzes'] as int? ?? 0;
    final avgScore =
        (quizPerformance['averageAccuracy'] as num?)?.toDouble() ?? 0.0;
    final bestScore = quizPerformance['bestScore'] as int? ?? 0;

    // Calculate correct rate with null safety
    final totalCorrect = quizPerformance['correctAnswers'] as int? ?? 0;
    final totalIncorrect = quizPerformance['incorrectAnswers'] as int? ?? 0;
    final totalQuestions = totalCorrect + totalIncorrect;
    final correctRate =
        totalQuestions > 0 ? (totalCorrect / totalQuestions * 100) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Quiz Performance',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildQuizStat('Quizzes', totalQuizzes.toString()),
                    _buildQuizStat(
                        'Avg Score', '${avgScore.toStringAsFixed(1)}%'),
                    _buildQuizStat('Best Score', '$bestScore%'),
                  ],
                ),
                const SizedBox(height: 16),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text('Correct Answer Rate'),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Container(
                      height: 12,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: AppColors.grey.withAlpha(76),),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: SizedBox.expand(
                          child: ShaderMask(
                            shaderCallback: (Rect bounds) {
                              return LinearGradient(
                                colors: [
                                  AppColors.gradientButton1,
                                  AppColors.gradientButton2,
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ).createShader(bounds);
                            },
                            child: LinearProgressIndicator(
                              value: correctRate / 100,
                              minHeight: 12,
                              backgroundColor: Colors.transparent,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${correctRate.toStringAsFixed(1)}%',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuizStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.black.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityCalendar(
      BuildContext context, Map<String, dynamic> stats) {
    final dailyActivity = stats['dailyActivity'] as Map<String, dynamic>;

    if (dailyActivity.isEmpty) {
      return const SizedBox.shrink();
    }

    // Get the last 7 days activity
    final lastWeek = <String, int>{};
    final now = DateTime.now();
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dateStr = DateFormat('yyyy-MM-dd').format(date);
      final dayStr = DateFormat('E').format(date); // Day of week (Mon, Tue)
      lastWeek[dayStr] = dailyActivity[dateStr] ?? 0;
    }

    // Find max count for scaling
    int maxCount = 0;
    for (final count in lastWeek.values) {
      if (count > maxCount) maxCount = count;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Weekly Activity',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              height: 180,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: lastWeek.entries.map((entry) {
                  final day = entry.key;
                  final count = entry.value;
                  final barHeight =
                      maxCount > 0 ? (count / maxCount * 120) : 0.0;

                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            count.toString(),
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            height: barHeight,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.gradientButton2,
                                  AppColors.gradientButton1,
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.topRightGradientColor
                                      .withAlpha(76),blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            day,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class WordTypeMasteryRadarChart extends StatelessWidget {
  final List<dynamic> data;

  const WordTypeMasteryRadarChart({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: const Size(double.infinity, 350),
      painter: RadarChartPainter(
        data: data
            .map((item) => RadarEntry(
                  value: (item['mastery'] ?? 0).toDouble(),
                  title: item['subject'] as String,
                  count: item['total'] as int,
                ))
            .toList(),
        baseColor: Theme.of(context).primaryColor,
        labelColor: AppColors.black.withAlpha(222),fillColor: Theme.of(context).primaryColor.withAlpha(51),),
    );
  }
}

class RadarEntry {
  final double value;
  final String title;
  final int count;

  RadarEntry({
    required this.value,
    required this.title,
    this.count = 0,
  });
}

class RadarChartPainter extends CustomPainter {
  final List<RadarEntry> data;
  final Color baseColor;
  final Color labelColor;
  final Color fillColor;
  final int segments;

  RadarChartPainter({
    required this.data,
    required this.baseColor,
    required this.labelColor,
    required this.fillColor,
    this.segments = 5,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius =
        math.min(size.width / 2, size.height / 2) - 40; // Margin for labels
    final sides = data.length;

    if (sides == 0) return;

    // Draw axis lines and grid
    _drawAxis(canvas, center, radius, sides);

    // Draw data
    _drawData(canvas, center, radius, sides);

    // Draw labels
    _drawLabels(canvas, center, radius, sides);
  }

  void _drawAxis(Canvas canvas, Offset center, double radius, int sides) {
    final axisPaint = Paint()
      ..color = AppColors.grey.withAlpha(76)..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw polygon grid
    for (int segment = 1; segment <= segments; segment++) {
      final currentRadius = radius * segment / segments;
      final path = Path();

      for (int i = 0; i < sides; i++) {
        final angle =
            2 * math.pi * i / sides - math.pi / 2; // Start from top (270°)
        final point = Offset(
          center.dx + currentRadius * math.cos(angle),
          center.dy + currentRadius * math.sin(angle),
        );

        if (i == 0) {
          path.moveTo(point.dx, point.dy);
        } else {
          path.lineTo(point.dx, point.dy);
        }
      }

      path.close();
      canvas.drawPath(path, axisPaint);
    }

    // Draw axis lines
    for (int i = 0; i < sides; i++) {
      final angle =
          2 * math.pi * i / sides - math.pi / 2; // Start from top (270°)
      final point = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );

      canvas.drawLine(center, point, axisPaint);
    }
  }

  void _drawData(Canvas canvas, Offset center, double radius, int sides) {
    final dataLinePaint = Paint()
      ..color = baseColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final dataFillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    final pointPaint = Paint()
      ..color = baseColor
      ..style = PaintingStyle.fill;

    final path = Path();

    for (int i = 0; i < sides; i++) {
      final angle = 2 * math.pi * i / sides - math.pi / 2;
      final value = data[i].value / 100; // Convert percentage to 0-1 scale
      final adjustedRadius = radius * value;

      final point = Offset(
        center.dx + adjustedRadius * math.cos(angle),
        center.dy + adjustedRadius * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }

      // Draw data points
      canvas.drawCircle(point, 5, pointPaint);

      // Draw value labels using ParagraphBuilder
      final valueParagraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(
        textAlign: TextAlign.center,
        fontSize: 10,
      ))
        ..pushStyle(ui.TextStyle(
          color: baseColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ))
        ..addText('${data[i].value.toInt()}%');

      final valueParagraph = valueParagraphBuilder.build()
        ..layout(ui.ParagraphConstraints(width: 50));

      // Position value labels slightly outside the data points
      final valueTextOffset = Offset(
        point.dx - valueParagraph.width / 2 + 8 * math.cos(angle),
        point.dy - valueParagraph.height / 2 + 8 * math.sin(angle),
      );

      canvas.drawParagraph(valueParagraph, valueTextOffset);
    }

    path.close();

    // Draw filled area first
    canvas.drawPath(path, dataFillPaint);

    // Then draw the outline
    canvas.drawPath(path, dataLinePaint);
  }

  void _drawLabels(Canvas canvas, Offset center, double radius, int sides) {
    final labelRadius = radius + 25; // Position labels outside the chart

    for (int i = 0; i < sides; i++) {
      if (i >= data.length) continue; // Skip if index is out of bounds

      final angle = 2 * math.pi * i / sides - math.pi / 2;

      final labelCenter = Offset(
        center.dx + labelRadius * math.cos(angle),
        center.dy + labelRadius * math.sin(angle),
      );

      // Create and draw the title text
      final titleStyle = TextStyle(
        color: labelColor,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      );

      final paragraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(
        textAlign: TextAlign.center,
        fontSize: 12,
      ))
        ..pushStyle(ui.TextStyle(
          color: labelColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ))
        ..addText(data[i].title);

      final paragraph = paragraphBuilder.build()
        ..layout(ui.ParagraphConstraints(width: 100));

      canvas.drawParagraph(
        paragraph,
        Offset(
          labelCenter.dx - paragraph.width / 2,
          labelCenter.dy - paragraph.height / 2,
        ),
      );

      // Create and draw the count text
      final countParagraphBuilder = ui.ParagraphBuilder(ui.ParagraphStyle(
        textAlign: TextAlign.center,
        fontSize: 10,
      ))
        ..pushStyle(ui.TextStyle(
          color: AppColors.grey.withAlpha(178),fontSize: 10,
        ))
        ..addText('(${data[i].count})');

      final countParagraph = countParagraphBuilder.build()
        ..layout(ui.ParagraphConstraints(width: 100));

      canvas.drawParagraph(
        countParagraph,
        Offset(
          labelCenter.dx - countParagraph.width / 2,
          labelCenter.dy + paragraph.height / 2,
        ),
      );
    }
  }

  @override
  bool shouldRepaint(covariant RadarChartPainter oldDelegate) {
    return data != oldDelegate.data ||
        baseColor != oldDelegate.baseColor ||
        labelColor != oldDelegate.labelColor ||
        fillColor != oldDelegate.fillColor ||
        segments != oldDelegate.segments;
  }
}
