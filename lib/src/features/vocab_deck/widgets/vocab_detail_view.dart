import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A component for displaying a detailed view of a vocabulary card
class VocabDetailView extends StatelessWidget {
  /// The vocabulary card to display
  final VocabCard vocab;

  /// Scroll controller for the list view
  final ScrollController scrollController;

  /// Optional callback when the close button is pressed
  final VoidCallback? onClose;

  /// Constructor
  const VocabDetailView({
    super.key,
    required this.vocab,
    required this.scrollController,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header with close button
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 16, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Hero(
                  tag: 'vocab_word_${vocab.id}',
                  child: Text(
                    vocab.word,
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose ?? () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          // Divider
          const Divider(),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: VocabularyCard(
                card: vocab,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A helper class to show a vocabulary detail modal
class VocabDetailModal {
  /// Show a modal bottom sheet with the vocabulary detail
  static void show(BuildContext context, VocabCard vocab) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppColors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (_, controller) => VocabDetailView(
          vocab: vocab,
          scrollController: controller,
        ),
      ),
    );
  }
}

/// A component for displaying a vocabulary card in a carousel
class VocabCarouselView extends StatelessWidget {
  /// List of vocabulary cards
  final List<VocabCard> cards;

  /// The current index
  final int currentIndex;

  /// Page controller for swiping between cards
  final PageController pageController;

  /// Callback when a page changes
  final Function(int)? onPageChanged;

  /// Callback when close button is pressed
  final VoidCallback? onClose;

  /// Constructor
  const VocabCarouselView({
    super.key,
    required this.cards,
    required this.currentIndex,
    required this.pageController,
    this.onPageChanged,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    if (cards.isEmpty || currentIndex >= cards.length) {
      return const Center(child: Text('No cards to display'));
    }

    return Column(
      children: [
        // Card carousel
        Expanded(
          child: PageView.builder(
            controller: pageController,
            itemCount: cards.length,
            onPageChanged: onPageChanged,
            itemBuilder: (context, index) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: VocabularyCard(
                  card: cards[index],
                ),
              );
            },
          ),
        ),

        // Navigation indicator
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${currentIndex + 1} of ${cards.length}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
