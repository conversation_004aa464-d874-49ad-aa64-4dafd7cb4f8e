import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Stream provider for watching vocabulary cards
final vocabListStreamProvider = StreamProvider<List<VocabCard>>((ref) {
  return FirebaseService().watchVocabulary();
});

/// Provider for selected vocabulary cards (for quiz generation, etc.)
final selectedVocabsProvider = StateProvider<Set<String>>((ref) => {});

/// Enum for different sorting types
enum VocabSortType { alphabetical, level, type, mastery }

/// Provider for the current sort type
final vocabSortTypeProvider =
    StateProvider<VocabSortType>((ref) => VocabSortType.alphabetical);

/// Enum for different view modes
enum VocabViewMode { list, grid }

/// Provider for tracking the current view mode
final vocabViewModeProvider =
    StateProvider<VocabViewMode>((ref) => VocabViewMode.list);

/// Provider for the current card index when in carousel view
final currentCardIndexProvider = StateProvider<int>((ref) => 0);

/// Provider for determining if detail view is open
final isDetailViewOpenProvider = StateProvider<bool>((ref) => false);
