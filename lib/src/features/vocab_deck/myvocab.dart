// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';

// import 'package:vocadex/src/features/vocabcard/model_vocabcard.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// // Provider definitions
// final vocabListStreamProvider = StreamProvider<List<VocabCard>>((ref) {
//   return FirebaseService().watchVocabulary();
// });

// final selectedVocabsProvider = StateProvider<Set<String>>((ref) => {});

// final sortTypeProvider =
//     StateProvider<SortType>((ref) => SortType.alphabetical);

// enum SortType { alphabetical, level, type, mastery }

// class MyVocabScreen extends ConsumerWidget {
//   const MyVocabScreen({super.key});

//   void _showVocabPreview(BuildContext context, VocabCard vocab) {
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: AppColors.transparent,
//       builder: (context) => DraggableScrollableSheet(
//         initialChildSize: 0.9,
//         minChildSize: 0.5,
//         maxChildSize: 0.95,
//         builder: (_, controller) => Container(
//           decoration: BoxDecoration(
//             color: Theme.of(context).scaffoldBackgroundColor,
//             borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
//           ),
//           child: VocabPreviewCard(
//             vocab: vocab,
//             scrollController: controller,
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final vocabsAsync = ref.watch(vocabListStreamProvider);
//     final selectedVocabs = ref.watch(selectedVocabsProvider);
//     final sortType = ref.watch(sortTypeProvider);

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('My Vocabulary'),
//         actions: [
//           PopupMenuButton<SortType>(
//             icon: const Icon(Icons.sort),
//             onSelected: (SortType value) {
//               ref.read(sortTypeProvider.notifier).state = value;
//             },
//             itemBuilder: (BuildContext context) => <PopupMenuEntry<SortType>>[
//               const PopupMenuItem<SortType>(
//                 value: SortType.alphabetical,
//                 child: Text('Sort Alphabetically'),
//               ),
//               const PopupMenuItem<SortType>(
//                 value: SortType.level,
//                 child: Text('Sort by Level'),
//               ),
//               const PopupMenuItem<SortType>(
//                 value: SortType.type,
//                 child: Text('Sort by Type'),
//               ),
//               const PopupMenuItem<SortType>(
//                 value: SortType.mastery,
//                 child: Text('Sort by Mastery'),
//               ),
//             ],
//           ),
//         ],
//       ),
//       body: vocabsAsync.when(
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stackTrace) => Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Text('Error: $error'),
//               const SizedBox(height: 16),
//               ElevatedButton(
//                 onPressed: () {
//                   ref.refresh(vocabListStreamProvider);
//                 },
//                 child: const Text('Retry'),
//               ),
//             ],
//           ),
//         ),
//         data: (vocabs) {
//           final sortedVocabs = _sortVocabulary(vocabs, sortType);

//           if (sortedVocabs.isEmpty) {
//             return const Center(
//               child: Text('No vocabulary words yet. Add some to get started!'),
//             );
//           }

//           return Column(
//             children: [
//               Expanded(
//                 child: ListView.builder(
//                   itemCount: sortedVocabs.length,
//                   itemBuilder: (context, index) {
//                     final vocab = sortedVocabs[index];
//                     return ListTile(
//                       title: Text(vocab.word),
//                       subtitle:
//                           Text('${vocab.level} • ${vocab.type.join(", ")}'),
//                       onTap: () => _showVocabPreview(context, vocab),
//                       trailing: Row(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Checkbox(
//                             value: selectedVocabs.contains(vocab.id),
//                             onChanged: (bool? value) {
//                               if (value == true) {
//                                 ref
//                                     .read(selectedVocabsProvider.notifier)
//                                     .state = {...selectedVocabs, vocab.id};
//                               } else {
//                                 final newSelected =
//                                     Set<String>.from(selectedVocabs);
//                                 newSelected.remove(vocab.id);
//                                 ref
//                                     .read(selectedVocabsProvider.notifier)
//                                     .state = newSelected;
//                               }
//                             },
//                           ),
//                         ],
//                       ),
//                     );
//                   },
//                 ),
//               ),
//               if (selectedVocabs.isNotEmpty)
//                 Padding(
//                   padding: const EdgeInsets.all(16.0),
//                   child: ElevatedButton(
//                     onPressed: () {
//                       ScaffoldMessenger.of(context).showSnackBar(
//                         const SnackBar(
//                           content:
//                               Text('Quiz generation will be implemented soon!'),
//                         ),
//                       );
//                     },
//                     child: Text(
//                         'Generate Quiz (${selectedVocabs.length} selected)'),
//                   ),
//                 ),
//             ],
//           );
//         },
//       ),
//     );
//   }

//   List<VocabCard> _sortVocabulary(List<VocabCard> vocabs, SortType sortType) {
//     switch (sortType) {
//       case SortType.alphabetical:
//         return List.from(vocabs)..sort((a, b) => a.word.compareTo(b.word));
//       case SortType.level:
//         return List.from(vocabs)..sort((a, b) => a.level.compareTo(b.level));
//       case SortType.type:
//         return List.from(vocabs)
//           ..sort((a, b) =>
//               (a.type.firstOrNull ?? '').compareTo(b.type.firstOrNull ?? ''));
//       case SortType.mastery:
//         return List.from(vocabs)
//           ..sort((a, b) => b.masteryLevel.compareTo(a.masteryLevel));
//     }
//   }
// }

// // VocabPreviewCard widget
// class VocabPreviewCard extends StatelessWidget {
//   final VocabCard vocab;
//   final ScrollController scrollController;

//   const VocabPreviewCard({
//     super.key,
//     required this.vocab,
//     required this.scrollController,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       child: ListView(
//         controller: scrollController,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 vocab.word,
//                 style: Theme.of(context).textTheme.headlineMedium,
//               ),
//               IconButton(
//                 icon: const Icon(Icons.close),
//                 onPressed: () => Navigator.pop(context),
//               ),
//             ],
//           ),
//           const SizedBox(height: 8),
//           Text(
//             vocab.pronunciation,
//             style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                   fontStyle: FontStyle.italic,
//                 ),
//           ),
//           const SizedBox(height: 16),
//           Text(
//             'Definition',
//             style: Theme.of(context).textTheme.titleMedium,
//           ),
//           Text(vocab.definition),
//           const SizedBox(height: 16),
//           Text(
//             'Examples',
//             style: Theme.of(context).textTheme.titleMedium,
//           ),
//           ...vocab.examples.map((example) => Padding(
//                 padding: const EdgeInsets.only(bottom: 8),
//                 child: Text('• $example'),
//               )),
//           const SizedBox(height: 16),
//           Row(
//             children: [
//               Chip(label: Text(vocab.level)),
//               const SizedBox(width: 8),
//               ...vocab.type.map((t) => Padding(
//                     padding: const EdgeInsets.only(right: 8),
//                     child: Chip(label: Text(t)),
//                   )),
//             ],
//           ),
//           const SizedBox(height: 16),
//           LinearProgressIndicator(
//             value: vocab.masteryLevel / 10,
//             backgroundColor: AppColors.grey200,
//           ),
//           const SizedBox(height: 8),
//           Text(
//             'Mastery Level: ${vocab.masteryLevel}/10',
//             textAlign: TextAlign.center,
//           ),
//         ],
//       ),
//     );
//   }
// }

// // Provider for the list of vocabulary cards
// final deckCardsProvider = StreamProvider<List<VocabCard>>((ref) {
//   return FirebaseService().watchVocabulary();
// });

// // Provider to track the current view mode (list or grid)
// final viewModeProvider = StateProvider<ViewMode>((ref) => ViewMode.list);

// // Provider to track the current sort method
// final sortModeProvider =
//     StateProvider<SortMode>((ref) => SortMode.alphabetical);

// enum ViewMode { list, grid }

// enum SortMode { alphabetical, level, recent, mastery }

// class DeckViewScreen extends ConsumerStatefulWidget {
//   const DeckViewScreen({super.key});

//   @override
//   ConsumerState<DeckViewScreen> createState() => _DeckViewScreenState();
// }

// class _DeckViewScreenState extends ConsumerState<DeckViewScreen> {
//   final PageController _cardPageController = PageController();
//   bool _isCardViewOpen = false;
//   int _currentCardIndex = 0;

//   @override
//   void dispose() {
//     _cardPageController.dispose();
//     super.dispose();
//   }

//   void _openCardView(int index, List<VocabCard> cards) {
//     setState(() {
//       _isCardViewOpen = true;
//       _currentCardIndex = index;
//     });

//     // Use Future.delayed to ensure the PageView is built before animating
//     Future.delayed(Duration.zero, () {
//       _cardPageController.jumpToPage(index);
//     });
//   }

//   void _closeCardView() {
//     setState(() {
//       _isCardViewOpen = false;
//     });
//   }

//   List<VocabCard> _sortCards(List<VocabCard> cards, SortMode sortMode) {
//     final sortedCards = List<VocabCard>.from(cards);
//     switch (sortMode) {
//       case SortMode.alphabetical:
//         sortedCards.sort((a, b) => a.word.compareTo(b.word));
//         break;
//       case SortMode.level:
//         sortedCards.sort((a, b) => a.level.compareTo(b.level));
//         break;
//       case SortMode.mastery:
//         sortedCards.sort((a, b) => b.masteryLevel.compareTo(a.masteryLevel));
//         break;
//       case SortMode.recent:
//         // This would require a timestamp field in the VocabCard model
//         // For now, we'll just use the existing order
//         break;
//     }
//     return sortedCards;
//   }

//   @override
//   Widget build(BuildContext context) {
//     final viewMode = ref.watch(viewModeProvider);
//     final sortMode = ref.watch(sortModeProvider);
//     final cardsAsync = ref.watch(deckCardsProvider);

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('My Deck'),
//         actions: [
//           _isCardViewOpen
//               ? IconButton(
//                   icon: const Icon(Icons.close),
//                   onPressed: _closeCardView,
//                 )
//               : Row(
//                   children: [
//                     // Sort button
//                     PopupMenuButton<SortMode>(
//                       icon: const Icon(Icons.sort),
//                       onSelected: (SortMode mode) {
//                         ref.read(sortModeProvider.notifier).state = mode;
//                       },
//                       itemBuilder: (context) => [
//                         const PopupMenuItem(
//                           value: SortMode.alphabetical,
//                           child: Text('Alphabetical'),
//                         ),
//                         const PopupMenuItem(
//                           value: SortMode.level,
//                           child: Text('By Level'),
//                         ),
//                         const PopupMenuItem(
//                           value: SortMode.mastery,
//                           child: Text('By Mastery'),
//                         ),
//                         const PopupMenuItem(
//                           value: SortMode.recent,
//                           child: Text('Most Recent'),
//                         ),
//                       ],
//                     ),
//                     // View mode toggle
//                     IconButton(
//                       icon: Icon(viewMode == ViewMode.list
//                           ? Icons.grid_view
//                           : Icons.list),
//                       onPressed: () {
//                         ref.read(viewModeProvider.notifier).state =
//                             viewMode == ViewMode.list
//                                 ? ViewMode.grid
//                                 : ViewMode.list;
//                       },
//                     ),
//                   ],
//                 ),
//         ],
//       ),
//       body: cardsAsync.when(
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stack) => Center(
//           child: Text('Error loading deck: $error'),
//         ),
//         data: (cards) {
//           if (cards.isEmpty) {
//             return const Center(
//               child: Text(
//                 'No cards in your deck yet.\nAdd some vocabulary to get started!',
//                 textAlign: TextAlign.center,
//               ),
//             );
//           }

//           final sortedCards = _sortCards(cards, sortMode);

//           if (_isCardViewOpen) {
//             return _buildCardCarousel(sortedCards);
//           } else {
//             return viewMode == ViewMode.list
//                 ? _buildListView(sortedCards)
//                 : _buildGridView(sortedCards);
//           }
//         },
//       ),
//     );
//   }

//   Widget _buildListView(List<VocabCard> cards) {
//     return ListView.builder(
//       itemCount: cards.length,
//       itemBuilder: (context, index) {
//         final card = cards[index];
//         return Card(
//           margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//           elevation: 2,
//           child: ListTile(
//             title: Text(
//               card.word,
//               style: const TextStyle(fontWeight: FontWeight.bold),
//             ),
//             subtitle: Text('${card.level} • ${card.type.join(", ")}'),
//             trailing: _getLevelIndicator(card.level),
//             onTap: () => _openCardView(index, cards),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildGridView(List<VocabCard> cards) {
//     return GridView.builder(
//       padding: const EdgeInsets.all(16),
//       gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//         crossAxisCount: 2,
//         crossAxisSpacing: 12,
//         mainAxisSpacing: 12,
//         childAspectRatio: 0.75,
//       ),
//       itemCount: cards.length,
//       itemBuilder: (context, index) {
//         final card = cards[index];
//         return GestureDetector(
//           onTap: () => _openCardView(index, cards),
//           child: Card(
//             elevation: 3,
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(12),
//               side: BorderSide(
//                 color: _getLevelColor(card.level),
//                 width: 2,
//               ),
//             ),
//             child: Padding(
//               padding: const EdgeInsets.all(12.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Expanded(
//                         child: Text(
//                           card.word,
//                           style: const TextStyle(
//                             fontSize: 18,
//                             fontWeight: FontWeight.bold,
//                           ),
//                           overflow: TextOverflow.ellipsis,
//                         ),
//                       ),
//                       Container(
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 8,
//                           vertical: 4,
//                         ),
//                         decoration: BoxDecoration(
//                           color: _getLevelColor(card.level),
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         child: Text(
//                           card.level,
//                           style: TextStyle(
//                             color:
//                                 _getLevelColor(card.level).computeLuminance() >
//                                         0.5
//                                     ? AppColors.black
//                                     : AppColors.white,
//                             fontWeight: FontWeight.bold,
//                             fontSize: 12,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 8),
//                   Text(
//                     card.type.join(', '),
//                     style: TextStyle(
//                       color: AppColors.grey600,
//                       fontStyle: FontStyle.italic,
//                       fontSize: 12,
//                     ),
//                   ),
//                   const SizedBox(height: 12),
//                   Expanded(
//                     child: Text(
//                       card.definition,
//                       style: const TextStyle(fontSize: 14),
//                       maxLines: 5,
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   LinearProgressIndicator(
//                     value: card.masteryLevel / 10,
//                     backgroundColor: AppColors.grey200,
//                     valueColor: AlwaysStoppedAnimation<Color>(
//                       _getLevelColor(card.level),
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   Text(
//                     'Mastery: ${card.masteryLevel}/10',
//                     style: const TextStyle(fontSize: 12),
//                     textAlign: TextAlign.right,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildCardCarousel(List<VocabCard> cards) {
//     return Column(
//       children: [
//         Expanded(
//           child: PageView.builder(
//             controller: _cardPageController,
//             itemCount: cards.length,
//             onPageChanged: (index) {
//               setState(() {
//                 _currentCardIndex = index;
//               });
//             },
//             itemBuilder: (context, index) {
//               return _buildFullCardView(cards[index]);
//             },
//           ),
//         ),
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Text(
//                 '${_currentCardIndex + 1} of ${cards.length}',
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildFullCardView(VocabCard card) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16.0),
//       child: Card(
//         elevation: 4,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.all(20.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Word and Level
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Expanded(
//                     child: Text(
//                       card.word,
//                       style: const TextStyle(
//                         fontSize: 28,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                   ),
//                   Container(
//                     padding: const EdgeInsets.symmetric(
//                       horizontal: 12,
//                       vertical: 6,
//                     ),
//                     decoration: BoxDecoration(
//                       color: _getLevelColor(card.level),
//                       borderRadius: BorderRadius.circular(16),
//                     ),
//                     child: Text(
//                       card.level,
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         color:
//                             _getLevelColor(card.level).computeLuminance() > 0.5
//                                 ? AppColors.black
//                                 : AppColors.white,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 12),

//               // Pronunciation
//               Text(
//                 '/${card.pronunciation}/',
//                 style: TextStyle(
//                   fontSize: 18,
//                   fontStyle: FontStyle.italic,
//                   color: AppColors.grey600,
//                 ),
//               ),
//               const SizedBox(height: 16),

//               // Word Types
//               Wrap(
//                 spacing: 8,
//                 children: card.type
//                     .map((type) => Container(
//                           padding: const EdgeInsets.symmetric(
//                             horizontal: 10,
//                             vertical: 4,
//                           ),
//                           decoration: BoxDecoration(
//                             color: AppColors.grey200,
//                             borderRadius: BorderRadius.circular(8),
//                           ),
//                           child: Text(type),
//                         ))
//                     .toList(),
//               ),
//               const SizedBox(height: 24),

//               // Definition
//               const Text(
//                 'Definition',
//                 style: TextStyle(
//                   fontSize: 20,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 card.definition,
//                 style: const TextStyle(
//                   fontSize: 16,
//                   height: 1.5,
//                 ),
//               ),
//               const SizedBox(height: 24),

//               // Examples
//               const Text(
//                 'Examples',
//                 style: TextStyle(
//                   fontSize: 20,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 12),
//               ...card.examples.map((example) => Padding(
//                     padding: const EdgeInsets.only(bottom: 12),
//                     child: Row(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         const Text(
//                           '•',
//                           style: TextStyle(
//                             fontSize: 18,
//                             height: 1.5,
//                           ),
//                         ),
//                         const SizedBox(width: 8),
//                         Expanded(
//                           child: Text(
//                             example,
//                             style: const TextStyle(
//                               fontSize: 16,
//                               height: 1.5,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   )),
//               const SizedBox(height: 24),

//               // Mastery Level
//               const Text(
//                 'Mastery Level',
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 8),
//               LinearProgressIndicator(
//                 value: card.masteryLevel / 10,
//                 backgroundColor: AppColors.grey200,
//                 valueColor: AlwaysStoppedAnimation<Color>(
//                   _getLevelColor(card.level),
//                 ),
//                 minHeight: 10,
//                 borderRadius: BorderRadius.circular(5),
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 '${card.masteryLevel}/10',
//                 style: const TextStyle(
//                   fontSize: 14,
//                   fontWeight: FontWeight.bold,
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _getLevelIndicator(String level) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: _getLevelColor(level),
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Text(
//         level,
//         style: TextStyle(
//           color: _getLevelColor(level).computeLuminance() > 0.5
//               ? AppColors.black
//               : AppColors.white,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }

//   Color _getLevelColor(String level) {
//     switch (level) {
//       case 'A1':
//         return const Color(0xFFDCFCE7); // Light green
//       case 'A2':
//         return const Color(0xFF86EFAC); // Green
//       case 'B1':
//         return const Color(0xFFDBEAFE); // Light blue
//       case 'B2':
//         return const Color(0xFF93C5FD); // Blue
//       case 'C1':
//         return const Color(0xFFF3E8FF); // Light purple
//       case 'C2':
//         return const Color(0xFFD8B4FE); // Purple
//       default:
//         return const Color(0xFFE5E7EB); // Gray
//     }
//   }
// }
