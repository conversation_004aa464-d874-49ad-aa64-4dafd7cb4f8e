import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';

/// Utility class for sorting vocabulary cards
class VocabSorting {
  /// Sort vocabulary cards based on the specified sort type
  static List<VocabCard> sortVocabulary(
      List<VocabCard> vocabs, VocabSortType sortType) {
    final sortedList = List<VocabCard>.from(vocabs);

    switch (sortType) {
      case VocabSortType.alphabetical:
        sortedList.sort((a, b) => a.word.compareTo(b.word));
        break;

      case VocabSortType.level:
        // Sort by CEFR level (A1, A2, B1, B2, C1, C2)
        sortedList.sort((a, b) {
          // Handle empty levels
          if (a.level.isEmpty) return 1;
          if (b.level.isEmpty) return -1;

          // First compare by the letter (A, B, C)
          int letterComparison = a.level[0].compareTo(b.level[0]);
          if (letterComparison != 0) return letterComparison;

          // Then compare by the number if letters are the same
          if (a.level.length > 1 && b.level.length > 1) {
            return int.parse(a.level[1]).compareTo(int.parse(b.level[1]));
          }
          return 0;
        });
        break;

      case VocabSortType.type:
        // Sort by first word type
        sortedList.sort((a, b) {
          final aType = a.type.isNotEmpty ? a.type[0] : '';
          final bType = b.type.isNotEmpty ? b.type[0] : '';
          return aType.compareTo(bType);
        });
        break;

      case VocabSortType.mastery:
        // Sort by mastery level (descending)
        sortedList.sort((a, b) => b.masteryLevel.compareTo(a.masteryLevel));
        break;
    }

    return sortedList;
  }

  /// Get the display name for a sort type
  static String getSortTypeName(VocabSortType sortType) {
    switch (sortType) {
      case VocabSortType.alphabetical:
        return 'Alphabetical';
      case VocabSortType.level:
        return 'By Level';
      case VocabSortType.type:
        return 'By Type';
      case VocabSortType.mastery:
        return 'By Mastery';
    }
  }
}
