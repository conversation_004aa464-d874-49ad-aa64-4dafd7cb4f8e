import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Utility class for handling level-related styling
class LevelUtils {
  /// Get the color associated with a CEFR level
  static Color getLevelColor(String level) {
    switch (level) {
      case 'A1':
        return AppColors.levelA1;
      case 'A2':
        return AppColors.levelA2;
      case 'B1':
        return AppColors.levelB1;
      case 'B2':
        return AppColors.levelB2;
      case 'C1':
        return AppColors.levelC1;
      case 'C2':
        return AppColors.levelC2;
      default:
        return AppColors.levelUnknown;
    }
  }

  /// Get a level indicator widget
  static Widget getLevelIndicator(String level) {
    final color = getLevelColor(level);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        level,
        style: TextStyle(
          color: color.computeLuminance() > 0.5
              ? AppColors.textLight
              : AppColors.textLight,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get the description for a level
  static String getLevelDescription(String level) {
    switch (level) {
      case 'A1':
        return 'Beginner';
      case 'A2':
        return 'Elementary';
      case 'B1':
        return 'Intermediate';
      case 'B2':
        return 'Upper Intermediate';
      case 'C1':
        return 'Advanced';
      case 'C2':
        return 'Proficient';
      default:
        return 'Unknown';
    }
  }

  /// Get a tooltip with level information
  static String getLevelTooltip(String level) {
    return '$level - ${getLevelDescription(level)}';
  }
}
