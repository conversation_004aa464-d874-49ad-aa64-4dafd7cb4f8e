// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';

// import 'package:vocadex/src/features/vocabcard/model_vocabcard.dart';

// import 'package:vocadex/src/features/vocabulary_deck/providers/vocab_providers.dart';
// import 'package:vocadex/src/features/vocabulary_deck/utils/vocab_sorting.dart';
// import 'package:vocadex/src/features/vocabulary_deck/widgets/vocab_detail_view.dart';

// /// A simpler screen that displays vocabulary cards in a list
// /// with selection capability (for creating quizzes, etc.)
// // class VocabListScreen extends ConsumerWidget {
// //   /// Constructor
// //   const VocabListScreen({super.key});

// //   void _showVocabDetail(BuildContext context, VocabCard vocab) {
// //     // Show the vocabulary detail modal
// //     VocabDetailModal.show(context, vocab);
// //   }

// //   @override
// //   Widget build(BuildContext context, WidgetRef ref) {
// //     final vocabsAsync = ref.watch(vocabListStreamProvider);
// //     final selectedVocabs = ref.watch(selectedVocabsProvider);
// //     final sortType = ref.watch(vocabSortTypeProvider);

// //     return Scaffold(
// //       appBar: AppBar(
// //         title: const Text('My Vocabulary'),
// //         actions: [
// //           PopupMenuButton<VocabSortType>(
// //             icon: const Icon(Icons.sort),
// //             onSelected: (VocabSortType value) {
// //               ref.read(vocabSortTypeProvider.notifier).state = value;
// //             },
// //             itemBuilder: (BuildContext context) => VocabSortType.values
// //                 .map((type) => PopupMenuItem<VocabSortType>(
// //                       value: type,
// //                       child: Text(VocabSorting.getSortTypeName(type)),
// //                     ))
// //                 .toList(),
// //           ),
// //         ],
// //       ),
// //       body: vocabsAsync.when(
// //         loading: () => const Center(child: CircularProgressIndicator()),
// //         error: (error, stackTrace) => Center(
// //           child: Column(
// //             mainAxisAlignment: MainAxisAlignment.center,
// //             children: [
// //               Text('Error: $error'),
// //               const SizedBox(height: 16),
// //               ElevatedButton(
// //                 onPressed: () {
// //                   ref.refresh(vocabListStreamProvider);
// //                 },
// //                 child: const Text('Retry'),
// //               ),
// //             ],
// //           ),
// //         ),
// //         data: (vocabs) {
// //           final sortedVocabs = VocabSorting.sortVocabulary(vocabs, sortType);

// //           if (sortedVocabs.isEmpty) {
// //             return const Center(
// //               child: Text('No vocabulary words yet. Add some to get started!'),
// //             );
// //           }

// //           return Column(
// //             children: [
// //               Expanded(
// //                 child: ListView.builder(
// //                   itemCount: sortedVocabs.length,
// //                   itemBuilder: (context, index) {
// //                     final vocab = sortedVocabs[index];
// //                     return ListTile(
// //                       title: Text(
// //                         vocab.word,
// //                         style: const TextStyle(fontWeight: FontWeight.bold),
// //                       ),
// //                       subtitle:
// //                           Text('${vocab.level} • ${vocab.type.join(", ")}'),
// //                       onTap: () => _showVocabDetail(context, vocab),
// //                       trailing: Checkbox(
// //                         value: selectedVocabs.contains(vocab.id),
// //                         onChanged: (bool? value) {
// //                           if (value == true) {
// //                             ref.read(selectedVocabsProvider.notifier).state = {
// //                               ...selectedVocabs,
// //                               vocab.id
// //                             };
// //                           } else {
// //                             final newSelected =
// //                                 Set<String>.from(selectedVocabs);
// //                             newSelected.remove(vocab.id);
// //                             ref.read(selectedVocabsProvider.notifier).state =
// //                                 newSelected;
// //                           }
// //                         },
// //                       ),
// //                     );
// //                   },
// //                 ),
// //               ),
// //               if (selectedVocabs.isNotEmpty)
// //                 Padding(
// //                   padding: const EdgeInsets.all(16.0),
// //                   child: ElevatedButton.icon(
// //                     onPressed: () {
// //                       ScaffoldMessenger.of(context).showSnackBar(
// //                         const SnackBar(
// //                           content:
// //                               Text('Quiz generation will be implemented soon!'),
// //                         ),
// //                       );
// //                     },
// //                     icon: const Icon(Icons.quiz),
// //                     label: Text(
// //                         'Generate Quiz (${selectedVocabs.length} selected)'),
// //                     style: ElevatedButton.styleFrom(
// //                       backgroundColor: Theme.of(context).primaryColor,
// //                       foregroundColor: AppColors.white,
// //                       padding: const EdgeInsets.symmetric(
// //                           horizontal: 16, vertical: 12),
// //                       shape: RoundedRectangleBorder(
// //                         borderRadius: BorderRadius.circular(8),
// //                       ),
// //                     ),
// //                   ),
// //                 ),
// //             ],
// //           );
// //         },
// //       ),
// //     );
// //   }
// // }
