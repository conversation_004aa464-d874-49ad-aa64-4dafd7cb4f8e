import 'package:flutter/material.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// A full-screen carousel view for vocabulary cards
class VocabCarouselScreen extends StatefulWidget {
  /// The list of vocabulary cards
  final List<VocabCard> cards;

  /// Initial index to display
  final int initialIndex;

  /// Hero tag for animation
  final String? heroTag;

  /// Constructor
  const VocabCarouselScreen({
    super.key,
    required this.cards,
    required this.initialIndex,
    this.heroTag,
  });

  @override
  State<VocabCarouselScreen> createState() => _VocabCarouselScreenState();
}

class _VocabCarouselScreenState extends State<VocabCarouselScreen> {
  late PageController _pageController;
  late int _currentIndex;
  late List<VocabCard> _cards;
  final FirebaseService _firebaseService = FirebaseService();
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _cards = List.from(widget.cards); // Create a mutable copy of the cards list
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: AppColors.transparent,
        elevation: 0,
        title: const Text(
          'My Deck',
          style: TextStyle(color: AppColors.black),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          // Delete button
          IconButton(
            icon: _isDeleting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.wild),
                    ),
                  )
                : const Icon(Icons.delete_outline, color: AppColors.wild),
            onPressed: _isDeleting ? null : _confirmDelete,
            tooltip: 'Delete card',
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Status bar height compensation
          SizedBox(height: MediaQuery.of(context).padding.top),
          SizedBox(height: 42),

          // Card carousel
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.cards.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final VocabCard card = _cards[index];
                final String heroTag = _currentIndex == widget.initialIndex &&
                        widget.heroTag != null
                    ? widget.heroTag!
                    : 'vocab-card-${card.id}-$index';

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Hero(
                    tag: heroTag,
                    child: VocabularyCard(
                      card: card,
                    ),
                  ),
                );
              },
            ),
          ),

          // Navigation indicator
          Padding(
            padding: const EdgeInsets.all(36.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${_currentIndex + 1} of ${_cards.length}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show a confirmation dialog before deleting the card
  Future<void> _confirmDelete() async {
    if (_cards.isEmpty || _currentIndex >= _cards.length) return;
    
    final VocabCard currentCard = _cards[_currentIndex];
    
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Card'),
        content: Text(
          'Are you sure you want to delete "${currentCard.word}" from your deck?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.wild,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirm == true && mounted) {
      await _deleteCard();
    }
  }

  /// Delete the current card
  Future<void> _deleteCard() async {
    if (_cards.isEmpty || _currentIndex >= _cards.length) return;
    
    final VocabCard cardToDelete = _cards[_currentIndex];
    
    setState(() {
      _isDeleting = true;
    });

    try {
      // Delete the card from Firebase
      await _firebaseService.deleteVocabCard(cardToDelete.id);
      
      // Show success message
      if (mounted) {
        showSuccessToast(
          context,
          title: 'Card Deleted',
          description: '"${cardToDelete.word}" has been removed from your deck.',
        );
      }

      // Update the UI
      setState(() {
        _isDeleting = false;
        
        // Remove the card from the local list
        _cards.removeAt(_currentIndex);
        
        // If there are no more cards, go back
        if (_cards.isEmpty) {
          Navigator.of(context).pop();
          return;
        }
        
        // Adjust the current index if needed
        if (_currentIndex >= _cards.length) {
          _currentIndex = _cards.length - 1;
        }
        
        // Jump to the new position without animation
        _pageController.jumpToPage(_currentIndex);
      });
    } catch (e) {
      // Show error message
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to delete card. Please try again.',
        );
      }
      
      setState(() {
        _isDeleting = false;
      });
    }
  }
}
