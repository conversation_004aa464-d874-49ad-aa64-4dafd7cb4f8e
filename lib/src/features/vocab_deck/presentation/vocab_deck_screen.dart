import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_deck/filter/filter_modal.dart';
import 'package:vocadex/src/features/vocab_deck/filter/provider_filter.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';

import 'package:vocadex/src/features/vocab_deck/utils/level_utils.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/filter/active_filters_bar.dart';
import 'package:vocadex/src/core/theme/constants/color_constants.dart';

class VocabDeckScreen extends ConsumerWidget {
  const VocabDeckScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewMode = ref.watch(vocabViewModeProvider);
    final filterOptions = ref.watch(vocabFilterOptionsProvider);
    final cardsAsync = ref.watch(vocabListStreamProvider);
    final filteredCards = ref.watch(filteredVocabCardsProvider);

    return GradientScaffold(
      // backgroundColor: AppColors.backgroundColorLight,
      appBar: AppBar(
        title: const Text('My Deck'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // View mode toggle
          IconButton(
            icon: Icon(
                viewMode == VocabViewMode.list ? Icons.grid_view : Icons.list),
            onPressed: () {
              ref.read(vocabViewModeProvider.notifier).state =
                  viewMode == VocabViewMode.list
                      ? VocabViewMode.grid
                      : VocabViewMode.list;
            },
          ),

          // Filter button with badge indicator if filters are active
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.filter_list),
                onPressed: () => showFixedFilterModal(context),
              ),
              if (countActiveFilters(filterOptions) > 0)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      countActiveFilters(filterOptions).toString(),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Show active filters bar if there are active filters
          ImprovedActiveFiltersBar(
            filterOptions: filterOptions,
            onUpdate: (updatedOptions) {
              ref.read(vocabFilterOptionsProvider.notifier).state =
                  updatedOptions;
            },
            onFilterTap: () => showFixedFilterModal(context),
          ),

          // Main content area
          Expanded(
            child: cardsAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error loading vocabulary: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(vocabListStreamProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
              data: (cards) {
                if (cards.isEmpty) {
                  return const Center(
                    child: Text(
                      'No vocabulary words yet.\nAdd some to get started!',
                      textAlign: TextAlign.center,
                    ),
                  );
                }

                // If filtered cards are empty but we have cards, show a message
                if (filteredCards.isEmpty && cards.isNotEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          'No cards match your filters',
                          style: TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => showFixedFilterModal(context),
                          icon: const Icon(Icons.filter_list),
                          label: const Text('Modify Filters'),
                        ),
                      ],
                    ),
                  );
                }

                return viewMode == VocabViewMode.list
                    ? _buildListView(context, filteredCards)
                    : _buildGridView(context, filteredCards);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _openCardView(BuildContext context, int index, List<VocabCard> cards) {
    // Navigate to a new screen for the carousel view
    context.pushNamed('vocab-card',
        pathParameters: {'id': index.toString()}, extra: cards);
  }

  Widget _buildListView(BuildContext context, List<VocabCard> cards) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SizedBox(
        height: cards.length * 100.0, // Adjust based on card height and overlap
        child: Stack(
          children: cards.asMap().entries.map((entry) {
            int index = entry.key;
            VocabCard card = entry.value;

            final levelColor = LevelUtils.getLevelColor(card.level);
            final double topOffset = index * 70; // Customize overlap spacing

            return Positioned(
              top: topOffset,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  color: levelColor,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.textLight.withAlpha(51),blurRadius: 6,
                      offset: const Offset(0, -3),
                    ),
                    BoxShadow(
                      color: AppColors.textLight.withAlpha(26),blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: InkWell(
                  borderRadius: const BorderRadius.all(
                    Radius.circular(24),
                  ),
                  onTap: () => _openCardView(context, index, cards),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        top: 16, bottom: 40, left: 32, right: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Hero(
                                tag: 'vocab_word_${card.id}',
                                child: Text(
                                  card.word.toLowerCase(),
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textLight,
                                  ),
                                ),
                              ),
                              if (card.type.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    card.type.join(', '),
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      color:
                                          AppColors.textLight.withAlpha(178),),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 38,
                              height: 38,
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  CircularProgressIndicator(
                                    value: card.masteryLevel / 10,
                                    backgroundColor:
                                        AppColors.textLight.withAlpha(61),valueColor: AlwaysStoppedAnimation<Color>(
                                      card.getMasteryStatusColor(),
                                    ),
                                    strokeWidth: 4,
                                  ),
                                  Text(
                                    '${card.masteryLevel}',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textLight,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildGridView(BuildContext context, List<VocabCard> cards) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.75, // Adjusted for the new design
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        final levelColor = LevelUtils.getLevelColor(card.level);

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: levelColor, width: 4),
            boxShadow: [
              BoxShadow(
                color: AppColors.textLight.withAlpha(76),spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: InkWell(
              onTap: () => _openCardView(context, index, cards),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header with level color and word
                  Container(
                    color: levelColor,
                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Frequency label

                        const SizedBox(height: 6),

                        // Word in large font (centered)
                        Center(
                          child: Hero(
                            tag: 'vocab_word_${card.id}',
                            child: Text(
                              card.word.toLowerCase(),
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textLight,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),

                        // Pronunciation (centered) if available
                        if (card.pronunciation.isNotEmpty)
                          Center(
                            child: Text(
                              card.pronunciation,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textLight,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Content area with light background
                  Expanded(
                    child: Container(
                      color: Colors.white,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Part of speech indicator
                          if (card.type.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.fromLTRB(12, 8, 12, 4),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade200,
                                  borderRadius: BorderRadius.circular(12),
                                  border:
                                      Border.all(color: AppColors.textLight),
                                ),
                                child: Text(
                                  card.type.first.toLowerCase(),
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppColors.textLight,
                                  ),
                                ),
                              ),
                            ),

                          // Definition section
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Orange vertical bar
                                  Container(
                                    width: 4,
                                    height: double.infinity,
                                    color: levelColor,
                                  ),

                                  const SizedBox(width: 8),

                                  // Definition text
                                  Expanded(
                                    child: Text(
                                      card.definition,
                                      style: const TextStyle(
                                        fontSize: 13,
                                        height: 1.3,
                                      ),
                                      maxLines: 5,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Mastery progress indicator
                          Padding(
                            padding: const EdgeInsets.fromLTRB(12, 4, 12, 12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: LinearProgressIndicator(
                                    value: card.masteryLevel / 10,
                                    backgroundColor: AppColors.textLight,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      card.getMasteryStatusColor(),
                                    ),
                                    minHeight: 6,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Mastery: ${card.masteryLevel}/10',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

Color darkenColor(Color color, [double amount = 0.1]) {
  assert(amount >= 0 && amount <= 1, 'Amount should be between 0 and 1.');

  // Convert to HSL for better darkening
  HSLColor hsl = HSLColor.fromColor(color);

  // Reduce the lightness by the amount
  return hsl
      .withLightness((hsl.lightness * (1.0 - amount)).clamp(0.0, 1.0))
      .toColor();
}
