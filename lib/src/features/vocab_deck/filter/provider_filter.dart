// lib/src/features/vocab_deck/providers/filter_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';

import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';

/// Provider for filter options
final vocabFilterOptionsProvider = StateProvider<VocabFilterOptions>((ref) {
  return VocabFilterOptions();
});

/// Provider that returns filtered vocabulary cards based on the current filters
final filteredVocabCardsProvider = Provider<List<VocabCard>>((ref) {
  final allCards = ref.watch(vocabListStreamProvider).valueOrNull ?? [];
  final filterOptions = ref.watch(vocabFilterOptionsProvider);

  // Start with all cards
  List<VocabCard> filteredCards = List.from(allCards);

  final selectedTypes = filterOptions.getSelectedWordTypes();
  if (selectedTypes.isNotEmpty) {
    // Convert selected types to lowercase for case-insensitive comparison
    final lowerSelectedTypes =
        selectedTypes.map((type) => type.toLowerCase()).toList();

    filteredCards = filteredCards.where((card) {
      // Check if the card has any of the selected word types, case-insensitive
      for (final type in card.type) {
        if (lowerSelectedTypes.contains(type.toLowerCase())) {
          return true; // Card matches if any of its types is in the selected types
        }
      }
      return false; // No type matches
    }).toList();
  }
  // Apply CEFR level filter if any levels are selected
  final selectedLevels = filterOptions.getSelectedCefrLevels();
  if (selectedLevels.isNotEmpty) {
    filteredCards = filteredCards.where((card) {
      return selectedLevels.contains(card.level);
    }).toList();
  }

  // Apply mastery level filter if any mastery levels are selected
  final selectedMasteryLevels = filterOptions.getSelectedMasteryLevels();
  if (selectedMasteryLevels.isNotEmpty) {
    filteredCards = filteredCards.where((card) {
      // Check if the card's mastery level falls within any of the selected categories
      for (final selectedLevel in selectedMasteryLevels) {
        if (selectedLevel <= 3 && card.masteryLevel <= 3) return true; // Wild
        if (selectedLevel <= 6 &&
            card.masteryLevel >= 4 &&
            card.masteryLevel <= 6) return true; // Tamed
        if (selectedLevel >= 7 && card.masteryLevel >= 7)
          return true; // Mastered
      }
      return false;
    }).toList();
  }

  // Apply frequency filter if any frequencies are selected
  final selectedFrequencies = filterOptions.getSelectedFrequencies();
  if (selectedFrequencies.isNotEmpty) {
    filteredCards = filteredCards.where((card) {
      return card.frequency != null &&
          selectedFrequencies.contains(card.frequency);
    }).toList();
  }

  // Sort the filtered cards based on active filter type and direction
  switch (filterOptions.activeFilterType) {
    case FilterType.alphabetical:
      filteredCards.sort((a, b) {
        final comparison = a.word.toLowerCase().compareTo(b.word.toLowerCase());
        return filterOptions.filterDirection == FilterDirection.ascending
            ? comparison
            : -comparison;
      });
      break;

    case FilterType.type:
      // Sort by first type (if available)
      filteredCards.sort((a, b) {
        final typeA = a.type.isNotEmpty ? a.type[0] : '';
        final typeB = b.type.isNotEmpty ? b.type[0] : '';
        final comparison = typeA.compareTo(typeB);

        return filterOptions.filterDirection == FilterDirection.ascending
            ? comparison
            : -comparison;
      });
      break;

    case FilterType.level:
      // Compare CEFR levels
      filteredCards.sort((a, b) {
        // Helper function to compare CEFR levels properly
        int compareCefr(String levelA, String levelB) {
          // Handle empty levels
          if (levelA.isEmpty) return 1;
          if (levelB.isEmpty) return -1;

          // Compare the letter (A, B, C)
          int letterComparison = levelA[0].compareTo(levelB[0]);
          if (letterComparison != 0) return letterComparison;

          // Compare the number if letters are the same and there's a second character
          if (levelA.length > 1 && levelB.length > 1) {
            return int.parse(levelA[1]).compareTo(int.parse(levelB[1]));
          }
          return 0;
        }

        final comparison = compareCefr(a.level, b.level);
        return filterOptions.filterDirection == FilterDirection.ascending
            ? comparison
            : -comparison;
      });
      break;

    case FilterType.mastery:
      // Sort by mastery level
      filteredCards.sort((a, b) {
        final comparison = a.masteryLevel.compareTo(b.masteryLevel);
        return filterOptions.filterDirection == FilterDirection.ascending
            ? comparison
            : -comparison;
      });
      break;

    case FilterType.frequency:
      // Sort by frequency (nulls last)
      filteredCards.sort((a, b) {
        int freqA = a.frequency ?? 0;
        int freqB = b.frequency ?? 0;
        final comparison = freqA.compareTo(freqB);
        return filterOptions.filterDirection == FilterDirection.ascending
            ? comparison
            : -comparison;
      });
      break;
  }

  return filteredCards;
});
