// lib/src/features/vocab_deck/filter/filter_option.dart
import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';

class FilterOptions extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;
  final double size;
  final Color? selectedColor;
  final bool showTextBelow;

  const FilterOptions({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.size = 80.0,
    this.selectedColor,
    this.showTextBelow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Define colors based on selection state
    final borderColor = isSelected
        ? (selectedColor ?? theme.colorScheme.primary)
        : theme.colorScheme.outline.withAlpha(26);
    final fillColor = isSelected
        ? (selectedColor ?? theme.colorScheme.primary).withAlpha(26)
        : theme.colorScheme.surface;

    final textColor = isSelected
        ? (selectedColor ?? theme.colorScheme.primary)
        : theme.colorScheme.onSurface;

    if (showTextBelow) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Square with icon or first letter
          _buildSquare(context, borderColor, fillColor, textColor),

          // Label below
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    } else {
      // Just the square with text inside (for smaller options)
      return _buildSquare(context, borderColor, fillColor, textColor,
          includeText: true);
    }
  }

  Widget _buildSquare(
      BuildContext context, Color borderColor, Color fillColor, Color textColor,
      {bool includeText = false}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizing.radiusM),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: fillColor,
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
          border: Border.all(
            color: borderColor,
            width: 2, // Thicker border when selected
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // For icon or first letter of label
              if (icon != null) ...[
                Icon(
                  icon,
                  color: textColor,
                  size: size * 0.4, // Scale icon to fit square
                ),
              ] else ...[
                Text(
                  label.isNotEmpty ? label[0] : '',
                  style: TextStyle(
                    fontSize: size * 0.4,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                  ),
                ),
              ],

              // Include text inside only if requested
              if (includeText) ...[
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    color: textColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// A horizontally scrollable row of filter options
class FilterOptionsScrollRow extends StatelessWidget {
  final List<FilterOptionModel> options;
  final double optionSize;
  final String? title;
  final bool showTextBelow;

  const FilterOptionsScrollRow({
    super.key,
    required this.options,
    this.optionSize = 80.0,
    this.title,
    this.showTextBelow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppSizing.spaceS),
        ],
        SizedBox(
          height: showTextBelow
              ? optionSize + 30
              : optionSize, // Extra height for text below
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: options.length,
            separatorBuilder: (context, index) =>
                const SizedBox(width: AppSizing.spaceS),
            itemBuilder: (context, index) {
              final option = options[index];
              return FilterOptions(
                label: option.label,
                isSelected: option.isSelected,
                onTap: option.onTap,
                icon: option.icon,
                size: optionSize,
                selectedColor: option.selectedColor,
                showTextBelow: showTextBelow,
              );
            },
          ),
        ),
      ],
    );
  }
}

/// A grid of filter options in horizontally scrollable rows
class FilterOptionsGrid extends StatelessWidget {
  final List<FilterOptionModel> options;
  final int itemsPerRow;
  final double optionSize;
  final String? title;
  final bool showTextBelow;

  const FilterOptionsGrid({
    super.key,
    required this.options,
    this.itemsPerRow = 4,
    this.optionSize = 80.0,
    this.title,
    this.showTextBelow = true,
  });

  @override
  Widget build(BuildContext context) {
    // Split options into rows of itemsPerRow items
    final List<List<FilterOptionModel>> rows = [];
    for (int i = 0; i < options.length; i += itemsPerRow) {
      final end = (i + itemsPerRow <= options.length)
          ? i + itemsPerRow
          : options.length;
      rows.add(options.sublist(i, end));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppSizing.spaceS),
        ],
        ...rows.map((rowOptions) {
          return Padding(
            padding: const EdgeInsets.only(bottom: AppSizing.spaceS),
            child: FilterOptionsScrollRow(
              options: rowOptions,
              optionSize: optionSize,
              showTextBelow: showTextBelow,
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Data class for filter options
class FilterOptionModel {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;
  final Color? selectedColor;

  FilterOptionModel({
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
    this.selectedColor,
  });
}
