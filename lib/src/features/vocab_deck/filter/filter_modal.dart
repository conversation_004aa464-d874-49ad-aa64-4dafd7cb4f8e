import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_deck/filter/active_filters_bar.dart';
import 'package:vocadex/src/features/vocab_deck/filter/filter_options_square.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';
import 'package:vocadex/src/features/vocab_deck/filter/provider_filter.dart';

import 'package:vocadex/src/features/vocab_deck/utils/level_utils.dart';

class FilterModal extends ConsumerWidget {
  const FilterModal({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterOptions = ref.watch(vocabFilterOptionsProvider);
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.only(
        top: AppSizing.spaceL,
        left: AppSizing.spaceM,
        right: AppSizing.spaceM,
        bottom: AppSizing.spaceL,
      ),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppSizing.radiusL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter your vocabulary',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 20),
                style: IconButton.styleFrom(
                  backgroundColor:
                      theme.colorScheme.surfaceVariant.withAlpha(128),shape: const CircleBorder(),
                  padding: const EdgeInsets.all(8),
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),

          const SizedBox(height: AppSizing.spaceL),

          // Expanded content area
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main Filter Options (larger)
                  Text(
                    'Sort by',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: AppSizing.spaceM),

                  // Main filter options in a horizontal scrollable row
                  FilterOptionsScrollRow(
                    optionSize: 100, // Larger size for main filters
                    options: [
                      FilterOptionModel(
                        label: 'A-Z',
                        icon: Icons.sort_by_alpha,
                        isSelected: filterOptions.activeFilterType ==
                            FilterType.alphabetical,
                        onTap: () {
                          ref.read(vocabFilterOptionsProvider.notifier).update(
                                (state) => state.copyWith(
                                    activeFilterType: FilterType.alphabetical),
                              );
                        },
                      ),
                      FilterOptionModel(
                        label: 'Word Type',
                        icon: Icons.category,
                        isSelected:
                            filterOptions.activeFilterType == FilterType.type,
                        onTap: () {
                          ref.read(vocabFilterOptionsProvider.notifier).update(
                                (state) => state.copyWith(
                                    activeFilterType: FilterType.type),
                              );
                        },
                      ),
                      FilterOptionModel(
                        label: 'CEFR Level',
                        icon: Icons.school,
                        isSelected:
                            filterOptions.activeFilterType == FilterType.level,
                        onTap: () {
                          ref.read(vocabFilterOptionsProvider.notifier).update(
                                (state) => state.copyWith(
                                    activeFilterType: FilterType.level),
                              );
                        },
                      ),
                      FilterOptionModel(
                        label: 'Mastery',
                        icon: Icons.star,
                        isSelected: filterOptions.activeFilterType ==
                            FilterType.mastery,
                        onTap: () {
                          ref.read(vocabFilterOptionsProvider.notifier).update(
                                (state) => state.copyWith(
                                    activeFilterType: FilterType.mastery),
                              );
                        },
                      ),
                      FilterOptionModel(
                        label: 'Frequency',
                        icon: Icons.signal_cellular_alt,
                        isSelected: filterOptions.activeFilterType ==
                            FilterType.frequency,
                        onTap: () {
                          ref.read(vocabFilterOptionsProvider.notifier).update(
                                (state) => state.copyWith(
                                    activeFilterType: FilterType.frequency),
                              );
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: AppSizing.spaceL),
                  const Divider(),
                  const SizedBox(height: AppSizing.spaceM),

                  // Show subfilters based on the selected main filter
                  _buildSubfiltersForSelectedType(context, ref, filterOptions),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppSizing.spaceL),

          // Action buttons
          Row(
            children: [
              // Clear button
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.resetFilters(),
                        );
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppSizing.spaceM,
                    ),
                    side: BorderSide(color: theme.colorScheme.outline),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppSizing.radiusM),
                    ),
                  ),
                  child: const Text('Clear'),
                ),
              ),
              const SizedBox(width: AppSizing.spaceM),
              // Apply button
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      vertical: AppSizing.spaceM,
                    ),
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppSizing.radiusM),
                    ),
                  ),
                  icon: const Icon(Icons.filter_list, size: 18),
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('Filter'),
                      if (countActiveFilters(filterOptions) > 0) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onPrimary.withAlpha(51),shape: BoxShape.circle,
                          ),
                          child: Text(
                            countActiveFilters(filterOptions).toString(),
                            style: TextStyle(
                              color: theme.colorScheme.onPrimary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build the appropriate subfilters based on the selected main filter
  Widget _buildSubfiltersForSelectedType(
      BuildContext context, WidgetRef ref, VocabFilterOptions filterOptions) {
    final theme = Theme.of(context);

    switch (filterOptions.activeFilterType) {
      case FilterType.alphabetical:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: AppSizing.spaceS),
            FilterOptionsScrollRow(
              optionSize: 70, // Smaller size for subfilters
              options: [
                FilterOptionModel(
                  label: 'Ascending',
                  icon: Icons.arrow_upward,
                  isSelected: filterOptions.filterDirection ==
                      FilterDirection.ascending,
                  onTap: () {
                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.copyWith(
                              filterDirection: FilterDirection.ascending),
                        );
                  },
                ),
                FilterOptionModel(
                  label: 'Descending',
                  icon: Icons.arrow_downward,
                  isSelected: filterOptions.filterDirection ==
                      FilterDirection.descending,
                  onTap: () {
                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.copyWith(
                              filterDirection: FilterDirection.descending),
                        );
                  },
                ),
              ],
            ),
          ],
        );

      case FilterType.type:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Word Types',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: AppSizing.spaceS),
            FilterOptionsScrollRow(
              optionSize: 90,
              options: filterOptions.wordTypes.map((typeFilter) {
                return FilterOptionModel(
                  label: typeFilter.type,
                  isSelected: typeFilter.isSelected,
                  onTap: () {
                    // Create a new list with the toggled selection
                    final updatedTypes = filterOptions.wordTypes.map((t) {
                      if (t.type.toLowerCase() ==
                          typeFilter.type.toLowerCase()) {
                        return WordTypeFilter(
                          type: t.type,
                          isSelected: !t.isSelected,
                        );
                      }
                      return t;
                    }).toList();

                    // Update the filter options
                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.copyWith(wordTypes: updatedTypes),
                        );
                  },
                );
              }).toList(),
            ),
          ],
        );

      case FilterType.level:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'CEFR Levels',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: AppSizing.spaceS),
            FilterOptionsScrollRow(
              optionSize: 90,
              options: filterOptions.cefrLevels.map((levelFilter) {
                final levelColor = LevelUtils.getLevelColor(levelFilter.level);

                return FilterOptionModel(
                  label: levelFilter.level,
                  isSelected: levelFilter.isSelected,
                  selectedColor: levelColor,
                  onTap: () {
                    final updatedLevels = filterOptions.cefrLevels.map((l) {
                      if (l.level == levelFilter.level) {
                        return LevelFilter(
                          level: l.level,
                          isSelected: !l.isSelected,
                        );
                      }
                      return l;
                    }).toList();

                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.copyWith(cefrLevels: updatedLevels),
                        );
                  },
                );
              }).toList(),
            ),
          ],
        );

      case FilterType.mastery:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Mastery Levels',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: AppSizing.spaceS),
            FilterOptionsGrid(
              options: filterOptions.masteryLevels.map((masteryFilter) {
                Color masteryColor;
                // Determine color based on mastery category
                if (masteryFilter.level <= 3) {
                  masteryColor = AppColors.warningLight; // Wild
                } else if (masteryFilter.level <= 6) {
                  masteryColor = AppColors.successLight; // Tamed
                } else {
                  masteryColor = AppColors.primaryLight; // Mastered
                }

                return FilterOptionModel(
                  label: masteryFilter.name,
                  isSelected: masteryFilter.isSelected,
                  selectedColor: masteryColor,
                  onTap: () {
                    final updatedLevels = filterOptions.masteryLevels.map((m) {
                      if (m.level == masteryFilter.level) {
                        return MasteryFilter(
                          level: m.level,
                          name: m.name,
                          isSelected: !m.isSelected,
                        );
                      }
                      return m;
                    }).toList();

                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) =>
                              state.copyWith(masteryLevels: updatedLevels),
                        );
                  },
                );
              }).toList(),
              optionSize: 90, // Slightly larger size for better visibility
              itemsPerRow: 3,
            ),
          ],
        );

      case FilterType.frequency:
        // Use same icon but different color intensity for each frequency level
        final List<Color> frequencyColors = [
          AppColors.textLight, // 1 (rarest)
          AppColors.infoLight.withAlpha(128),// 2
          AppColors.infoLight.withAlpha(178),// 3
          AppColors.warningLight.withAlpha(178),// 4
          AppColors.successLight.withAlpha(178),// 5 (most common)
        ];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Word Frequency',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: AppSizing.spaceS),
            FilterOptionsScrollRow(
              optionSize: 90,
              showTextBelow: false,
              options: filterOptions.frequencyFilters.map((freqFilter) {
                final color =
                    frequencyColors[(freqFilter.frequency - 1).clamp(0, 4)];
                return FilterOptionModel(
                  label: freqFilter.frequency.toString(),
                  icon: Icons.signal_cellular_alt,
                  isSelected: freqFilter.isSelected,
                  selectedColor: color,
                  onTap: () {
                    final updatedFrequencies =
                        filterOptions.frequencyFilters.map((f) {
                      if (f.frequency == freqFilter.frequency) {
                        return FrequencyFilter(
                            frequency: f.frequency, isSelected: !f.isSelected);
                      }
                      return f;
                    }).toList();
                    ref.read(vocabFilterOptionsProvider.notifier).update(
                          (state) => state.copyWith(
                              frequencyFilters: updatedFrequencies),
                        );
                  },
                );
              }).toList(),
            ),
          ],
        );
    }
  }
}

// Helper method to show the fixed filter modal
void showFixedFilterModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.65,
      maxChildSize: 0.85,
      minChildSize: 0.45,
      expand: false,
      builder: (context, scrollController) {
        return const FilterModal();
      },
    ),
  );
}
