// // lib/src/features/vocab_deck/widgets/active_filter_chip.dart
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
// import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';

// class ActiveFilterChip extends StatelessWidget {
//   final String label;
//   final VoidCallback onRemove;

//   const ActiveFilterChip({
//     super.key,
//     required this.label,
//     required this.onRemove,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);

//     return Container(
//       margin: const EdgeInsets.only(right: AppSizing.spaceXS),
//       padding: const EdgeInsets.symmetric(
//         horizontal: AppSizing.spaceXS,
//         vertical: 2,
//       ),
//       decoration: BoxDecoration(
//         color: theme.colorScheme.primary.withAlpha(26),//         borderRadius: BorderRadius.circular(AppSizing.radiusS),
//         border: Border.all(
//           color: theme.colorScheme.primary.withAlpha(76),//           width: 1,
//         ),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Text(
//             label,
//             style: TextStyle(
//               fontSize: 12,
//               color: theme.colorScheme.primary,
//             ),
//           ),
//           const SizedBox(width: 2),
//           InkWell(
//             onTap: onRemove,
//             customBorder: const CircleBorder(),
//             child: Padding(
//               padding: const EdgeInsets.all(2),
//               child: Icon(
//                 Icons.close,
//                 size: 14,
//                 color: theme.colorScheme.primary,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// /// Widget to show the summary of active filters
// class ActiveFiltersSummary extends ConsumerWidget {
//   final VocabFilterOptions filterOptions;
//   final ValueChanged<VocabFilterOptions> onUpdate;

//   const ActiveFiltersSummary({
//     super.key,
//     required this.filterOptions,
//     required this.onUpdate,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final theme = Theme.of(context);

//     // Build lists of active filters
//     final List<Widget> activeFilterChips = [];

//     // Add sort direction
//     final directionLabel =
//         filterOptions.filterDirection == FilterDirection.ascending
//             ? 'Ascending'
//             : 'Descending';

//     activeFilterChips.add(
//       Chip(
//         label: Text(
//           'Sort: ${_getSortTypeLabel(filterOptions.activeFilterType)} ($directionLabel)',
//           style: TextStyle(
//             fontSize: 12,
//             color: theme.colorScheme.onSurface,
//           ),
//         ),
//         backgroundColor: theme.colorScheme.surfaceVariant,
//       ),
//     );

//     // Add word type filters
//     for (final typeFilter
//         in filterOptions.wordTypes.where((t) => t.isSelected)) {
//       activeFilterChips.add(
//         ActiveFilterChip(
//           label: 'Type: ${typeFilter.type}',
//           onRemove: () {
//             final updatedTypes = filterOptions.wordTypes.map((t) {
//               if (t.type == typeFilter.type) {
//                 return WordTypeFilter(type: t.type, isSelected: false);
//               }
//               return t;
//             }).toList();

//             onUpdate(filterOptions.copyWith(wordTypes: updatedTypes));
//           },
//         ),
//       );
//     }

//     // Add CEFR level filters
//     for (final levelFilter
//         in filterOptions.cefrLevels.where((l) => l.isSelected)) {
//       activeFilterChips.add(
//         ActiveFilterChip(
//           label: 'Level: ${levelFilter.level}',
//           onRemove: () {
//             final updatedLevels = filterOptions.cefrLevels.map((l) {
//               if (l.level == levelFilter.level) {
//                 return LevelFilter(level: l.level, isSelected: false);
//               }
//               return l;
//             }).toList();

//             onUpdate(filterOptions.copyWith(cefrLevels: updatedLevels));
//           },
//         ),
//       );
//     }

//     // Add mastery level filters
//     for (final masteryFilter
//         in filterOptions.masteryLevels.where((m) => m.isSelected)) {
//       activeFilterChips.add(
//         ActiveFilterChip(
//           label: 'Mastery: ${masteryFilter.name}',
//           onRemove: () {
//             final updatedLevels = filterOptions.masteryLevels.map((m) {
//               if (m.level == masteryFilter.level) {
//                 return MasteryFilter(
//                   level: m.level,
//                   name: m.name,
//                   isSelected: false,
//                 );
//               }
//               return m;
//             }).toList();

//             onUpdate(filterOptions.copyWith(masteryLevels: updatedLevels));
//           },
//         ),
//       );
//     }

//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       padding: const EdgeInsets.symmetric(
//         horizontal: AppSizing.spaceM,
//         vertical: AppSizing.spaceXS,
//       ),
//       child: Row(
//         children: activeFilterChips,
//       ),
//     );
//   }

//   String _getSortTypeLabel(FilterType filterType) {
//     switch (filterType) {
//       case FilterType.alphabetical:
//         return 'Alphabetical';
//       case FilterType.type:
//         return 'Type';
//       case FilterType.level:
//         return 'CEFR Level';
//       case FilterType.mastery:
//         return 'Mastery';
//     }
//   }
// }
