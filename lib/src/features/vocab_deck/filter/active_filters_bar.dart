// lib/src/features/vocab_deck/filter/active_filters_bar.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';

// lib/src/features/vocab_deck/filter/improved_active_filters.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';

// lib/src/features/vocab_deck/filter/improved_active_filters.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';

/// An improved component to display and manage active filters
class ImprovedActiveFiltersBar extends ConsumerWidget {
  final VocabFilterOptions filterOptions;
  final ValueChanged<VocabFilterOptions> onUpdate;
  final VoidCallback onFilterTap;

  const ImprovedActiveFiltersBar({
    super.key,
    required this.filterOptions,
    required this.onUpdate,
    required this.onFilterTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Generate list of active filter chips
    final List<Widget> activeFilters = [];

    // Add word type filters
    for (final typeFilter
        in filterOptions.wordTypes.where((t) => t.isSelected)) {
      activeFilters.add(
        _buildActiveFilterChip(
          context: context,
          label: typeFilter.type,
          category: 'Type',
          onRemove: () {
            final updatedTypes = filterOptions.wordTypes.map((t) {
              if (t.type == typeFilter.type) {
                return WordTypeFilter(type: t.type, isSelected: false);
              }
              return t;
            }).toList();

            onUpdate(filterOptions.copyWith(wordTypes: updatedTypes));
          },
        ),
      );
    }

    // Add CEFR level filters
    for (final levelFilter
        in filterOptions.cefrLevels.where((l) => l.isSelected)) {
      activeFilters.add(
        _buildActiveFilterChip(
          context: context,
          label: levelFilter.level,
          category: 'Level',
          onRemove: () {
            final updatedLevels = filterOptions.cefrLevels.map((l) {
              if (l.level == levelFilter.level) {
                return LevelFilter(level: l.level, isSelected: false);
              }
              return l;
            }).toList();

            onUpdate(filterOptions.copyWith(cefrLevels: updatedLevels));
          },
        ),
      );
    }

    // Add mastery level filters
    for (final masteryFilter
        in filterOptions.masteryLevels.where((m) => m.isSelected)) {
      activeFilters.add(
        _buildActiveFilterChip(
          context: context,
          label: masteryFilter.name,
          category: 'Mastery',
          onRemove: () {
            final updatedLevels = filterOptions.masteryLevels.map((m) {
              if (m.level == masteryFilter.level) {
                return MasteryFilter(
                  level: m.level,
                  name: m.name,
                  isSelected: false,
                );
              }
              return m;
            }).toList();

            onUpdate(filterOptions.copyWith(masteryLevels: updatedLevels));
          },
        ),
      );
    }

    // Add frequency filters
    for (final freqFilter
        in filterOptions.frequencyFilters.where((f) => f.isSelected)) {
      activeFilters.add(
        _buildActiveFilterChip(
          context: context,
          label: freqFilter.frequency.toString(),
          category: 'Frequency',
          onRemove: () {
            final updatedFrequencies = filterOptions.frequencyFilters.map((f) {
              if (f.frequency == freqFilter.frequency) {
                return FrequencyFilter(
                    frequency: f.frequency, isSelected: false);
              }
              return f;
            }).toList();
            onUpdate(
                filterOptions.copyWith(frequencyFilters: updatedFrequencies));
          },
        ),
      );
    }

    // Add sort direction if it's not the default (ascending)
    if (filterOptions.filterDirection == FilterDirection.descending) {
      activeFilters.add(
        _buildActiveFilterChip(
          context: context,
          label: 'Descending',
          category: 'Order',
          onRemove: () {
            onUpdate(filterOptions.copyWith(
              filterDirection: FilterDirection.ascending,
            ));
          },
        ),
      );
    }

    // If there are no active filters, return nothing
    if (activeFilters.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: AppSizing.spaceS),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withAlpha(26),width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withAlpha(13),blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter bar with horizontal scrolling
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: AppSizing.spaceM),
            child: Row(
              children: [
                // Filter button
                _buildFilterButton(context, onFilterTap, activeFilters.length),
                const SizedBox(width: AppSizing.spaceS),
                // Clear all button
                if (activeFilters.isNotEmpty)
                  TextButton(
                    onPressed: () => onUpdate(filterOptions.resetFilters()),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizing.spaceS,
                        vertical: 2,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Clear all',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(width: AppSizing.spaceS),
                // Active filter chips
                ...activeFilters,
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(
      BuildContext context, VoidCallback onTap, int filterCount) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizing.radiusS),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizing.spaceS,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary,
          borderRadius: BorderRadius.circular(AppSizing.radiusS),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.filter_list,
              size: 16,
              color: theme.colorScheme.onPrimary,
            ),
            const SizedBox(width: 4),
            Text(
              'Filters',
              style: TextStyle(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
            ),
            if (filterCount > 0) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onPrimary.withAlpha(51),shape: BoxShape.circle,
                ),
                child: Text(
                  filterCount.toString(),
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActiveFilterChip({
    required BuildContext context,
    required String label,
    required String category,
    required VoidCallback onRemove,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(right: AppSizing.spaceXS),
      child: InkWell(
        onTap: onRemove,
        borderRadius: BorderRadius.circular(AppSizing.radiusS),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSizing.spaceS,
            vertical: 6,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(AppSizing.radiusS),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(51),width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '$category: ',
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                Icons.close,
                size: 14,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Helper method to count active filters
int countActiveFilters(VocabFilterOptions filterOptions) {
  int count = 0;

  // Count selected word types
  count += filterOptions.wordTypes.where((t) => t.isSelected).length;

  // Count selected CEFR levels
  count += filterOptions.cefrLevels.where((l) => l.isSelected).length;

  // Count selected mastery levels
  count += filterOptions.masteryLevels.where((m) => m.isSelected).length;

  // Count selected frequency filters
  count += filterOptions.frequencyFilters.where((f) => f.isSelected).length;

  // Add one for non-default sort direction
  if (filterOptions.filterDirection == FilterDirection.descending) {
    count += 1;
  }

  return count;
}
