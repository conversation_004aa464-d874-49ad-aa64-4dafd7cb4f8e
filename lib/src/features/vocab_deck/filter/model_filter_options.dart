/// Enum for primary filter types
enum FilterType {
  alphabetical,
  type,
  level,
  mastery,
  frequency,
}

/// Enum for filter directions
enum FilterDirection {
  ascending,
  descending,
}

/// Model for word types
class WordTypeFilter {
  final String type;
  bool isSelected;

  WordTypeFilter({
    required this.type,
    this.isSelected = false,
  });
}

/// Model for CEFR levels
class LevelFilter {
  final String level;
  bool isSelected;

  LevelFilter({
    required this.level,
    this.isSelected = false,
  });
}

/// Model for mastery levels
class MasteryFilter {
  final int level;
  final String name;
  bool isSelected;

  MasteryFilter({
    required this.level,
    required this.name,
    this.isSelected = false,
  });
}

/// Model for frequency levels
class FrequencyFilter {
  final int frequency; // 1 to 5
  bool isSelected;

  FrequencyFilter({required this.frequency, this.isSelected = false});
}

/// Model representing the complete set of filter options
class VocabFilterOptions {
  // Primary filters
  final List<WordTypeFilter> wordTypes;
  final List<LevelFilter> cefrLevels;
  final List<MasteryFilter> masteryLevels;
  final List<FrequencyFilter> frequencyFilters;

  // Active filters
  FilterType activeFilterType;
  FilterDirection filterDirection;

  // Default word types
  static List<WordTypeFilter> defaultWordTypes() {
    return [
      WordTypeFilter(type: 'Noun'),
      WordTypeFilter(type: 'Verb'),
      WordTypeFilter(type: 'Adjective'),
      WordTypeFilter(type: 'Adverb'),
      WordTypeFilter(type: 'Pronoun'),
      WordTypeFilter(type: 'Preposition'),
      WordTypeFilter(type: 'Conjunction'),
      WordTypeFilter(type: 'Interjection'),
    ];
  }

  // Default CEFR levels
  static List<LevelFilter> defaultCefrLevels() {
    return [
      LevelFilter(level: 'A1'),
      LevelFilter(level: 'A2'),
      LevelFilter(level: 'B1'),
      LevelFilter(level: 'B2'),
      LevelFilter(level: 'C1'),
      LevelFilter(level: 'C2'),
    ];
  }

  // Default mastery levels
  static List<MasteryFilter> defaultMasteryLevels() {
    return [
      // Wild category (levels 1-3)
      MasteryFilter(level: 1, name: 'Wild'),
      // Tamed category (levels 4-6)
      MasteryFilter(level: 4, name: 'Tamed'),
      // Mastered category (levels 7-10)
      MasteryFilter(level: 7, name: 'Mastered'),
    ];
  }

  // Default frequency levels
  static List<FrequencyFilter> defaultFrequencyFilters() {
    return List.generate(5, (i) => FrequencyFilter(frequency: i + 1));
  }

  VocabFilterOptions({
    List<WordTypeFilter>? wordTypes,
    List<LevelFilter>? cefrLevels,
    List<MasteryFilter>? masteryLevels,
    List<FrequencyFilter>? frequencyFilters,
    this.activeFilterType = FilterType.alphabetical,
    this.filterDirection = FilterDirection.ascending,
  })  : wordTypes = wordTypes ?? defaultWordTypes(),
        cefrLevels = cefrLevels ?? defaultCefrLevels(),
        masteryLevels = masteryLevels ?? defaultMasteryLevels(),
        frequencyFilters = frequencyFilters ?? defaultFrequencyFilters();

  // Get selected word types
  List<String> getSelectedWordTypes() {
    return wordTypes
        .where((type) => type.isSelected)
        .map((type) => type.type)
        .toList();
  }

  // Get selected CEFR levels
  List<String> getSelectedCefrLevels() {
    return cefrLevels
        .where((level) => level.isSelected)
        .map((level) => level.level)
        .toList();
  }

  // Get selected mastery levels
  List<int> getSelectedMasteryLevels() {
    return masteryLevels
        .where((level) => level.isSelected)
        .map((level) => level.level)
        .toList();
  }

  // Get selected frequency levels
  List<int> getSelectedFrequencies() {
    return frequencyFilters
        .where((f) => f.isSelected)
        .map((f) => f.frequency)
        .toList();
  }

  // Check if any filters are active
  bool get hasActiveFilters {
    return wordTypes.any((t) => t.isSelected) ||
        cefrLevels.any((l) => l.isSelected) ||
        masteryLevels.any((m) => m.isSelected) ||
        frequencyFilters.any((f) => f.isSelected);
  }

  // Create a copy with reset filters
  VocabFilterOptions resetFilters() {
    return VocabFilterOptions(
      wordTypes: wordTypes
          .map((t) => WordTypeFilter(type: t.type, isSelected: false))
          .toList(),
      cefrLevels: cefrLevels
          .map((l) => LevelFilter(level: l.level, isSelected: false))
          .toList(),
      masteryLevels: masteryLevels
          .map((m) =>
              MasteryFilter(level: m.level, name: m.name, isSelected: false))
          .toList(),
      frequencyFilters: frequencyFilters
          .map(
              (f) => FrequencyFilter(frequency: f.frequency, isSelected: false))
          .toList(),
      activeFilterType: FilterType.alphabetical,
      filterDirection: FilterDirection.ascending,
    );
  }

  // Create a copy with the given changes
  VocabFilterOptions copyWith({
    List<WordTypeFilter>? wordTypes,
    List<LevelFilter>? cefrLevels,
    List<MasteryFilter>? masteryLevels,
    List<FrequencyFilter>? frequencyFilters,
    FilterType? activeFilterType,
    FilterDirection? filterDirection,
  }) {
    return VocabFilterOptions(
      wordTypes: wordTypes ?? this.wordTypes,
      cefrLevels: cefrLevels ?? this.cefrLevels,
      masteryLevels: masteryLevels ?? this.masteryLevels,
      frequencyFilters: frequencyFilters ?? this.frequencyFilters,
      activeFilterType: activeFilterType ?? this.activeFilterType,
      filterDirection: filterDirection ?? this.filterDirection,
    );
  }
}
