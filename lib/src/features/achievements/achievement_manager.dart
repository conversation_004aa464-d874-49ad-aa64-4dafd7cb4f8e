import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/achievements/achievement_model.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Manages the user's achievements
class AchievementManager {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseService _firebaseService = FirebaseService();

  /// Get the current user ID
  String get _userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  /// Initialize default achievements for a new user
  Future<void> initializeAchievements() async {
    try {
      // Check if achievements collection already exists
      final snapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        // Already initialized
        return;
      }

      // Collection is empty, initialize default achievements
      final batch = _firestore.batch();

      // Mastery achievements
      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('mastery_first'),
        Achievement(
          id: 'mastery_first',
          title: 'First Mastery',
          description: 'Master your first vocabulary word',
          type: AchievementType.mastery,
          icon: 'mastery',
          pointsAwarded: 50,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('mastery_10'),
        Achievement(
          id: 'mastery_10',
          title: 'Vocabulary Master',
          description: 'Master 10 vocabulary words',
          type: AchievementType.mastery,
          icon: 'mastery',
          pointsAwarded: 100,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('mastery_50'),
        Achievement(
          id: 'mastery_50',
          title: 'Lexicon Legend',
          description: 'Master 50 vocabulary words',
          type: AchievementType.mastery,
          icon: 'mastery',
          pointsAwarded: 500,
        ).toFirestore(),
      );

      // Streak achievements
      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('streak_3'),
        Achievement(
          id: 'streak_3',
          title: 'Three-Day Streak',
          description: 'Use the app for 3 consecutive days',
          type: AchievementType.streak,
          icon: 'streak',
          pointsAwarded: 30,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('streak_7'),
        Achievement(
          id: 'streak_7',
          title: 'Weekly Dedication',
          description: 'Use the app for 7 consecutive days',
          type: AchievementType.streak,
          icon: 'streak',
          pointsAwarded: 70,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('streak_30'),
        Achievement(
          id: 'streak_30',
          title: 'Monthly Devotion',
          description: 'Use the app for 30 consecutive days',
          type: AchievementType.streak,
          icon: 'streak',
          pointsAwarded: 300,
        ).toFirestore(),
      );

      // Quiz achievements
      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('quiz_first'),
        Achievement(
          id: 'quiz_first',
          title: 'First Quiz',
          description: 'Complete your first quiz',
          type: AchievementType.quiz,
          icon: 'quiz',
          pointsAwarded: 20,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('quiz_perfect'),
        Achievement(
          id: 'quiz_perfect',
          title: 'Perfect Score',
          description: 'Get a perfect score on a quiz',
          type: AchievementType.quiz,
          icon: 'quiz',
          pointsAwarded: 100,
        ).toFirestore(),
      );

      // Collection achievements
      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('collection_10'),
        Achievement(
          id: 'collection_10',
          title: 'Beginner Collector',
          description: 'Add 10 words to your collection',
          type: AchievementType.collection,
          icon: 'vocabulary',
          pointsAwarded: 50,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('collection_50'),
        Achievement(
          id: 'collection_50',
          title: 'Vocabulary Enthusiast',
          description: 'Add 50 words to your collection',
          type: AchievementType.collection,
          icon: 'vocabulary',
          pointsAwarded: 200,
        ).toFirestore(),
      );

      batch.set(
        _firestore
            .collection('users')
            .doc(_userId)
            .collection('achievements')
            .doc('collection_100'),
        Achievement(
          id: 'collection_100',
          title: 'Vocabulary Expert',
          description: 'Add 100 words to your collection',
          type: AchievementType.collection,
          icon: 'vocabulary',
          pointsAwarded: 500,
        ).toFirestore(),
      );

      // Word type achievements
      final wordTypes = [
        'verb',
        'noun',
        'adjective',
        'adverb',
        'pronoun',
        'preposition',
        'conjunction',
        'interjection'
      ];

      for (final type in wordTypes) {
        batch.set(
          _firestore
              .collection('users')
              .doc(_userId)
              .collection('achievements')
              .doc('type_${type}_5'),
          Achievement(
            id: 'type_${type}_5',
            title:
                '${type.substring(0, 1).toUpperCase()}${type.substring(1)} Specialist',
            description: 'Master 5 ${type}s',
            type: AchievementType.wordType,
            icon: 'vocabulary',
            pointsAwarded: 50,
            wordType: type,
          ).toFirestore(),
        );
      }

      // Commit all the achievements to Firestore
      await batch.commit();

      debugPrint('Initialized achievements for user');
    } catch (e) {
      debugPrint('Error initializing achievements: $e');
    }
  }

  /// Get all achievements for the current user
  Future<List<Achievement>> getUserAchievements() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .get();

      return snapshot.docs
          .map((doc) => Achievement.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting achievements: $e');
      return [];
    }
  }

  /// Get unlocked achievements for the current user
  Future<List<Achievement>> getUnlockedAchievements() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .where('is_unlocked', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => Achievement.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting unlocked achievements: $e');
      return [];
    }
  }

  /// Unlock an achievement by its ID
  Future<bool> unlockAchievement(String achievementId) async {
    try {
      // Get the achievement
      final doc = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .doc(achievementId)
          .get();

      if (!doc.exists) {
        debugPrint('Achievement not found: $achievementId');
        return false;
      }

      final achievement = Achievement.fromFirestore(doc);

      // Skip if already unlocked
      if (achievement.isUnlocked) {
        return false;
      }

      // Unlock the achievement
      await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .doc(achievementId)
          .update({
        'is_unlocked': true,
        'unlocked_at': FieldValue.serverTimestamp(),
      });

      // Award the points
      await _firestore.collection('users').doc(_userId).update({
        'points': FieldValue.increment(achievement.pointsAwarded),
      });

      debugPrint('Achievement unlocked: ${achievement.title}');
      return true;
    } catch (e) {
      debugPrint('Error unlocking achievement: $e');
      return false;
    }
  }

  /// Check for and update collection-based achievements
  Future<List<Achievement>> checkCollectionAchievements() async {
    try {
      final cards = await _firebaseService.fetchVocabulary();
      final count = cards.length;

      List<Achievement> unlocked = [];

      // Check milestones
      if (count >= 10) {
        final result = await unlockAchievement('collection_10');
        if (result) {
          final achievement = await _getAchievement('collection_10');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (count >= 50) {
        final result = await unlockAchievement('collection_50');
        if (result) {
          final achievement = await _getAchievement('collection_50');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (count >= 100) {
        final result = await unlockAchievement('collection_100');
        if (result) {
          final achievement = await _getAchievement('collection_100');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      return unlocked;
    } catch (e) {
      debugPrint('Error checking collection achievements: $e');
      return [];
    }
  }

  /// Check for and update mastery-based achievements
  Future<List<Achievement>> checkMasteryAchievements() async {
    try {
      final cards = await _firebaseService.fetchVocabulary();

      // Count mastered cards
      int masteredCount = 0;
      Map<String, int> typeCount = {};

      for (final card in cards) {
        if (card.getMasteryStatus() == MasteryStatus.mastered) {
          masteredCount++;

          // Count by word type
          for (final type in card.type) {
            final normalizedType = type.toLowerCase();
            typeCount[normalizedType] = (typeCount[normalizedType] ?? 0) + 1;
          }
        }
      }

      List<Achievement> unlocked = [];

      // Check mastery milestones
      if (masteredCount >= 1) {
        final result = await unlockAchievement('mastery_first');
        if (result) {
          final achievement = await _getAchievement('mastery_first');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (masteredCount >= 10) {
        final result = await unlockAchievement('mastery_10');
        if (result) {
          final achievement = await _getAchievement('mastery_10');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (masteredCount >= 50) {
        final result = await unlockAchievement('mastery_50');
        if (result) {
          final achievement = await _getAchievement('mastery_50');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      // Check word type achievements
      for (final entry in typeCount.entries) {
        final type = entry.key;
        final count = entry.value;

        if (count >= 5) {
          final achievementId = 'type_${type}_5';
          final result = await unlockAchievement(achievementId);
          if (result) {
            final achievement = await _getAchievement(achievementId);
            if (achievement != null) {
              unlocked.add(achievement);
            }
          }
        }
      }

      return unlocked;
    } catch (e) {
      debugPrint('Error checking mastery achievements: $e');
      return [];
    }
  }

  /// Check for quiz-related achievements
  Future<List<Achievement>> checkQuizAchievements(int score, int total) async {
    try {
      List<Achievement> unlocked = [];

      // First quiz achievement
      final result1 = await unlockAchievement('quiz_first');
      if (result1) {
        final achievement = await _getAchievement('quiz_first');
        if (achievement != null) {
          unlocked.add(achievement);
        }
      }

      // Perfect score achievement
      if (score == total) {
        final result2 = await unlockAchievement('quiz_perfect');
        if (result2) {
          final achievement = await _getAchievement('quiz_perfect');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      return unlocked;
    } catch (e) {
      debugPrint('Error checking quiz achievements: $e');
      return [];
    }
  }

  /// Check for streak-related achievements
  Future<List<Achievement>> checkStreakAchievements(int currentStreak) async {
    try {
      List<Achievement> unlocked = [];

      // Check streak milestones
      if (currentStreak >= 3) {
        final result = await unlockAchievement('streak_3');
        if (result) {
          final achievement = await _getAchievement('streak_3');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (currentStreak >= 7) {
        final result = await unlockAchievement('streak_7');
        if (result) {
          final achievement = await _getAchievement('streak_7');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      if (currentStreak >= 30) {
        final result = await unlockAchievement('streak_30');
        if (result) {
          final achievement = await _getAchievement('streak_30');
          if (achievement != null) {
            unlocked.add(achievement);
          }
        }
      }

      return unlocked;
    } catch (e) {
      debugPrint('Error checking streak achievements: $e');
      return [];
    }
  }

  /// Get an achievement by ID
  Future<Achievement?> _getAchievement(String achievementId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('achievements')
          .doc(achievementId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return Achievement.fromFirestore(doc);
    } catch (e) {
      debugPrint('Error getting achievement: $e');
      return null;
    }
  }
}
