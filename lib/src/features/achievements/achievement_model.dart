import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Model representing a user achievement
class Achievement {
  final String id;
  final String title;
  final String description;
  final AchievementType type;
  final int level; // For tiered achievements (e.g., level 1, 2, 3)
  final String icon;
  final int pointsAwarded;
  final bool isUnlocked;
  final DateTime? unlockedAt;
  final String? wordType; // If achievement is related to a specific word type

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.level = 1,
    required this.icon,
    required this.pointsAwarded,
    this.isUnlocked = false,
    this.unlockedAt,
    this.wordType,
  });

  /// Create an Achievement from Firestore document
  factory Achievement.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Achievement(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String,
      type: AchievementType.values.firstWhere(
        (type) => type.name == data['type'],
        orElse: () => AchievementType.general,
      ),
      level: (data['level'] as num?)?.toInt() ?? 1,
      icon: data['icon'] as String,
      pointsAwarded: (data['points_awarded'] as num).toInt(),
      isUnlocked: data['is_unlocked'] as bool,
      unlockedAt: data['unlocked_at'] != null
          ? (data['unlocked_at'] as Timestamp).toDate()
          : null,
      wordType: data['word_type'] as String?,
    );
  }

  /// Convert Achievement to a Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'type': type.name,
      'level': level,
      'icon': icon,
      'points_awarded': pointsAwarded,
      'is_unlocked': isUnlocked,
      'unlocked_at':
          unlockedAt != null ? Timestamp.fromDate(unlockedAt!) : null,
      'word_type': wordType,
    };
  }

  /// Create a copy of this achievement with updated properties
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    AchievementType? type,
    int? level,
    String? icon,
    int? pointsAwarded,
    bool? isUnlocked,
    DateTime? unlockedAt,
    String? wordType,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      level: level ?? this.level,
      icon: icon ?? this.icon,
      pointsAwarded: pointsAwarded ?? this.pointsAwarded,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      wordType: wordType ?? this.wordType,
    );
  }

  /// Get the icon data for this achievement
  IconData getIconData() {
    // This is a simplified version - in reality, you'd want to map
    // icon string names to actual IconData objects
    switch (icon) {
      case 'trophy':
        return Icons.emoji_events;
      case 'streak':
        return Icons.local_fire_department;
      case 'mastery':
        return Icons.military_tech;
      case 'quiz':
        return Icons.quiz;
      case 'vocabulary':
        return Icons.menu_book;
      default:
        return Icons.star;
    }
  }

  /// Get the color for this achievement type
  Color getColor() {
    switch (type) {
      case AchievementType.mastery:
        return AppColors.primaryLight; // Purple
      case AchievementType.streak:
        return AppColors.warningLight; // Orange/Yellow
      case AchievementType.quiz:
        return AppColors.infoLight; // Blue
      case AchievementType.collection:
        return AppColors.successLight; // Green
      case AchievementType.wordType:
        return AppColors.secondaryLight; // Teal/Secondary
      case AchievementType.general:
      default:
        return AppColors.warningLight; // Amber/Yellow
    }
  }
}

/// Different types of achievements
enum AchievementType {
  general, // General achievements
  mastery, // Related to mastering vocabulary
  streak, // Daily usage streaks
  quiz, // Quiz performance
  collection, // Collection size achievements
  wordType, // Word type specific achievements
}
