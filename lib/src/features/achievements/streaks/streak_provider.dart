import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/streaks/streak_tracker.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

/// Provider for the StreakTracker
final streakTrackerProvider = Provider<StreakTracker>((ref) {
  return StreakTracker();
});

/// Provider for current streak count
final currentStreakProvider = FutureProvider<int>((ref) async {
  final streakTracker = ref.read(streakTrackerProvider);
  return await streakTracker.getCurrentStreak();
});

/// Provider for longest streak count
final longestStreakProvider = FutureProvider<int>((ref) async {
  final streakTracker = ref.read(streakTrackerProvider);
  return await streakTracker.getLongestStreak();
});

/// Provider for streak calendar data
final streakCalendarProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final streakTracker = ref.read(streakTrackerProvider);
  return await streakTracker.getStreakData();
});

/// Provider that records daily visit when the app is loaded
final recordDailyVisitProvider = FutureProvider<void>((ref) async {
  final streakTracker = ref.read(streakTrackerProvider);
  await streakTracker.recordDailyVisit();
});

/// A streak screen that displays the streak calendar and stats
class StreakScreen extends ConsumerWidget {
  const StreakScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final streakCalendarAsync = ref.watch(streakCalendarProvider);
    final isPremium = ref.watch(subscriptionStateProvider);
    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text('Streak Calendar'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with fire icon and streak info
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: streakCalendarAsync.when(
                  data: (data) {
                    final currentStreak = data['current_streak'] as int;
                    final longestStreak = data['longest_streak'] as int;

                    return Row(
                      children: [
                        Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            color: Colors.deepOrange.withAlpha(26),shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.local_fire_department,
                            color: Colors.deepOrange,
                            size: 36,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '$currentStreak Day Streak',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Longest streak: $longestStreak days',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, _) => Text('Error: $error'),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Streak calendar
            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: streakCalendarAsync.when(
                  data: (data) {
                    final streakDates =
                        List<String>.from(data['streak_dates'] as List);
                    final currentStreak = data['current_streak'] as int;
                    final longestStreak = data['longest_streak'] as int;

                    return StreakCalendarWidget(
                      streakDates: streakDates,
                      currentStreak: currentStreak,
                      longestStreak: longestStreak,
                    );
                  },
                  loading: () => const SizedBox(
                    height: 300,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  error: (error, _) => Text('Error: $error'),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Streak recovery (Premium only)
            if (!isPremium)
              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Premium Feature',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Upgrade to Premium to unlock Streak Recovery!',
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      OutlinedButton(
                        onPressed: () {
                          // Navigate to premium upgrade
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.amber,
                          side: const BorderSide(color: Colors.amber),
                        ),
                        child: const Text('Upgrade to Premium'),
                      ),
                    ],
                  ),
                ),
              )
            else
              Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(
                            Icons.auto_fix_high,
                            color: Colors.amber,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Streak Recovery',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Missed a day? No problem! As a Premium user, you can recover your streak once per week.',
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          final streakTracker = ref.read(streakTrackerProvider);
                          final success = await streakTracker.recoverStreak();

                          if (context.mounted) {
                            if (success) {
                              showSuccessToast(
                                context,
                                title: 'Streak Recovered',
                                description: 'Streak recovered successfully!',
                              );
                            } else {
                              showFailureToast(
                                context,
                                title: 'Recovery Failed',
                                description:
                                    'Failed to recover streak. You may have already used this feature this week.',
                              );
                            }

                            // Refresh data
                            ref.refresh(streakCalendarProvider);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Recover Streak'),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// /// Provider for premium status
// final isPremiumUserProvider = FutureProvider<bool>((ref) async {
//   // Implement this using the Firebase service
//   return false; // Default to false until implemented
// });

/// Screen that shows recent streaks in a compact way
class StreakSummaryWidget extends ConsumerWidget {
  const StreakSummaryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final streakAsync = ref.watch(currentStreakProvider);
    final longestStreakAsync = ref.watch(longestStreakProvider);

    return Card(
      color: AppColors.cardBackgroundLight,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Your Streak',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const StreakScreen(),
                      ),
                    );
                  },
                  child: Text(
                    'Details',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Current streak
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.deepOrange.withAlpha(26),borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.deepOrange.withAlpha(76),),
                    ),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.local_fire_department,
                          color: Colors.deepOrange,
                        ),
                        const SizedBox(height: 8),
                        streakAsync.when(
                          data: (streak) => Text(
                            streak.toString(),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.deepOrange,
                            ),
                          ),
                          loading: () => const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          error: (_, __) => const Text('0'),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Current Streak',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Longest streak
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(26),borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.green.withAlpha(76),),
                    ),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.emoji_events,
                          color: Colors.green,
                        ),
                        const SizedBox(height: 8),
                        longestStreakAsync.when(
                          data: (streak) => Text(
                            streak.toString(),
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          loading: () => const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                          error: (_, __) => const Text('0'),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Longest Streak',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Visit the app daily to maintain your streak!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
