import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:vocadex/src/features/achievements/achievement_manager.dart';

/// Tracks user's daily usage streak
class StreakTracker {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AchievementManager _achievementManager = AchievementManager();

  /// Get the current user's ID
  String get _userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  /// Get the current date as a string in the format "yyyy-MM-dd"
  String _getCurrentDateString() {
    final DateTime now = DateTime.now();
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(now);
  }

  /// Record a user visit for today
  Future<void> recordDailyVisit() async {
    try {
      final today = _getCurrentDateString();

      // Get user document
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;

      if (userData == null) {
        // If user doesn't exist, create new user with streak data
        await _firestore.collection('users').doc(_userId).set({
          'current_streak': 1,
          'longest_streak': 1,
          'last_visit_date': today,
          'streak_dates': [today],
        }, SetOptions(merge: true));

        return;
      }

      // Check if user already has streak data
      final lastVisitDate = userData['last_visit_date'] as String?;
      int currentStreak = (userData['current_streak'] as num?)?.toInt() ?? 0;
      int longestStreak = (userData['longest_streak'] as num?)?.toInt() ?? 0;
      List<String> streakDates = [];

      if (userData['streak_dates'] != null) {
        streakDates = List<String>.from(userData['streak_dates'] as List);
      }

      // If already visited today, do nothing
      if (lastVisitDate == today) {
        return;
      }

      // Check if the last visit was yesterday
      final yesterday = _getPreviousDate(today);
      if (lastVisitDate == yesterday) {
        // Continue streak
        currentStreak++;

        // Update longest streak if current is greater
        if (currentStreak > longestStreak) {
          longestStreak = currentStreak;
        }
      } else if (lastVisitDate != null) {
        // Streak broken, start new streak
        currentStreak = 1;
      } else {
        // First visit ever
        currentStreak = 1;
        longestStreak = 1;
      }

      // Add today to streak dates (limit to last 30 days)
      streakDates.add(today);
      if (streakDates.length > 30) {
        streakDates = streakDates.sublist(streakDates.length - 30);
      }

      // Update streak data
      await _firestore.collection('users').doc(_userId).update({
        'current_streak': currentStreak,
        'longest_streak': longestStreak,
        'last_visit_date': today,
        'streak_dates': streakDates,
      });

      // Check for streak achievements
      if (currentStreak >= 3 || currentStreak >= 7 || currentStreak >= 30) {
        await _achievementManager.checkStreakAchievements(currentStreak);
      }
    } catch (e) {
      debugPrint('Error recording daily visit: $e');
    }
  }

  /// Get the previous date as a string
  String _getPreviousDate(String dateString) {
    final format = DateFormat('yyyy-MM-dd');
    final date = format.parse(dateString);
    final previousDate = date.subtract(const Duration(days: 1));
    return format.format(previousDate);
  }

  /// Get the user's current streak
  Future<int> getCurrentStreak() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;

      if (userData == null) {
        return 0;
      }

      return (userData['current_streak'] as num?)?.toInt() ?? 0;
    } catch (e) {
      debugPrint('Error getting current streak: $e');
      return 0;
    }
  }

  /// Get the user's longest streak
  Future<int> getLongestStreak() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;

      if (userData == null) {
        return 0;
      }

      return (userData['longest_streak'] as num?)?.toInt() ?? 0;
    } catch (e) {
      debugPrint('Error getting longest streak: $e');
      return 0;
    }
  }

  /// Get the streak data for the last 30 days
  Future<Map<String, dynamic>> getStreakData() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;

      if (userData == null) {
        return {
          'current_streak': 0,
          'longest_streak': 0,
          'streak_dates': <String>[],
        };
      }

      final currentStreak = (userData['current_streak'] as num?)?.toInt() ?? 0;
      final longestStreak = (userData['longest_streak'] as num?)?.toInt() ?? 0;
      List<String> streakDates = [];

      if (userData['streak_dates'] != null) {
        streakDates = List<String>.from(userData['streak_dates'] as List);
      }

      return {
        'current_streak': currentStreak,
        'longest_streak': longestStreak,
        'streak_dates': streakDates,
      };
    } catch (e) {
      debugPrint('Error getting streak data: $e');
      return {
        'current_streak': 0,
        'longest_streak': 0,
        'streak_dates': <String>[],
      };
    }
  }

  /// Recover a broken streak (premium feature)
  Future<bool> recoverStreak() async {
    try {
      final today = _getCurrentDateString();
      final yesterday = _getPreviousDate(today);

      // Get user document
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data() as Map<String, dynamic>?;

      if (userData == null) {
        return false;
      }

      // Check if user is premium
      final isPremium = userData['is_premium'] as bool? ?? false;
      if (!isPremium) {
        return false; // Only premium users can recover streaks
      }

      // Update last visit date to yesterday to recover streak
      await _firestore.collection('users').doc(_userId).update({
        'last_visit_date': yesterday,
      });

      // Now record today's visit to continue the streak
      await recordDailyVisit();

      return true;
    } catch (e) {
      debugPrint('Error recovering streak: $e');
      return false;
    }
  }
}

/// Widget to display a calendar of streak days
class StreakCalendarWidget extends StatelessWidget {
  final List<String> streakDates;
  final int currentStreak;
  final int longestStreak;

  const StreakCalendarWidget({
    super.key,
    required this.streakDates,
    required this.currentStreak,
    required this.longestStreak,
  });

  @override
  Widget build(BuildContext context) {
    // Get the current month days
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    final firstDayOfMonth = DateTime(now.year, now.month, 1);
    final firstWeekday = firstDayOfMonth.weekday;

    // Convert streak dates to DateTime objects
    final streakDateObjects = streakDates
        .map((dateStr) => DateFormat('yyyy-MM-dd').parse(dateStr))
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                DateFormat('MMMM yyyy').format(now),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  Text(
                    'Current: $currentStreak',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.deepOrange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'Best: $longestStreak',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // Weekday headers
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const [
            Text('M', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('T', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('W', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('T', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('F', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('S', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('S', style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 8),

        // Calendar grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            childAspectRatio: 1,
          ),
          itemCount: (firstWeekday - 1) + daysInMonth,
          itemBuilder: (context, index) {
            // Empty cells for days before the 1st of the month
            if (index < (firstWeekday - 1)) {
              return const SizedBox();
            }

            // Days of the month
            final day = index - (firstWeekday - 1) + 1;
            final date = DateTime(now.year, now.month, day);

            // Check if this date is in the streak
            final isStreakDay = streakDateObjects.any((d) =>
                d.year == date.year &&
                d.month == date.month &&
                d.day == date.day);

            // Check if this date is today
            final isToday = now.year == date.year &&
                now.month == date.month &&
                now.day == date.day;

            // Check if this date is in the future
            final isFuture = date.isAfter(now);

            return Container(
              margin: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isStreakDay
                    ? Colors.deepOrange.withAlpha(51): Colors.transparent,
                border: Border.all(
                  color: isToday ? Colors.deepOrange : Colors.grey.shade300,
                  width: isToday ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  day.toString(),
                  style: TextStyle(
                    color: isFuture
                        ? Colors.grey.shade400
                        : isStreakDay
                            ? Colors.deepOrange
                            : null,
                    fontWeight: isStreakDay || isToday
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
