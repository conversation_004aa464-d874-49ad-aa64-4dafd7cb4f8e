// import 'package:flutter/material.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

// /// A widget that displays the mastery status of a vocabulary card
// class MasteryStatusWidget extends StatelessWidget {
//   final VocabCard card;
//   final bool isCompact;
//   final double progressSize;
//   final double iconSize;
//   final bool showLabel;

//   const MasteryStatusWidget({
//     super.key,
//     required this.card,
//     this.isCompact = false,
//     this.progressSize = 8.0,
//     this.iconSize = 16.0,
//     this.showLabel = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final status = card.getMasteryStatus();
//     final statusText = card.getMasteryStatusText();
//     final statusColor = card.getMasteryStatusColor();
//     final percentage = card.getMasteryPercentage() / 100;

//     // Get appropriate icon for mastery status
//     IconData statusIcon;
//     switch (status) {
//       case MasteryStatus.wild:
//         statusIcon = Icons.catching_pokemon;
//         break;
//       case MasteryStatus.tamed:
//         statusIcon = Icons.pets;
//         break;
//       case MasteryStatus.mastered:
//         statusIcon = Icons.military_tech;
//         break;
//     }

//     if (isCompact) {
//       // Compact version just shows the icon with color
//       return Tooltip(
//         message: 'Mastery: $statusText (${card.correctAnswers}/10)',
//         child: Container(
//           padding: const EdgeInsets.all(4),
//           decoration: BoxDecoration(
//             color: statusColor.withAlpha(26),//             borderRadius: BorderRadius.circular(8),
//             border: Border.all(color: statusColor, width: 1),
//           ),
//           child: Icon(
//             statusIcon,
//             size: iconSize,
//             color: statusColor,
//           ),
//         ),
//       );
//     }

//     // Full version with icon, text and progress bar
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
//       decoration: BoxDecoration(
//         color: statusColor.withAlpha(26),//         borderRadius: BorderRadius.circular(12),
//         border: Border.all(color: statusColor, width: 1),
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Icon(
//                 statusIcon,
//                 size: iconSize,
//                 color: statusColor,
//               ),
//               if (showLabel) ...[
//                 const SizedBox(width: 8),
//                 Text(
//                   statusText,
//                   style: TextStyle(
//                     color: statusColor,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               ],
//               const Spacer(),
//               Text(
//                 '${card.correctAnswers}/10',
//                 style: TextStyle(
//                   color: statusColor,
//                   fontSize: 12,
//                 ),
//               ),
//             ],
//           ),
//           const SizedBox(height: 4),
//           ClipRRect(
//             borderRadius: BorderRadius.circular(4),
//             child: LinearProgressIndicator(
//               value: percentage,
//               backgroundColor: Colors.grey.shade200,
//               valueColor: AlwaysStoppedAnimation<Color>(statusColor),
//               minHeight: progressSize,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// /// A widget that displays the word type with an appropriate icon
// class WordTypeChip extends StatelessWidget {
//   final String type;
//   final double? fontSize;

//   const WordTypeChip({
//     super.key,
//     required this.type,
//     this.fontSize,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final typeData = _getTypeData(type);

//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: typeData.color.withAlpha(26),//         borderRadius: BorderRadius.circular(16),
//         border: Border.all(color: typeData.color.withAlpha(128),//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(
//             typeData.icon,
//             size: 14,
//             color: typeData.color,
//           ),
//           const SizedBox(width: 4),
//           Text(
//             typeData.displayName,
//             style: TextStyle(
//               fontSize: fontSize ?? 12,
//               color: typeData.color,
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   /// Get icon, color, and display name for word type
//   _WordTypeData _getTypeData(String type) {
//     final normalizedType = type.toLowerCase();

//     switch (normalizedType) {
//       case 'verb':
//         return _WordTypeData(
//           icon: Icons.directions_run,
//           color: Colors.blue,
//           displayName: 'Verb',
//         );
//       case 'noun':
//         return _WordTypeData(
//           icon: Icons.apple,
//           color: Colors.red,
//           displayName: 'Noun',
//         );
//       case 'adjective':
//         return _WordTypeData(
//           icon: Icons.thermostat,
//           color: Colors.orange,
//           displayName: 'Adjective',
//         );
//       case 'adverb':
//         return _WordTypeData(
//           icon: Icons.timer,
//           color: Colors.purple,
//           displayName: 'Adverb',
//         );
//       case 'preposition':
//         return _WordTypeData(
//           icon: Icons.arrow_forward,
//           color: Colors.green,
//           displayName: 'Preposition',
//         );
//       case 'pronoun':
//         return _WordTypeData(
//           icon: Icons.person,
//           color: Colors.teal,
//           displayName: 'Pronoun',
//         );
//       case 'conjunction':
//         return _WordTypeData(
//           icon: Icons.link,
//           color: Colors.amber,
//           displayName: 'Conjunction',
//         );
//       case 'interjection':
//       case 'slang':
//         return _WordTypeData(
//           icon: Icons.sentiment_satisfied_alt,
//           color: Colors.pink,
//           displayName: type,
//         );
//       default:
//         return _WordTypeData(
//           icon: Icons.text_fields,
//           color: Colors.grey,
//           displayName: type,
//         );
//     }
//   }
// }

// /// Helper class for word type data
// class _WordTypeData {
//   final IconData icon;
//   final Color color;
//   final String displayName;

//   _WordTypeData({
//     required this.icon,
//     required this.color,
//     required this.displayName,
//   });
// }

// lib/src/features/achievements/mastery/mastery_stats_widget.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';
import 'package:vocadex/src/features/vocab_deck/filter/model_filter_options.dart';
import 'package:vocadex/src/features/vocab_deck/filter/provider_filter.dart';

/// A provider that calculates mastery statistics based on vocabulary cards
final masteryStatsProvider = Provider<MasteryStats>((ref) {
  // Get the filtered cards (respects current filters)
  final filteredCards = ref.watch(filteredVocabCardsProvider);

  // Count cards in each mastery category
  int wildCount = 0;
  int tamedCount = 0;
  int masteredCount = 0;

  // Count by word type
  Map<String, Map<String, int>> typeStats = {};

  for (final card in filteredCards) {
    // Get mastery status
    final status = card.getMasteryStatus();

    // Increment appropriate counter
    switch (status) {
      case MasteryStatus.wild:
        wildCount++;
        break;
      case MasteryStatus.tamed:
        tamedCount++;
        break;
      case MasteryStatus.mastered:
        masteredCount++;
        break;
    }

    // Count by type
    for (final type in card.type) {
      if (!typeStats.containsKey(type)) {
        typeStats[type] = {
          'wild': 0,
          'tamed': 0,
          'mastered': 0,
          'total': 0,
        };
      }

      // Increment total for this type
      typeStats[type]!['total'] = (typeStats[type]!['total'] ?? 0) + 1;

      // Increment mastery counter for this type
      switch (status) {
        case MasteryStatus.wild:
          typeStats[type]!['wild'] = (typeStats[type]!['wild'] ?? 0) + 1;
          break;
        case MasteryStatus.tamed:
          typeStats[type]!['tamed'] = (typeStats[type]!['tamed'] ?? 0) + 1;
          break;
        case MasteryStatus.mastered:
          typeStats[type]!['mastered'] =
              (typeStats[type]!['mastered'] ?? 0) + 1;
          break;
      }
    }
  }

  return MasteryStats(
    totalCards: filteredCards.length,
    wildCards: wildCount,
    tamedCards: tamedCount,
    masteredCards: masteredCount,
    typeStats: typeStats,
  );
});

/// Data class containing mastery statistics
class MasteryStats {
  final int totalCards;
  final int wildCards;
  final int tamedCards;
  final int masteredCards;
  final Map<String, Map<String, int>> typeStats;

  MasteryStats({
    required this.totalCards,
    required this.wildCards,
    required this.tamedCards,
    required this.masteredCards,
    required this.typeStats,
  });

  /// Calculate percentage of mastered cards
  double get masteredPercentage =>
      totalCards > 0 ? (masteredCards / totalCards * 100) : 0;

  /// Calculate percentage of tamed cards
  double get tamedPercentage =>
      totalCards > 0 ? (tamedCards / totalCards * 100) : 0;

  /// Calculate percentage of wild cards
  double get wildPercentage =>
      totalCards > 0 ? (wildCards / totalCards * 100) : 0;
}

/// A widget that displays mastery statistics
class MasteryStatsWidget extends ConsumerWidget {
  final bool showTypeBreakdown;
  final bool isCompact;

  const MasteryStatsWidget({
    super.key,
    this.showTypeBreakdown = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stats = ref.watch(masteryStatsProvider);
    final theme = Theme.of(context);

    if (isCompact) {
      return _buildCompactStats(context, stats, theme);
    }

    return _buildFullStats(context, stats, theme, ref);
  }

  Widget _buildCompactStats(
      BuildContext context, MasteryStats stats, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Mastery Progress',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${stats.totalCards} cards',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar showing mastery distribution
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                height: 20,
                child: Row(
                  children: [
                    // Mastered section
                    Expanded(
                      flex: stats.masteredCards,
                      child: Container(
                        color: Colors.green,
                        height: double.infinity,
                        child: stats.masteredCards > 0
                            ? Center(
                                child: Text(
                                  '${stats.masteredCards}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),

                    // Tamed section
                    Expanded(
                      flex: stats.tamedCards,
                      child: Container(
                        color: Colors.orange,
                        height: double.infinity,
                        child: stats.tamedCards > 0
                            ? Center(
                                child: Text(
                                  '${stats.tamedCards}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),

                    // Wild section
                    Expanded(
                      flex: stats.wildCards,
                      child: Container(
                        color: Colors.red,
                        height: double.infinity,
                        child: stats.wildCards > 0
                            ? Center(
                                child: Text(
                                  '${stats.wildCards}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildLegendItem(Colors.green, 'Mastered', stats.masteredCards),
                _buildLegendItem(Colors.orange, 'Tamed', stats.tamedCards),
                _buildLegendItem(Colors.red, 'Wild', stats.wildCards),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullStats(
    BuildContext context,
    MasteryStats stats,
    ThemeData theme,
    WidgetRef ref,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Vocabulary Mastery',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${stats.totalCards} total cards',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Circular progress indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildMasteryCircle(
                  label: 'Mastered',
                  count: stats.masteredCards,
                  percentage: stats.masteredPercentage,
                  color: Colors.green,
                ),
                _buildMasteryCircle(
                  label: 'Tamed',
                  count: stats.tamedCards,
                  percentage: stats.tamedPercentage,
                  color: Colors.orange,
                ),
                _buildMasteryCircle(
                  label: 'Wild',
                  count: stats.wildCards,
                  percentage: stats.wildPercentage,
                  color: Colors.red,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Progress bar showing overall progress
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Overall Progress',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: SizedBox(
                    height: 24,
                    child: Row(
                      children: [
                        // Mastered section
                        Expanded(
                          flex: stats.masteredCards,
                          child: Container(
                            color: Colors.green,
                            height: double.infinity,
                            child: stats.masteredCards > 0
                                ? Center(
                                    child: Text(
                                      'Mastered: ${stats.masteredCards}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                        ),

                        // Tamed section
                        Expanded(
                          flex: stats.tamedCards,
                          child: Container(
                            color: Colors.orange,
                            height: double.infinity,
                            child: stats.tamedCards > 0
                                ? Center(
                                    child: Text(
                                      'Tamed: ${stats.tamedCards}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                        ),

                        // Wild section
                        Expanded(
                          flex: stats.wildCards,
                          child: Container(
                            color: Colors.red,
                            height: double.infinity,
                            child: stats.wildCards > 0
                                ? Center(
                                    child: Text(
                                      'Wild: ${stats.wildCards}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 12,
                                      ),
                                    ),
                                  )
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            if (showTypeBreakdown && stats.typeStats.isNotEmpty) ...[
              const SizedBox(height: 24),
              Text(
                'Mastery by Word Type',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              ...stats.typeStats.entries
                  .where((entry) => entry.value['total']! > 0)
                  .map((entry) =>
                      _buildWordTypeProgress(entry.key, entry.value)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMasteryCircle({
    required String label,
    required int count,
    required double percentage,
    required Color color,
  }) {
    return Column(
      children: [
        SizedBox(
          width: 80,
          height: 80,
          child: Stack(
            alignment: Alignment.center,
            children: [
              CircularProgressIndicator(
                value: percentage / 100,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(color),
                strokeWidth: 10,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$count',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${percentage.toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label, int count) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ($count)',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildWordTypeProgress(String type, Map<String, int> typeCounts) {
    final total = typeCounts['total'] ?? 0;
    if (total == 0) return const SizedBox.shrink();

    final mastered = typeCounts['mastered'] ?? 0;
    final tamed = typeCounts['tamed'] ?? 0;
    final wild = typeCounts['wild'] ?? 0;

    final masteredFlex = (mastered / total * 100).round();
    final tamedFlex = (tamed / total * 100).round();
    final wildFlex = (wild / total * 100).round();

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                type,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '$total cards',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: SizedBox(
              height: 12,
              child: Row(
                children: [
                  // Mastered
                  if (masteredFlex > 0)
                    Expanded(
                      flex: masteredFlex,
                      child: Container(color: Colors.green),
                    ),

                  // Tamed
                  if (tamedFlex > 0)
                    Expanded(
                      flex: tamedFlex,
                      child: Container(color: Colors.orange),
                    ),

                  // Wild
                  if (wildFlex > 0)
                    Expanded(
                      flex: wildFlex,
                      child: Container(color: Colors.red),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (mastered > 0) ...[
                Text(
                  'Mastered: $mastered',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
              ],
              if (tamed > 0) ...[
                Text(
                  'Tamed: $tamed',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
              ],
              if (wild > 0)
                Text(
                  'Wild: $wild',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
