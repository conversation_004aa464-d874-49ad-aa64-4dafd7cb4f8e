// // lib/src/features/achievements/mastery/mastery_providers.dart

// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:vocadex/src/features/achievements/mastery/mastery_level_manager.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// /// Provider for the MasteryLevelManager
// final masteryManagerProvider = Provider<MasteryLevelManager>((ref) {
//   return MasteryLevelManager();
// });

// /// Provider to get cards by mastery status
// final cardsByMasteryStatusProvider =
//     FutureProvider.family<List<VocabCard>, MasteryStatus>((ref, status) async {
//   final manager = ref.read(masteryManagerProvider);
//   return await manager.getCardsByMasteryStatus(status);
// });

// /// Provider to get overall mastery distribution
// final masteryDistributionProvider =
//     FutureProvider<Map<MasteryStatus, int>>((ref) async {
//   final manager = ref.read(masteryManagerProvider);
//   return await manager.getMasteryStatusCounts();
// });

// /// Provider to get mastery percentages by word type
// final typesMasteryPercentageProvider =
//     FutureProvider<Map<String, double>>((ref) async {
//   final manager = ref.read(masteryManagerProvider);
//   return await manager.getWordTypeMasteryStats();
// });

// /// Provider for determining if a card just advanced in mastery level
// final cardMasteryAdvancementProvider =
//     StateProvider.family<bool, VocabCard>((ref, card) => false);

// /// Provider to calculate and apply mastery decay
// final masteryDecayProvider = FutureProvider<int>((ref) async {
//   final manager = ref.read(masteryManagerProvider);
//   return await manager.applyMasteryDecay();
// });

// /// Provider to get recommended cards for review based on mastery status
// final recommendedReviewCardsProvider =
//     FutureProvider<List<VocabCard>>((ref) async {
//   // Get all cards
//   final firebaseService = FirebaseService();
//   final allCards = await firebaseService.fetchVocabulary();

//   // Sort list by priority for review:
//   // 1. Cards that are almost at a threshold (4, 8 correct answers)
//   // 2. Tamed cards (to get to mastered)
//   // 3. Wild cards (to get to tamed)
//   // 4. Cards that haven't been reviewed in a while

//   return allCards.map((card) {
//     // Calculate a review priority score
//     double priorityScore = 0;

//     // Prioritize cards at threshold points
//     if (card.correctAnswers == 4 || card.correctAnswers == 8) {
//       priorityScore += 100;
//     }

//     // Prioritize by mastery status
//     switch (card.getMasteryStatus()) {
//       case MasteryStatus.tamed:
//         priorityScore += 50;
//         break;
//       case MasteryStatus.wild:
//         priorityScore += 30;
//         break;
//       case MasteryStatus.mastered:
//         priorityScore += 10;
//         break;
//     }

//     // Prioritize cards not reviewed recently
//     if (card.lastReviewedAt != null) {
//       final daysSinceReview =
//           DateTime.now().difference(card.lastReviewedAt!).inDays;

//       // Higher priority for cards not reviewed in a while
//       if (daysSinceReview > 14) {
//         priorityScore += 40;
//       } else if (daysSinceReview > 7) {
//         priorityScore += 20;
//       }
//     } else {
//       // Never reviewed cards get high priority
//       priorityScore += 30;
//     }

//     return MapEntry(card, priorityScore);
//   })
//       // Sort by priority score (descending)
//       .toList()
//     ..sort((a, b) => b.value.compareTo(a.value))
//         // Extract just the cards
//         .map((entry) => entry.key)
//         .take(10) // Limit to 10 cards
//         .toList();
// });

// /// Provider for advanced quiz generation based on mastery needs
// final masteryBasedQuizProvider =
//     FutureProvider<Map<String, dynamic>>((ref) async {
//   // Get all cards
//   final firebaseService = FirebaseService();
//   final allCards = await firebaseService.fetchVocabulary();

//   // Get mastery distribution
//   final masteryDistribution =
//       await ref.watch(masteryDistributionProvider.future);

//   // Determine which category needs most attention
//   final wildCount = masteryDistribution[MasteryStatus.wild] ?? 0;
//   final tamedCount = masteryDistribution[MasteryStatus.tamed] ?? 0;
//   final masteredCount = masteryDistribution[MasteryStatus.mastered] ?? 0;

//   String quizFocus;
//   List<VocabCard> targetCards;

//   // Strategy: focus on the largest group that needs improvement
//   if (wildCount > tamedCount && wildCount > 0) {
//     // Focus on helping wild cards become tamed
//     quizFocus = 'wild';
//     targetCards = allCards
//         .where((card) => card.getMasteryStatus() == MasteryStatus.wild)
//         .toList();
//   } else if (tamedCount > 0) {
//     // Focus on helping tamed cards become mastered
//     quizFocus = 'tamed';
//     targetCards = allCards
//         .where((card) => card.getMasteryStatus() == MasteryStatus.tamed)
//         .toList();
//   } else {
//     // Reinforcement of mastered cards
//     quizFocus = 'mastered';
//     targetCards = allCards
//         .where((card) => card.getMasteryStatus() == MasteryStatus.mastered)
//         .toList();
//   }

//   // If we don't have enough cards in the target category, add some from other categories
//   if (targetCards.length < 5 && allCards.isNotEmpty) {
//     // Add random cards from other categories
//     final otherCards = allCards
//         .where((card) => !targetCards.contains(card))
//         .toList()
//       ..shuffle();

//     final remainingNeeded = 5 - targetCards.length;
//     final additionalCards = otherCards.take(remainingNeeded).toList();

//     targetCards.addAll(additionalCards);
//   }

//   // Shuffle the cards
//   targetCards.shuffle();

//   // Limit to 10 cards maximum
//   if (targetCards.length > 10) {
//     targetCards = targetCards.sublist(0, 10);
//   }

//   return {
//     'focus': quizFocus,
//     'cards': targetCards,
//   };
// });

// /// Provider for generating a flashcard study session focused on mastery improvement
// final masteryStudySessionProvider =
//     FutureProvider<List<VocabCard>>((ref) async {
//   // Similar to the quiz provider but more targeted for flashcard study
//   final distributionData = await ref.watch(masteryDistributionProvider.future);

//   // Get the cards that need the most attention
//   final wildCards =
//       await ref.watch(cardsByMasteryStatusProvider(MasteryStatus.wild).future);
//   final tamedCards =
//       await ref.watch(cardsByMasteryStatusProvider(MasteryStatus.tamed).future);

//   // Create a balanced study set
//   List<VocabCard> studySet = [];

//   // Aim for 70% wild cards (if available) and 30% tamed cards
//   final wildTarget = 7;
//   final tamedTarget = 3;

//   // Add wild cards
//   if (wildCards.isNotEmpty) {
//     // Shuffle for randomness
//     wildCards.shuffle();
//     final wildToAdd = wildCards.take(wildTarget).toList();
//     studySet.addAll(wildToAdd);
//   }

//   // Add tamed cards
//   if (tamedCards.isNotEmpty) {
//     // Shuffle for randomness
//     tamedCards.shuffle();
//     final tamedToAdd = tamedCards.take(tamedTarget).toList();
//     studySet.addAll(tamedToAdd);
//   }

//   // If we don't have enough cards, get whatever is available
//   if (studySet.isEmpty) {
//     final firebaseService = FirebaseService();
//     final allCards = await firebaseService.fetchVocabulary();

//     // Shuffle and take up to 10
//     allCards.shuffle();
//     studySet = allCards.take(10).toList();
//   }

//   // Final shuffle for the study set
//   studySet.shuffle();

//   return studySet;
// });
