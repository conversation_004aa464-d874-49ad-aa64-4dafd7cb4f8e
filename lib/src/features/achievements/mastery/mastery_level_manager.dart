// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// /// Manages the mastery level system for vocabulary cards
// class MasteryLevelManager {
//   /// Constants for mastery level thresholds
//   static const int wildThreshold = 4; // 0-4 correct answers = wild
//   static const int tamedThreshold = 8; // 5-8 correct answers = tamed
//   static const int masteredThreshold = 10; // 9-10 correct answers = mastered
//   static const int decayDays = 14; // Days after which mastery starts to decay

//   final FirebaseService _firebaseService = FirebaseService();

//   /// Get the mastery status of a card based on correct answers
//   MasteryStatus getMasteryStatus(int correctAnswers) {
//     if (correctAnswers >= 9) {
//       return MasteryStatus.mastered;
//     } else if (correctAnswers >= 5) {
//       return MasteryStatus.tamed;
//     } else {
//       return MasteryStatus.wild;
//     }
//   }

//   /// Update card mastery after a quiz answer
//   Future<VocabCard> updateCardAfterQuizAnswer(
//       String cardId, bool wasCorrect) async {
//     // Get the current card from Firestore
//     final cards = await _firebaseService.fetchVocabulary();
//     final card = cards.firstWhere((c) => c.id == cardId);

//     // Calculate new values
//     final newCorrectAnswers =
//         wasCorrect ? card.correctAnswers + 1 : card.correctAnswers;
//     final cappedCorrectAnswers = newCorrectAnswers > masteredThreshold
//         ? masteredThreshold
//         : newCorrectAnswers;

//     // Calculate new mastery level (1-10 scale)
//     final newMasteryLevel =
//         (cappedCorrectAnswers / masteredThreshold * 10).ceil();

//     // Create the updated card
//     final updatedCard = VocabCard(
//       id: card.id,
//       word: card.word,
//       definition: card.definition,
//       examples: card.examples,
//       type: card.type,
//       pronunciation: card.pronunciation,
//       level: card.level,
//       color: card.color,
//       masteryLevel: newMasteryLevel,
//       correctAnswers: cappedCorrectAnswers,
//       totalAnswers: card.totalAnswers + 1,
//       lastReviewedAt: DateTime.now(),
//     );

//     // Update the card in Firestore
//     await _firebaseService.updateVocabCard(
//       cardId,
//       {
//         'masteryLevel': newMasteryLevel,
//         'correctAnswers': cappedCorrectAnswers,
//         'totalAnswers': updatedCard.totalAnswers,
//         'lastReviewedAt': Timestamp.fromDate(DateTime.now()),
//       },
//     );

//     return updatedCard;
//   }

//   /// Calculate time-based decay for cards that haven't been reviewed recently
//   Future<void> applyMasteryDecay() async {
//     try {
//       // Get all cards
//       final cards = await _firebaseService.fetchVocabulary();
//       final now = DateTime.now();

//       for (final card in cards) {
//         // Skip cards with no mastery
//         if (card.correctAnswers == 0) continue;

//         // Skip cards with no last review date
//         if (card.lastReviewedAt == null) continue;

//         // Calculate days since last review
//         final daysSinceReview = now.difference(card.lastReviewedAt!).inDays;

//         // Apply decay for cards not reviewed recently
//         if (daysSinceReview >= decayDays) {
//           // Calculate decay amount (1 point for every 2 weeks)
//           final decayAmount = (daysSinceReview / decayDays).floor();

//           // Don't let it go below 0
//           int newCorrectAnswers = card.correctAnswers - decayAmount;
//           if (newCorrectAnswers < 0) newCorrectAnswers = 0;

//           // Only update if there's an actual decay
//           if (newCorrectAnswers < card.correctAnswers) {
//             // Calculate new mastery level
//             final newMasteryLevel =
//                 (newCorrectAnswers / masteredThreshold * 10).ceil();

//             // Update the card
//             await _firebaseService.updateVocabCard(
//               card.id,
//               {
//                 'masteryLevel': newMasteryLevel,
//                 'correctAnswers': newCorrectAnswers,
//               },
//             );
//           }
//         }
//       }
//     } catch (e) {
//       debugPrint('Error applying mastery decay: $e');
//     }
//   }

//   /// Calculate the percentage of mastery for a card (0-100%)
//   double calculateMasteryPercentage(int correctAnswers) {
//     return (correctAnswers / masteredThreshold) * 100;
//   }

//   /// Get an appropriate color for displaying mastery level
//   Color getMasteryColor(MasteryStatus status) {
//     switch (status) {
//       case MasteryStatus.wild:
//         return AppColors.red;
//       case MasteryStatus.tamed:
//         return AppColors.orange;
//       case MasteryStatus.mastered:
//         return AppColors.green;
//     }
//   }

//   /// Get a user-friendly description of the mastery status
//   String getMasteryDescription(MasteryStatus status) {
//     switch (status) {
//       case MasteryStatus.wild:
//         return 'Wild - Keep practicing!';
//       case MasteryStatus.tamed:
//         return 'Tamed - Getting better!';
//       case MasteryStatus.mastered:
//         return 'Mastered - Well done!';
//     }
//   }

//   /// Get the user's overall mastery by word type
//   Future<Map<String, double>> getWordTypeMasteryStats() async {
//     // Get all vocabulary cards
//     final cards = await _firebaseService.fetchVocabulary();

//     // Group cards by type
//     Map<String, List<VocabCard>> cardsByType = {};
//     for (final card in cards) {
//       for (final type in card.type) {
//         if (!cardsByType.containsKey(type)) {
//           cardsByType[type] = [];
//         }
//         cardsByType[type]!.add(card);
//       }
//     }

//     // Calculate average mastery percentage for each type
//     Map<String, double> typeMasteryStats = {};
//     cardsByType.forEach((type, typeCards) {
//       if (typeCards.isNotEmpty) {
//         double totalMastery = 0;
//         for (final card in typeCards) {
//           totalMastery += calculateMasteryPercentage(card.correctAnswers);
//         }
//         typeMasteryStats[type] = totalMastery / typeCards.length;
//       } else {
//         typeMasteryStats[type] = 0;
//       }
//     });

//     return typeMasteryStats;
//   }
// }

// lib/src/features/achievements/mastery/mastery_level_manager.dart

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:flutter/material.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// /// Manages the mastery level system for vocabulary cards
// class MasteryLevelManager {
//   /// Constants for mastery level thresholds
//   static const int wildThreshold = 4; // 0-4 correct answers = wild
//   static const int tamedThreshold = 8; // 5-8 correct answers = tamed
//   static const int masteredThreshold = 10; // 9-10 correct answers = mastered
//   static const int decayDays = 14; // Days after which mastery starts to decay

//   final FirebaseService _firebaseService = FirebaseService();

//   /// Cache for cards to reduce redundant Firestore calls
//   final Map<String, VocabCard> _cardCache = {};

//   /// Get the mastery status of a card based on correct answers
//   MasteryStatus getMasteryStatus(int correctAnswers) {
//     if (correctAnswers >= 9) {
//       return MasteryStatus.mastered;
//     } else if (correctAnswers >= 5) {
//       return MasteryStatus.tamed;
//     } else {
//       return MasteryStatus.wild;
//     }
//   }

//   /// Update card mastery after a quiz answer
//   Future<VocabCard> updateCardAfterQuizAnswer(
//       String cardId, bool wasCorrect) async {
//     try {
//       // Try to get the card from cache first
//       VocabCard? card = _cardCache[cardId];

//       // If not in cache, fetch from Firestore
//       if (card == null) {
//         final cards = await _firebaseService.fetchVocabulary();
//         card = cards.firstWhere((c) => c.id == cardId);

//         // Add to cache for future use
//         _cardCache[cardId] = card;
//       }

//       // Calculate new values
//       final newCorrectAnswers =
//           wasCorrect ? card.correctAnswers + 1 : card.correctAnswers;
//       final cappedCorrectAnswers = newCorrectAnswers > masteredThreshold
//           ? masteredThreshold
//           : newCorrectAnswers;

//       // Calculate new mastery level (1-10 scale)
//       final newMasteryLevel =
//           (cappedCorrectAnswers / masteredThreshold * 10).ceil();

//       // Create the updated card
//       final updatedCard = card.copyWith(
//         masteryLevel: newMasteryLevel,
//         correctAnswers: cappedCorrectAnswers,
//         totalAnswers: card.totalAnswers + 1,
//         lastReviewedAt: DateTime.now(),
//       );

//       // Update the card in Firestore
//       final updateData = {
//         'masteryLevel': newMasteryLevel,
//         'correctAnswers': cappedCorrectAnswers,
//         'totalAnswers': updatedCard.totalAnswers,
//         'lastReviewedAt': Timestamp.fromDate(DateTime.now()),
//       };

//       await _firebaseService.updateVocabCard(cardId, updateData);

//       // Update the cache
//       _cardCache[cardId] = updatedCard;

//       return updatedCard;
//     } catch (e) {
//       debugPrint('Error updating card mastery: $e');
//       rethrow; // Re-throw to allow caller to handle the error
//     }
//   }

//   /// Calculate time-based decay for cards that haven't been reviewed recently
//   /// Returns the number of cards that were decayed
//   Future<int> applyMasteryDecay() async {
//     try {
//       // Get all cards
//       final cards = await _firebaseService.fetchVocabulary();
//       final now = DateTime.now();
//       int decayedCards = 0;

//       // Prepare a batch update to minimize Firestore writes
//       final batch = FirebaseFirestore.instance.batch();
//       bool hasBatchOperations = false;

//       for (final card in cards) {
//         // Skip cards with no mastery or no last review date
//         if (card.correctAnswers == 0 || card.lastReviewedAt == null) continue;

//         // Calculate days since last review
//         final daysSinceReview = now.difference(card.lastReviewedAt!).inDays;

//         // Apply decay for cards not reviewed recently
//         if (daysSinceReview >= decayDays) {
//           // Calculate decay amount (1 point for every 2 weeks)
//           final decayAmount = (daysSinceReview / decayDays).floor();

//           // Don't let it go below 0
//           int newCorrectAnswers = card.correctAnswers - decayAmount;
//           if (newCorrectAnswers < 0) newCorrectAnswers = 0;

//           // Only update if there's an actual decay
//           if (newCorrectAnswers < card.correctAnswers) {
//             // Calculate new mastery level
//             final newMasteryLevel =
//                 (newCorrectAnswers / masteredThreshold * 10).ceil();

//             // Add to batch update
//             final cardRef = FirebaseFirestore.instance
//                 .collection('users')
//                 .doc(_firebaseService.userId)
//                 .collection('vocabulary')
//                 .doc(card.id);

//             batch.update(cardRef, {
//               'masteryLevel': newMasteryLevel,
//               'correctAnswers': newCorrectAnswers,
//             });

//             hasBatchOperations = true;
//             decayedCards++;

//             // Update cache
//             _cardCache[card.id] = card.copyWith(
//               masteryLevel: newMasteryLevel,
//               correctAnswers: newCorrectAnswers,
//             );
//           }
//         }
//       }

//       // Execute batch update if there are any operations
//       if (hasBatchOperations) {
//         await batch.commit();
//       }

//       return decayedCards;
//     } catch (e) {
//       debugPrint('Error applying mastery decay: $e');
//       return 0;
//     }
//   }

//   /// Calculate the percentage of mastery for a card (0-100%)
//   double calculateMasteryPercentage(int correctAnswers) {
//     return (correctAnswers / masteredThreshold) * 100;
//   }

//   /// Get an appropriate color for displaying mastery level
//   Color getMasteryColor(MasteryStatus status) {
//     switch (status) {
//       case MasteryStatus.wild:
//         return AppColors.red;
//       case MasteryStatus.tamed:
//         return AppColors.orange;
//       case MasteryStatus.mastered:
//         return AppColors.green;
//     }
//   }

//   /// Get a user-friendly description of the mastery status
//   String getMasteryDescription(MasteryStatus status) {
//     switch (status) {
//       case MasteryStatus.wild:
//         return 'Wild - Keep practicing!';
//       case MasteryStatus.tamed:
//         return 'Tamed - Getting better!';
//       case MasteryStatus.mastered:
//         return 'Mastered - Well done!';
//     }
//   }

//   /// Get the user's overall mastery by word type
//   Future<Map<String, double>> getWordTypeMasteryStats() async {
//     // Get all vocabulary cards
//     final cards = await _firebaseService.fetchVocabulary();

//     // Group cards by type
//     Map<String, List<VocabCard>> cardsByType = {};
//     for (final card in cards) {
//       for (final type in card.type) {
//         if (!cardsByType.containsKey(type)) {
//           cardsByType[type] = [];
//         }
//         cardsByType[type]!.add(card);
//       }
//     }

//     // Calculate average mastery percentage for each type
//     Map<String, double> typeMasteryStats = {};
//     cardsByType.forEach((type, typeCards) {
//       if (typeCards.isNotEmpty) {
//         double totalMastery = 0;
//         for (final card in typeCards) {
//           totalMastery += calculateMasteryPercentage(card.correctAnswers);
//         }
//         typeMasteryStats[type] = totalMastery / typeCards.length;
//       } else {
//         typeMasteryStats[type] = 0;
//       }
//     });

//     return typeMasteryStats;
//   }

//   /// Clear the card cache
//   void clearCache() {
//     _cardCache.clear();
//   }

//   /// Get card counts by mastery status
//   Future<Map<MasteryStatus, int>> getMasteryStatusCounts() async {
//     final cards = await _firebaseService.fetchVocabulary();

//     int wildCount = 0;
//     int tamedCount = 0;
//     int masteredCount = 0;

//     for (final card in cards) {
//       switch (card.getMasteryStatus()) {
//         case MasteryStatus.wild:
//           wildCount++;
//           break;
//         case MasteryStatus.tamed:
//           tamedCount++;
//           break;
//         case MasteryStatus.mastered:
//           masteredCount++;
//           break;
//       }
//     }

//     return {
//       MasteryStatus.wild: wildCount,
//       MasteryStatus.tamed: tamedCount,
//       MasteryStatus.mastered: masteredCount,
//     };
//   }

//   /// Get cards of a specific mastery status
//   Future<List<VocabCard>> getCardsByMasteryStatus(MasteryStatus status) async {
//     final cards = await _firebaseService.fetchVocabulary();
//     return cards.where((card) => card.getMasteryStatus() == status).toList();
//   }

//   /// Update mastery directly (useful for testing or admin tools)
//   Future<void> setCardMastery(String cardId, int correctAnswers) async {
//     // Ensure correct answers are within valid range
//     final cappedCorrectAnswers = correctAnswers.clamp(0, masteredThreshold);

//     // Calculate mastery level (1-10 scale)
//     final masteryLevel = (cappedCorrectAnswers / masteredThreshold * 10).ceil();

//     // Update the card
//     await _firebaseService.updateVocabCard(
//       cardId,
//       {
//         'masteryLevel': masteryLevel,
//         'correctAnswers': cappedCorrectAnswers,
//         'lastReviewedAt': Timestamp.fromDate(DateTime.now()),
//       },
//     );

//     // Remove from cache if present
//     _cardCache.remove(cardId);
//   }
// }

// lib/src/features/achievements/mastery/mastery_level_manager.dart

import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Manager class for handling mastery level functionality
class MasteryLevelManager {
  final FirebaseService _firebaseService = FirebaseService();

  /// Get counts of cards by mastery status
  Future<Map<MasteryStatus, int>> getMasteryStatusCounts() async {
    try {
      // Fetch all cards
      final cards = await _firebaseService.fetchVocabulary();

      // Initialize counters
      int wildCount = 0;
      int tamedCount = 0;
      int masteredCount = 0;

      // Count cards by mastery status
      for (final card in cards) {
        final status = card.getMasteryStatus();
        switch (status) {
          case MasteryStatus.wild:
            wildCount++;
            break;
          case MasteryStatus.tamed:
            tamedCount++;
            break;
          case MasteryStatus.mastered:
            masteredCount++;
            break;
        }
      }

      // Return counts as a map
      return {
        MasteryStatus.wild: wildCount,
        MasteryStatus.tamed: tamedCount,
        MasteryStatus.mastered: masteredCount,
      };
    } catch (e) {
      debugPrint('Error getting mastery status counts: $e');
      // Return empty counts on error
      return {
        MasteryStatus.wild: 0,
        MasteryStatus.tamed: 0,
        MasteryStatus.mastered: 0,
      };
    }
  }

  /// Apply mastery decay for cards not reviewed recently
  /// This reduces mastery levels for words that haven't been reviewed in a while
  Future<void> applyMasteryDecay() async {
    try {
      // Get all cards
      final cards = await _firebaseService.fetchVocabulary();
      final now = DateTime.now();

      for (final card in cards) {
        // Skip cards that are already at the lowest level
        if (card.masteryLevel <= 1) continue;

        // Get the last reviewed date
        final lastReviewed = card.lastReviewedAt;

        // Skip if the card has been reviewed recently or has no review date
        if (lastReviewed == null) continue;

        // Calculate days since last review
        final daysSinceReview = now.difference(lastReviewed).inDays;

        // Apply decay based on days since last review
        int newMasteryLevel = card.masteryLevel;

        if (daysSinceReview > 30) {
          // More than a month: significant decay
          newMasteryLevel = (card.masteryLevel * 0.7).round();
        } else if (daysSinceReview > 14) {
          // More than two weeks: moderate decay
          newMasteryLevel = (card.masteryLevel * 0.9).round();
        }

        // Ensure mastery level is at least 1
        newMasteryLevel = newMasteryLevel.clamp(1, 10);

        // Only update if mastery level has changed
        if (newMasteryLevel < card.masteryLevel) {
          await _firebaseService.updateVocabCard(
            card.id,
            {'masteryLevel': newMasteryLevel},
          );

          debugPrint(
              'Applied decay to ${card.word}: ${card.masteryLevel} -> $newMasteryLevel');
        }
      }

      debugPrint('Mastery decay applied successfully');
    } catch (e) {
      debugPrint('Error applying mastery decay: $e');
    }
  }
}
