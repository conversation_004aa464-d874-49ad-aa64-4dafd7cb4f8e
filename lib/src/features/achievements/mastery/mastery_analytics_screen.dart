// // lib/src/features/achievements/mastery/mastery_analytics_screen.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:fl_chart/fl_chart.dart';
// import 'package:vocadex/src/features/achievements/mastery/mastery_level_manager.dart';
// import 'package:vocadex/src/features/achievements/mastery/mastery_providers.dart';
// import 'package:vocadex/src/features/achievements/mastery/mastery_stats_widget.dart';
// import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
// import 'package:vocadex/src/features/vocab_deck/utils/level_utils.dart';
// import 'package:vocadex/src/services/firebase_service.dart';

// /// Data for the analytics screen
// class MasteryAnalyticsData {
//   final Map<MasteryStatus, int> statusCounts;
//   final Map<String, double> typeMasteryPercentages;
//   final Map<String, int> levelCounts;
//   final List<VocabCard> recentProgress;

//   MasteryAnalyticsData({
//     required this.statusCounts,
//     required this.typeMasteryPercentages,
//     required this.levelCounts,
//     required this.recentProgress,
//   });
// }

// /// Provider for analytics data
// final masteryAnalyticsProvider =
//     FutureProvider<MasteryAnalyticsData>((ref) async {
//   final manager = ref.read(masteryManagerProvider);

//   // Get status counts
//   final statusCounts = await manager.getMasteryStatusCounts();

//   // Get type mastery stats
//   final typeMasteryPercentages = await manager.getWordTypeMasteryStats();

//   // Get all cards
//   final firebaseService = await ref.read(Provider((ref) => FirebaseService()));
//   final allCards = await firebaseService.fetchVocabulary();

//   // Count cards by CEFR level
//   final levelCounts = <String, int>{};
//   for (final card in allCards) {
//     levelCounts[card.level] = (levelCounts[card.level] ?? 0) + 1;
//   }

//   // Get recently progressed cards (cards with recent lastReviewedAt)
//   final now = DateTime.now();
//   final lastWeek = now.subtract(const Duration(days: 7));

//   final recentProgress = allCards
//       .where((card) =>
//           card.lastReviewedAt != null && card.lastReviewedAt!.isAfter(lastWeek))
//       .toList()
//     ..sort((a, b) => (b.lastReviewedAt ?? DateTime(0))
//         .compareTo(a.lastReviewedAt ?? DateTime(0)));

//   // Limit to 10 most recent
//   final recentProgressLimited = recentProgress.take(10).toList();

//   return MasteryAnalyticsData(
//     statusCounts: statusCounts,
//     typeMasteryPercentages: typeMasteryPercentages,
//     levelCounts: levelCounts,
//     recentProgress: recentProgressLimited,
//   );
// });

// /// Screen showing detailed mastery analytics
// class MasteryAnalyticsScreen extends ConsumerWidget {
//   const MasteryAnalyticsScreen({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final analyticsAsync = ref.watch(masteryAnalyticsProvider);

//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('Mastery Analytics'),
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh),
//             onPressed: () {
//               ref.invalidate(masteryAnalyticsProvider);
//             },
//             tooltip: 'Refresh',
//           ),
//         ],
//       ),
//       body: analyticsAsync.when(
//         data: (data) => _buildAnalyticsContent(context, data),
//         loading: () => const Center(child: CircularProgressIndicator()),
//         error: (error, stack) => Center(
//           child: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 const Icon(Icons.error_outline, color: AppColors.red, size: 48),
//                 const SizedBox(height: 16),
//                 Text(
//                   'Error loading analytics: $error',
//                   textAlign: TextAlign.center,
//                 ),
//                 const SizedBox(height: 16),
//                 ElevatedButton(
//                   onPressed: () => ref.refresh(masteryAnalyticsProvider),
//                   child: const Text('Retry'),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildAnalyticsContent(
//       BuildContext context, MasteryAnalyticsData data) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(16.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           // Mastery Status Summary
//           Card(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const Text(
//                     'Mastery Status',
//                     style: TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   _buildPieChart(data.statusCounts),
//                 ],
//               ),
//             ),
//           ),

//           const SizedBox(height: 16),

//           // CEFR Level Distribution
//           Card(
//             child: Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const Text(
//                     'CEFR Level Distribution',
//                     style: TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   SizedBox(
//                     height: 200,
//                     child: _buildBarChart(data.levelCounts),
//                   ),
//                 ],
//               ),
//             ),
//           ),

//           const SizedBox(height: 16),

//           // Mastery by Word Type
//           if (data.typeMasteryPercentages.isNotEmpty)
//             Card(
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const Text(
//                       'Mastery by Word Type',
//                       style: TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     SizedBox(
//                       height: 250,
//                       child: _buildRadarChart(data.typeMasteryPercentages),
//                     ),
//                   ],
//                 ),
//               ),
//             ),

//           const SizedBox(height: 16),

//           // Recent Progress
//           if (data.recentProgress.isNotEmpty)
//             Card(
//               child: Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const Text(
//                       'Recent Progress',
//                       style: TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     ...data.recentProgress.map(_buildRecentProgressItem),
//                   ],
//                 ),
//               ),
//             ),

//           const SizedBox(height: 24),

//           // Tip for improving mastery
//           _buildTip(data),
//         ],
//       ),
//     );
//   }

//   Widget _buildPieChart(Map<MasteryStatus, int> statusCounts) {
//     final total = statusCounts.values.fold(0, (prev, curr) => prev + curr);

//     if (total == 0) {
//       return const Center(
//         child: Padding(
//           padding: EdgeInsets.all(24.0),
//           child: Text(
//             'No vocabulary cards yet',
//             style: TextStyle(fontStyle: FontStyle.italic),
//           ),
//         ),
//       );
//     }

//     final sections = <PieChartSectionData>[];

//     // Mastered section
//     final masteredCount = statusCounts[MasteryStatus.mastered] ?? 0;
//     if (masteredCount > 0) {
//       sections.add(
//         PieChartSectionData(
//           value: masteredCount.toDouble(),
//           title: '${(masteredCount / total * 100).round()}%',
//           color: AppColors.green,
//           radius: 80,
//           titleStyle: const TextStyle(
//             color: AppColors.white,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       );
//     }

//     // Tamed section
//     final tamedCount = statusCounts[MasteryStatus.tamed] ?? 0;
//     if (tamedCount > 0) {
//       sections.add(
//         PieChartSectionData(
//           value: tamedCount.toDouble(),
//           title: '${(tamedCount / total * 100).round()}%',
//           color: AppColors.orange,
//           radius: 80,
//           titleStyle: const TextStyle(
//             color: AppColors.white,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       );
//     }

//     // Wild section
//     final wildCount = statusCounts[MasteryStatus.wild] ?? 0;
//     if (wildCount > 0) {
//       sections.add(
//         PieChartSectionData(
//           value: wildCount.toDouble(),
//           title: '${(wildCount / total * 100).round()}%',
//           color: AppColors.red,
//           radius: 80,
//           titleStyle: const TextStyle(
//             color: AppColors.white,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//       );
//     }

//     return Column(
//       children: [
//         SizedBox(
//           height: 200,
//           child: PieChart(
//             PieChartData(
//               sections: sections,
//               centerSpaceRadius: 30,
//               sectionsSpace: 2,
//             ),
//           ),
//         ),
//         const SizedBox(height: 16),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//           children: [
//             _buildPieLegendItem(
//               color: AppColors.green,
//               label: 'Mastered',
//               count: masteredCount,
//             ),
//             _buildPieLegendItem(
//               color: AppColors.orange,
//               label: 'Tamed',
//               count: tamedCount,
//             ),
//             _buildPieLegendItem(
//               color: AppColors.red,
//               label: 'Wild',
//               count: wildCount,
//             ),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget _buildBarChart(Map<String, int> levelCounts) {
//     // Sort the levels in CEFR order
//     final sortedLevels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']
//         .where((level) => levelCounts.containsKey(level))
//         .toList();

//     if (sortedLevels.isEmpty) {
//       return const Center(
//         child: Text(
//           'No vocabulary cards with CEFR levels',
//           style: TextStyle(fontStyle: FontStyle.italic),
//         ),
//       );
//     }

//     return BarChart(
//       BarChartData(
//         alignment: BarChartAlignment.spaceAround,
//         maxY: levelCounts.values
//                 .fold(0, (max, count) => count > max ? count : max)
//                 .toDouble() *
//             1.2,
//         titlesData: FlTitlesData(
//           leftTitles: AxisTitles(
//             sideTitles: SideTitles(
//               showTitles: true,
//               reservedSize: 30,
//               getTitlesWidget: (value, meta) {
//                 return SideTitleWidget(
//                   axisSide: meta.axisSide,
//                   meta: null,
//                   child: Text(value.toInt().toString()),
//                 );
//               },
//             ),
//           ),
//           rightTitles: AxisTitles(
//             sideTitles: SideTitles(showTitles: false),
//           ),
//           topTitles: AxisTitles(
//             sideTitles: SideTitles(showTitles: false),
//           ),
//           bottomTitles: AxisTitles(
//             sideTitles: SideTitles(
//               showTitles: true,
//               getTitlesWidget: (value, meta) {
//                 if (value.toInt() >= 0 && value.toInt() < sortedLevels.length) {
//                   final level = sortedLevels[value.toInt()];
//                   return SideTitleWidget(
//                     axisSide: meta.axisSide,
//                     child: Text(level),
//                   );
//                 }
//                 return const SizedBox.shrink();
//               },
//             ),
//           ),
//         ),
//         gridData: FlGridData(
//           drawVerticalLine: false,
//           horizontalInterval: 10,
//         ),
//         borderData: FlBorderData(show: false),
//         barGroups: List.generate(
//           sortedLevels.length,
//           (index) {
//             final level = sortedLevels[index];
//             final count = levelCounts[level] ?? 0;
//             final levelColor = LevelUtils.getLevelColor(level);

//             return BarChartGroupData(
//               x: index,
//               barRods: [
//                 BarChartRodData(
//                   toY: count.toDouble(),
//                   color: levelColor,
//                   width: 20,
//                   borderRadius: const BorderRadius.only(
//                     topLeft: Radius.circular(4),
//                     topRight: Radius.circular(4),
//                   ),
//                 ),
//               ],
//             );
//           },
//         ),
//       ),
//     );
//   }

//   Widget _buildRadarChart(Map<String, double> typeMasteryPercentages) {
//     if (typeMasteryPercentages.isEmpty) {
//       return const Center(
//         child: Text(
//           'No vocabulary word types data available',
//           style: TextStyle(fontStyle: FontStyle.italic),
//         ),
//       );
//     }

//     // Sort by percentage (highest first) and take top 6
//     final sortedTypes = typeMasteryPercentages.entries.toList()
//       ..sort((a, b) => b.value.compareTo(a.value));

//     final topTypes = sortedTypes.take(6).toList();

//     return Column(
//       children: [
//         SizedBox(
//           height: 200,
//           child: RadarChart(
//             RadarChartData(
//               radarBorderData: BorderSide(color: AppColors.grey.withAlpha(51),//               tickBorderData: BorderSide(color: AppColors.grey.withAlpha(51),//               gridBorderData: BorderSide(color: AppColors.grey.withAlpha(51),//               radarBackgroundColor: AppColors.transparent,
//               radarShape: RadarShape.polygon,
//               dataSets: [
//                 RadarDataSet(
//                   dataEntries: topTypes
//                       .map((entry) => RadarEntry(value: entry.value / 100))
//                       .toList(),
//                   color: AppColors.blue.withAlpha(51),//                   borderColor: AppColors.blue,
//                   fillColor: AppColors.blue.withAlpha(51),//                   borderWidth: 2,
//                 ),
//               ],
//               titleTextStyle: const TextStyle(
//                 color: AppColors.black,
//                 fontSize: 10,
//               ),
//               // Place the type names around the radar
//               getTitle: (index) {
//                 if (index < topTypes.length) {
//                   final type = topTypes[index].key;
//                   return RadarTitle(
//                     text: type,
//                     angle: 0,
//                   );
//                 }
//                 return const RadarTitle(text: '');
//               },
//               titlePositionPercentageOffset: 0.2,
//               tickCount: 5,
//               ticksTextStyle: const TextStyle(
//                 color: AppColors.black,
//                 fontSize: 8,
//               ),
//             ),
//           ),
//         ),
//         const SizedBox(height: 16),
//         Wrap(
//           spacing: 8,
//           runSpacing: 8,
//           children: topTypes.map((entry) {
//             final percentage = entry.value.toStringAsFixed(1);
//             return Chip(
//               label: Text(
//                 '${entry.key}: $percentage%',
//                 style: const TextStyle(fontSize: 12),
//               ),
//               backgroundColor: AppColors.blue.withAlpha(26),//             );
//           }).toList(),
//         ),
//       ],
//     );
//   }

//   Widget _buildRecentProgressItem(VocabCard card) {
//     final status = card.getMasteryStatus();
//     final statusColor = card.getMasteryStatusColor();

//     return ListTile(
//       contentPadding: EdgeInsets.zero,
//       leading: Container(
//         width: 40,
//         height: 40,
//         decoration: BoxDecoration(
//           color: statusColor.withAlpha(26),//           shape: BoxShape.circle,
//           border: Border.all(color: statusColor),
//         ),
//         child: Center(
//           child: Text(
//             '${card.correctAnswers}',
//             style: TextStyle(
//               color: statusColor,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//         ),
//       ),
//       title: Text(card.word),
//       subtitle: Row(
//         children: [
//           // CEFR level
//           Container(
//             padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
//             decoration: BoxDecoration(
//               color: LevelUtils.getLevelColor(card.level).withAlpha(51),//               borderRadius: BorderRadius.circular(4),
//             ),
//             child: Text(
//               card.level,
//               style: TextStyle(
//                 fontSize: 10,
//                 color: LevelUtils.getLevelColor(card.level),
//               ),
//             ),
//           ),
//           const SizedBox(width: 8),
//           // Status text
//           Text(
//             card.getMasteryStatusText(),
//             style: TextStyle(
//               fontSize: 12,
//               color: statusColor,
//             ),
//           ),
//         ],
//       ),
//       trailing: card.lastReviewedAt != null
//           ? Text(
//               _formatDate(card.lastReviewedAt!),
//               style: TextStyle(
//                 fontSize: 12,
//                 color: AppColors.grey600,
//               ),
//             )
//           : null,
//     );
//   }

//   Widget _buildPieLegendItem({
//     required Color color,
//     required String label,
//     required int count,
//   }) {
//     return Row(
//       children: [
//         Container(
//           width: 12,
//           height: 12,
//           decoration: BoxDecoration(
//             color: color,
//             shape: BoxShape.circle,
//           ),
//         ),
//         const SizedBox(width: 4),
//         Text(
//           '$label ($count)',
//           style: const TextStyle(fontSize: 12),
//         ),
//       ],
//     );
//   }

//   Widget _buildTip(MasteryAnalyticsData data) {
//     final wildCount = data.statusCounts[MasteryStatus.wild] ?? 0;
//     final tamedCount = data.statusCounts[MasteryStatus.tamed] ?? 0;
//     final masteredCount = data.statusCounts[MasteryStatus.mastered] ?? 0;
//     final total = wildCount + tamedCount + masteredCount;

//     // Different tips based on mastery status distribution
//     String tipTitle;
//     String tipContent;
//     IconData icon;
//     Color color;

//     if (total == 0) {
//       tipTitle = 'Start Your Journey';
//       tipContent =
//           'Add vocabulary cards to begin tracking your mastery progress.';
//       icon = Icons.play_arrow;
//       color = AppColors.blue;
//     } else if (wildCount > tamedCount + masteredCount) {
//       tipTitle = 'Tame Your Vocabulary';
//       tipContent =
//           'Focus on reviewing your wild words to move them to tamed status. Taking quizzes regularly will help improve your mastery.';
//       icon = Icons.catching_pokemon;
//       color = AppColors.red;
//     } else if (tamedCount > masteredCount) {
//       tipTitle = 'Aim for Mastery';
//       tipContent =
//           'Keep reviewing your tamed words to achieve full mastery. You\'re making great progress!';
//       icon = Icons.pets;
//       color = AppColors.orange;
//     } else if (masteredCount > 0 && masteredCount >= total * 0.7) {
//       tipTitle = 'Excellent Work!';
//       tipContent =
//           'You\'ve mastered most of your vocabulary. Consider adding more challenging words to expand your vocabulary.';
//       icon = Icons.emoji_events;
//       color = AppColors.green;
//     } else {
//       tipTitle = 'Balance Your Practice';
//       tipContent =
//           'Try to maintain a balanced approach by reviewing words across all mastery levels.';
//       icon = Icons.balance;
//       color = AppColors.purple;
//     }

//     return Card(
//       elevation: 2,
//       color: color.withAlpha(13),//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(12),
//         side: BorderSide(color: color.withAlpha(76),//       ),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Icon(icon, color: color, size: 24),
//             const SizedBox(width: 16),
//             Expanded(
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     tipTitle,
//                     style: TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.bold,
//                       color: color,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   Text(
//                     tipContent,
//                     style: const TextStyle(
//                       fontSize: 14,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   String _formatDate(DateTime date) {
//     final now = DateTime.now();
//     final difference = now.difference(date);

//     if (difference.inDays == 0) {
//       return 'Today';
//     } else if (difference.inDays == 1) {
//       return 'Yesterday';
//     } else if (difference.inDays < 7) {
//       return '${difference.inDays} days ago';
//     } else {
//       return '${date.month}/${date.day}';
//     }
//   }
// }
