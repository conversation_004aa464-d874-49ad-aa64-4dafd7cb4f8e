import 'package:flutter/material.dart';
import 'package:vocadex/src/features/achievements/achievement_model.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// A widget that displays an achievement
class AchievementWidget extends StatelessWidget {
  final Achievement achievement;
  final bool showDetails;
  final VoidCallback? onTap;

  const AchievementWidget({
    super.key,
    required this.achievement,
    this.showDetails = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: achievement.isUnlocked
              ? achievement.getColor()
              : AppColors.textLight.withAlpha(76),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: showDetails ? _buildDetailedView() : _buildCompactView(),
        ),
      ),
    );
  }

  Widget _buildCompactView() {
    return Row(
      children: [
        _buildAchievementIcon(),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                achievement.title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: achievement.isUnlocked
                      ? achievement.getColor()
                      : AppColors.textLight.withAlpha(178),),
              ),
              const SizedBox(height: 2),
              Text(
                achievement.description,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.textLight.withAlpha(178),),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        _buildPointsBadge(),
      ],
    );
  }

  Widget _buildDetailedView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildAchievementIcon(),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: achievement.isUnlocked
                          ? achievement.getColor()
                          : AppColors.textLight.withAlpha(178), // 0.7 opacity = 178 alpha,
                    ),
                  ),
                  if (achievement.wordType != null)
                    Text(
                      'Type: ${achievement.wordType!}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textLight.withAlpha(153), // 0.6 opacity = 153 alpha,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
            _buildPointsBadge(),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          achievement.description,
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textLight.withAlpha(178),),
        ),
        if (achievement.unlockedAt != null) ...[
          const SizedBox(height: 8),
          Text(
            'Unlocked: ${_formatDate(achievement.unlockedAt!)}',
            style: const TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAchievementIcon() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: achievement.isUnlocked
            ? achievement.getColor().withAlpha(26): AppColors.textLight.withAlpha(51),shape: BoxShape.circle,
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.getColor().withAlpha(128): AppColors.textLight.withAlpha(102),width: 2,
        ),
      ),
      child: Icon(
        achievement.getIconData(),
        color: achievement.isUnlocked
            ? achievement.getColor()
            : AppColors.textLight.withAlpha(102),size: 24,
      ),
    );
  }

  Widget _buildPointsBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: achievement.isUnlocked
            ? achievement.getColor().withAlpha(26): AppColors.textLight.withAlpha(51),borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.getColor().withAlpha(128): AppColors.textLight.withAlpha(102),),
      ),
      child: Text(
        '+${achievement.pointsAwarded}',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: achievement.isUnlocked
              ? achievement.getColor()
              : AppColors.textLight.withAlpha(102),),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
}

/// Dialog that appears when an achievement is unlocked
class AchievementUnlockedDialog extends StatelessWidget {
  final Achievement achievement;
  final VoidCallback? onDismiss;

  const AchievementUnlockedDialog({
    super.key,
    required this.achievement,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Achievement unlocked text
            const Text(
              'Achievement Unlocked!',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Achievement icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: achievement.getColor().withAlpha(26),shape: BoxShape.circle,
                border: Border.all(
                  color: achievement.getColor(),
                  width: 3,
                ),
              ),
              child: Icon(
                achievement.getIconData(),
                color: achievement.getColor(),
                size: 40,
              ),
            ),
            const SizedBox(height: 24),

            // Achievement title
            Text(
              achievement.title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: achievement.getColor(),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // Achievement description
            Text(
              achievement.description,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),

            // Points earned
            Container(
              margin: const EdgeInsets.symmetric(vertical: 16),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: achievement.getColor().withAlpha(26),borderRadius: BorderRadius.circular(16),
                border: Border.all(color: achievement.getColor()),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.stars, color: achievement.getColor()),
                  const SizedBox(width: 8),
                  Text(
                    '+${achievement.pointsAwarded} points',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: achievement.getColor(),
                    ),
                  ),
                ],
              ),
            ),

            // Dismiss button
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDismiss?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: achievement.getColor(),
                foregroundColor: AppColors.buttonForegroundLight,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Awesome!'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Show the achievement unlocked dialog
void showAchievementUnlocked(BuildContext context, Achievement achievement,
    {VoidCallback? onDismiss}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => AchievementUnlockedDialog(
      achievement: achievement,
      onDismiss: onDismiss,
    ),
  );
}

/// Show multiple achievements unlocked one after another
void showMultipleAchievementsUnlocked(
    BuildContext context, List<Achievement> achievements,
    {VoidCallback? onAllDismissed}) {
  if (achievements.isEmpty) {
    onAllDismissed?.call();
    return;
  }

  // Show first achievement
  showAchievementUnlocked(
    context,
    achievements.first,
    onDismiss: () {
      // Show the rest of the achievements
      if (achievements.length > 1) {
        showMultipleAchievementsUnlocked(
          context,
          achievements.sublist(1),
          onAllDismissed: onAllDismissed,
        );
      } else {
        // This was the last achievement
        onAllDismissed?.call();
      }
    },
  );
}
