import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'points_manager.dart';

/// Provider for accessing the PointsManager instance
final pointsManagerProvider = Provider<PointsManager>((ref) {
  return PointsManager();
});

/// Provider for the user's current points
final userPointsProvider = FutureProvider<int>((ref) async {
  final pointsManager = ref.watch(pointsManagerProvider);
  return await pointsManager.getUserPoints();
});

/// Provider for the user's points history
final userPointsHistoryProvider = FutureProvider<List<PointsTransaction>>((ref) async {
  final pointsManager = ref.watch(pointsManagerProvider);
  return await pointsManager.getPointsHistory();
});
