import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// Manages the points system for quiz performance
class PointsManager {
  // Points earned for different activities
  static const int pointsForCorrectAnswer = 10;
  static const int pointsForWeeklyGoalCompletion = 50;
  static const int pointsForCardMastery = 100;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Gets the current user's ID
  String get _userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  /// Get the user's current points total
  Future<int> getUserPoints() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data();

      if (userData == null) return 0;

      return (userData['points'] as num?)?.toInt() ?? 0;
    } catch (e) {
      debugPrint('Error getting user points: $e');
      return 0;
    }
  }

  /// Get the user's points history
  Future<List<PointsTransaction>> getPointsHistory({int limit = 20}) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('points_history')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => PointsTransaction.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('Error getting points history: $e');
      return [];
    }
  }

  /// Award points to the user
  Future<void> awardPoints({
    required int amount,
    required String reason,
    String? cardId,
    String? cardType,
  }) async {
    try {
      // Update total points in user document
      await _firestore.collection('users').doc(_userId).update({
        'points': FieldValue.increment(amount),
      });

      // Record the transaction in history
      await _firestore
          .collection('users')
          .doc(_userId)
          .collection('points_history')
          .add({
        'amount': amount,
        'reason': reason,
        'card_id': cardId,
        'card_type': cardType,
        'timestamp': FieldValue.serverTimestamp(),
      });

      // Update type-specific points if a type is provided
      if (cardType != null) {
        await _updateTypePoints(cardType, amount);
      }
    } catch (e) {
      debugPrint('Error awarding points: $e');
    }
  }

  /// Update points for a specific word type
  Future<void> _updateTypePoints(String type, int amount) async {
    try {
      final normalizedType = type.toLowerCase();

      // Get the current type points
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data();

      if (userData == null) return;

      // Get or create type points map
      Map<String, dynamic> typePoints = userData['type_points'] != null
          ? Map<String, dynamic>.from(
              userData['type_points'] as Map<String, dynamic>)
          : {};

      // Update the points for the type
      final currentPoints = typePoints[normalizedType] ?? 0;
      typePoints[normalizedType] = currentPoints + amount;

      // Save the updated type points
      await _firestore.collection('users').doc(_userId).update({
        'type_points': typePoints,
      });
    } catch (e) {
      debugPrint('Error updating type points: $e');
    }
  }

  /// Award points for completing a quiz
  Future<Map<String, dynamic>> awardQuizPoints({
    required List<Map<String, dynamic>> results,
    required String quizTitle,
  }) async {
    int totalPoints = 0;
    Map<String, int> pointsByType = {};

    try {
      // Process each quiz answer
      for (final result in results) {
        final VocabCard card = result['card'] as VocabCard;
        final bool isCorrect = result['isCorrect'] as bool;

        if (isCorrect) {
          // Award points for correct answer
          final int points = pointsForCorrectAnswer;
          totalPoints += points;

          // Track points by word type
          for (final type in card.type) {
            final normalizedType = type.toLowerCase();
            pointsByType[normalizedType] =
                (pointsByType[normalizedType] ?? 0) + points;
          }

          // Add to user's type-specific points
          for (final type in card.type) {
            await _updateTypePoints(type, points);
          }
        }
      }

      // Award the total points
      if (totalPoints > 0) {
        await awardPoints(
          amount: totalPoints,
          reason: 'Completed quiz: $quizTitle',
        );
      }

      return {
        'totalPoints': totalPoints,
        'pointsByType': pointsByType,
      };
    } catch (e) {
      debugPrint('Error awarding quiz points: $e');
      return {
        'totalPoints': 0,
        'pointsByType': {},
      };
    }
  }

  /// Award points for completing a weekly goal
  Future<void> awardWeeklyGoalPoints(String type) async {
    try {
      final normalizedType = type.toLowerCase();

      // Award points
      await awardPoints(
        amount: pointsForWeeklyGoalCompletion,
        reason: 'Completed weekly goal: $normalizedType',
        cardType: normalizedType,
      );
    } catch (e) {
      debugPrint('Error awarding weekly goal points: $e');
    }
  }

  /// Award points for achieving card mastery
  Future<void> awardMasteryPoints(VocabCard card) async {
    try {
      // Award points
      await awardPoints(
        amount: pointsForCardMastery,
        reason: 'Mastered vocabulary: ${card.word}',
        cardId: card.id,
        cardType: card.type.isNotEmpty ? card.type.first.toLowerCase() : null,
      );
    } catch (e) {
      debugPrint('Error awarding mastery points: $e');
    }
  }

  /// Get user's points by word type (for radar chart)
  Future<Map<String, int>> getPointsByWordType() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      final userData = userDoc.data();

      if (userData == null || userData['type_points'] == null) {
        return {
          'verb': 0,
          'noun': 0,
          'adjective': 0,
          'adverb': 0,
          'preposition': 0,
          'pronoun': 0,
          'conjunction': 0,
          'interjection': 0,
        };
      }

      final typePoints = userData['type_points'] as Map<String, dynamic>;

      // Convert to Map<String, int>
      Map<String, int> result = {};
      typePoints.forEach((key, value) {
        result[key] = (value as num).toInt();
      });

      return result;
    } catch (e) {
      debugPrint('Error getting points by word type: $e');
      return {};
    }
  }
}

/// Represents a points transaction in the user's history
class PointsTransaction {
  final String id;
  final int amount;
  final String reason;
  final String? cardId;
  final String? cardType;
  final DateTime timestamp;

  PointsTransaction({
    required this.id,
    required this.amount,
    required this.reason,
    this.cardId,
    this.cardType,
    required this.timestamp,
  });

  factory PointsTransaction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return PointsTransaction(
      id: doc.id,
      amount: (data['amount'] as num).toInt(),
      reason: data['reason'] as String,
      cardId: data['card_id'] as String?,
      cardType: data['card_type'] as String?,
      timestamp: (data['timestamp'] as Timestamp).toDate(),
    );
  }
}
