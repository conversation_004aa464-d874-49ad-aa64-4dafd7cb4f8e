import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/achievements/achievement_manager.dart';
import 'package:vocadex/src/features/achievements/achievement_model.dart';
import 'package:vocadex/src/features/achievements/achievement_widget.dart';

/// Provider for the achievement manager
final achievementManagerProvider = Provider<AchievementManager>((ref) {
  return AchievementManager();
});

/// Provider for all user achievements
final userAchievementsProvider = FutureProvider<List<Achievement>>((ref) async {
  final manager = ref.read(achievementManagerProvider);
  return await manager.getUserAchievements();
});

/// Screen to display all user achievements
class AchievementsScreen extends ConsumerWidget {
  const AchievementsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final achievementsAsync = ref.watch(userAchievementsProvider);

    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text('Achievements'),
      ),
      body: achievementsAsync.when(
        data: (achievements) => _buildAchievementsList(achievements),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading achievements: $error'),
        ),
      ),
    );
  }

  Widget _buildAchievementsList(List<Achievement> achievements) {
    // Sort achievements: unlocked first, then by type
    achievements.sort((a, b) {
      // First by unlock status
      if (a.isUnlocked && !b.isUnlocked) return -1;
      if (!a.isUnlocked && b.isUnlocked) return 1;

      // Then by type
      return a.type.index.compareTo(b.type.index);
    });

    // Group achievements by type
    final groupedAchievements = <AchievementType, List<Achievement>>{};
    for (final achievement in achievements) {
      if (!groupedAchievements.containsKey(achievement.type)) {
        groupedAchievements[achievement.type] = [];
      }
      groupedAchievements[achievement.type]!.add(achievement);
    }

    if (achievements.isEmpty) {
      return const Center(
        child: Text('No achievements available yet.'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAchievementSummary(achievements),
          const SizedBox(height: 24),

          // Generate sections for each achievement type
          ...groupedAchievements.entries.map((entry) {
            return _buildAchievementSection(entry.key, entry.value);
          }),
        ],
      ),
    );
  }

  Widget _buildAchievementSummary(List<Achievement> achievements) {
    final totalAchievements = achievements.length;
    final unlockedAchievements = achievements.where((a) => a.isUnlocked).length;
    final progressPercentage =
        totalAchievements > 0 ? unlockedAchievements / totalAchievements : 0.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const Text(
              'Achievement Progress',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Progress indicator
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: progressPercentage,
                    backgroundColor: AppColors.textLight
                        .withAlpha(51), // 0.2 opacity = 51 alpha,
                    strokeWidth: 12,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColors.warningLight),
                  ),
                ),
                Column(
                  children: [
                    Text(
                      '$unlockedAchievements/$totalAchievements',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Unlocked',
                      style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textLight
                              .withAlpha(128),),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Percentage text
            Text(
              '${(progressPercentage * 100).toInt()}% Complete',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementSection(
      AchievementType type, List<Achievement> achievements) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              Icon(
                _getIconForType(type),
                color: _getColorForType(type),
              ),
              const SizedBox(width: 8),
              Text(
                _getNameForType(type),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getColorForType(type),
                ),
              ),
            ],
          ),
        ),
        ...achievements.map((achievement) => Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: AchievementWidget(
                achievement: achievement,
                showDetails: true,
              ),
            )),
        const SizedBox(height: 16),
      ],
    );
  }

  String _getNameForType(AchievementType type) {
    switch (type) {
      case AchievementType.general:
        return 'General';
      case AchievementType.mastery:
        return 'Mastery';
      case AchievementType.streak:
        return 'Streaks';
      case AchievementType.quiz:
        return 'Quizzes';
      case AchievementType.collection:
        return 'Collection';
      case AchievementType.wordType:
        return 'Word Types';
    }
  }

  IconData _getIconForType(AchievementType type) {
    switch (type) {
      case AchievementType.general:
        return Icons.star;
      case AchievementType.mastery:
        return Icons.military_tech;
      case AchievementType.streak:
        return Icons.local_fire_department;
      case AchievementType.quiz:
        return Icons.quiz;
      case AchievementType.collection:
        return Icons.menu_book;
      case AchievementType.wordType:
        return Icons.text_fields;
    }
  }

  Color _getColorForType(AchievementType type) {
    switch (type) {
      case AchievementType.general:
        return AppColors.warningLight;
      case AchievementType.mastery:
        return AppColors.primaryLight;
      case AchievementType.streak:
        return AppColors.warningLight;
      case AchievementType.quiz:
        return AppColors.infoLight;
      case AchievementType.collection:
        return AppColors.successLight;
      case AchievementType.wordType:
        return AppColors.secondaryLight;
    }
  }
}
