// lib/src/features/auth/presentation/screens/login_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/auth_text_field.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/social_auth_buttons.dart';
import 'package:vocadex/src/features/auth/providers/auth_controller.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/features/auth/providers/login_form_provider.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loginForm = ref.watch(loginFormProvider);

    ref.listen(authStateNotifierProvider, (previous, next) {
      next.whenOrNull(
        authenticated: (_) => context.goNamed(RouteNames.home),
        error: (message) => showFailureToast(
          context,
          title: 'Login Error',
          description: message,
        ),
      );
    });

    // Listen to auth controller for login-specific errors
    ref.listen(authControllerProvider, (previous, next) {
      next.whenOrNull(
        error: (error, stackTrace) {
          if (context.mounted) {
            showFailureToast(
              context,
              title: 'Login Failed',
              description: error.toString(),
            );
          }
        },
      );
    });

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppSizing.screenEdgeInsets,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: AppSizing.spaceXL),

              // Logo or branding
              SizedBox(
                  height: 100,
                  child:
                      Image.asset('assets/images/onboarding/vocadex_logo.png')),

              const SizedBox(height: AppSizing.spaceXL),

              // Email field
              AuthTextField(
                label: 'Email',
                onChanged: ref.read(loginFormProvider.notifier).setEmail,
                keyboardType: TextInputType.emailAddress,
                enabled: !loginForm.isLoading,
              ),

              SizedBox(height: AppSizing.spaceM),
              SizedBox(height: AppSizing.spaceM),
              // Password field
              AuthTextField(
                label: 'Password',
                onChanged: ref.read(loginFormProvider.notifier).setPassword,
                obscureText: true,
                enabled: !loginForm.isLoading,
              ),

              const SizedBox(height: AppSizing.spaceS),

              // Forgot password link
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => context.pushNamed(RouteNames.forgotPassword),
                  child: const Text('Forgot Password?'),
                ),
              ),

              const SizedBox(height: AppSizing.spaceM),

              // Login button
              AuthButton(
                onPressed: loginForm.isLoading
                    ? null
                    : () => _handleLogin(context, ref),
                label: 'Login',
                isLoading: loginForm.isLoading,
              ),

              const SizedBox(height: AppSizing.spaceL),

              // Social auth divider
              // const Row(
              //   children: [
              //     Expanded(child: Divider()),
              //     Padding(
              //       padding: EdgeInsets.symmetric(horizontal: 16),
              //       child: Text('OR'),
              //     ),
              //     Expanded(child: Divider()),
              //   ],
              // ),

              const SizedBox(height: AppSizing.spaceL),

              // Social auth buttons
              const SocialAuthButtons(),

              const SizedBox(height: AppSizing.spaceXL),

              // Sign up link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("Don't have an account?"),
                  TextButton(
                    onPressed: () {
                      // Use go_router to navigate to the signup screen
                      context.goNamed(RouteNames.signup);
                    },
                    child: const Text('Sign Up'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin(BuildContext context, WidgetRef ref) async {
    final formState = ref.read(loginFormProvider);

    // Basic validation
    if (formState.email.trim().isEmpty || formState.password.trim().isEmpty) {
      showFailureToast(
        context,
        title: 'Login Failed',
        description: 'Please enter both email and password',
      );
      return;
    }

    final controller = ref.read(authControllerProvider.notifier);

    ref.read(loginFormProvider.notifier).setLoading(true);
    ref.read(loginFormProvider.notifier).setError(null);

    await controller.signInWithEmailAndPassword(
      email: formState.email.trim(),
      password: formState.password,
    );

    ref.read(loginFormProvider.notifier).setLoading(false);
  }
}
