// lib/src/features/auth/presentation/screens/signup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/auth_text_field.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/social_auth_buttons.dart';
import 'package:vocadex/src/features/auth/providers/auth_controller.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/features/auth/providers/signup_form_provider.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

class SignupScreen extends ConsumerWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final signupForm = ref.watch(signupFormProvider);
    final theme = Theme.of(context);

    // Debug output to help diagnose form validation issues
    final isFormValid = _isFormValid(signupForm);
    debugPrint('Form validation status:');
    debugPrint(
        'Email: ${signupForm.email}, Valid: ${signupForm.email.isNotEmpty && _validateEmail(signupForm.email) == null}');
    debugPrint(
        'Password: ${signupForm.password}, Valid: ${signupForm.password.isNotEmpty && _validatePassword(signupForm.password) == null}');
    debugPrint(
        'Password Match: ${signupForm.confirmPassword == signupForm.password}');
    debugPrint('First Name: ${signupForm.firstName.isNotEmpty}');
    debugPrint('Last Name: ${signupForm.lastName.isNotEmpty}');
    debugPrint('Terms Accepted: ${signupForm.acceptedTerms}');
    debugPrint('Form Valid: $isFormValid');

    // Add auth state listener for redirection
    ref.listen(authStateNotifierProvider, (previous, next) {
      next.whenOrNull(
        authenticated: (_) => context.goNamed(RouteNames.home),
        error: (message) => showFailureToast(
          context,
          title: 'Signup Error',
          description: message,
        ),
      );
    });

    return Material(
      color: Colors.transparent,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: AppSizing.spaceL),
            // First Name field
            AuthTextField(
              label: 'First Name',
              onChanged: ref.read(signupFormProvider.notifier).setFirstName,
              enabled: !signupForm.isLoading,
              validator: _validateName,
            ),

            const SizedBox(height: AppSizing.spaceS),

            // Last Name field
            AuthTextField(
              label: 'Last Name',
              onChanged: ref.read(signupFormProvider.notifier).setLastName,
              enabled: !signupForm.isLoading,
              validator: _validateName,
            ),

            const SizedBox(height: AppSizing.spaceS),

            // Email field
            AuthTextField(
              label: 'Email',
              onChanged: ref.read(signupFormProvider.notifier).setEmail,
              keyboardType: TextInputType.emailAddress,
              enabled: !signupForm.isLoading,
              validator: _validateEmail,
            ),

            const SizedBox(height: AppSizing.spaceS),

            // Password field
            AuthTextField(
              label: 'Password',
              onChanged: (value) {
                ref.read(signupFormProvider.notifier).setPassword(value);
              },
              obscureText: true,
              enabled: !signupForm.isLoading,
              validator: _validatePassword,
            ),

            const SizedBox(height: AppSizing.spaceS),

            // Password strength indicator
            // if (signupForm.password.isNotEmpty)
            //   PasswordStrengthIndicator(password: signupForm.password),

            // Confirm password field
            AuthTextField(
              label: 'Confirm Password',
              onChanged: (value) {
                ref.read(signupFormProvider.notifier).setConfirmPassword(value);
              },
              obscureText: true,
              enabled: !signupForm.isLoading,
              validator: (value) =>
                  _validateConfirmPassword(value, signupForm.password),
            ),

            const SizedBox(height: AppSizing.spaceM),

            // Terms and conditions checkbox
            Row(
              children: [
                Checkbox(
                  value: signupForm.acceptedTerms,
                  onChanged: signupForm.isLoading
                      ? null
                      : (value) => ref
                          .read(signupFormProvider.notifier)
                          .setAcceptedTerms(value ?? false),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () =>
                        launchUrl(Uri.parse('https;//tabemedia.co.uk/terms')),
                    child: const Text(
                      'I agree to the Terms of Service and Privacy Policy',
                      style: TextStyle(
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSizing.spaceL),

            // Sign up button
            AuthButton(
              label: 'Sign Up',
              onPressed: isFormValid && !signupForm.isLoading
                  ? () => _handleSignup(context, ref, signupForm)
                  : null,
              isLoading: signupForm.isLoading,
            ),

            const SizedBox(height: AppSizing.spaceM),

            // Divider with "or" text
            // Row(
            //   children: [
            //     const Expanded(child: Divider()),
            //     Padding(
            //       padding:
            //           const EdgeInsets.symmetric(horizontal: AppSizing.spaceS),
            //       child: Text(
            //         'OR',
            //         style: theme.textTheme.bodySmall?.copyWith(
            //           color: theme.colorScheme.outline,
            //         ),
            //       ),
            //     ),
            //     const Expanded(child: Divider()),
            //   ],
            // ),

            const SizedBox(height: AppSizing.spaceS),

            // Social sign in buttons
            const SocialAuthButtons(),

            // Already have an account? Login
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('Already have an account?'),
                TextButton(
                  onPressed: signupForm.isLoading
                      ? null
                      : () => context.pushNamed(RouteNames.login),
                  child: const Text('Login'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Future<void> _handleGuestSignin(WidgetRef ref) async {
  //   try {
  //     await ref.read(authControllerProvider.notifier).signInAnonymously();
  //   } catch (e) {
  //     // Error handling is done through the auth state listener
  //   }
  // }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }
    if (value.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }

  bool _isFormValid(SignupFormState form) {
    // Check if all required fields are filled
    final isEmailValid =
        form.email.isNotEmpty && _validateEmail(form.email) == null;
    final isPasswordValid =
        form.password.isNotEmpty; // Only check if password is not empty
    final isFirstNameValid = form.firstName.isNotEmpty;
    final isLastNameValid = form.lastName.isNotEmpty;

    return isEmailValid &&
        isPasswordValid &&
        isFirstNameValid &&
        isLastNameValid &&
        form.acceptedTerms;
  }

  Future<void> _handleSignup(
    BuildContext context,
    WidgetRef ref,
    SignupFormState signupForm,
  ) async {
    if (!_isFormValid(signupForm)) return;

    try {
      await ref.read(authControllerProvider.notifier).signUp(
            email: signupForm.email,
            password: signupForm.password,
            firstName: signupForm.firstName,
            lastName: signupForm.lastName,
          );
    } catch (e) {
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Signup Error',
          description: e.toString(),
        );
      }
    }
  }
}
