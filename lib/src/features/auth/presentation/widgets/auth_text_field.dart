// lib/src/features/auth/presentation/widgets/auth_text_field.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/constants/sizing_constants.dart';

class AuthTextField extends StatefulWidget {
  final String label;
  final String? hint;
  final bool obscureText;
  final TextInputType? keyboardType;
  final void Function(String)? onChanged;
  final String? Function(String?)? validator;
  final bool enabled;
  final TextEditingController? controller;

  const AuthTextField({
    super.key,
    required this.label,
    this.hint,
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.validator,
    this.enabled = true,
    this.controller,
  });

  @override
  State<AuthTextField> createState() => _AuthTextFieldState();
}

class _AuthTextFieldState extends State<AuthTextField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextForm<PERSON>ield(
      controller: widget.controller,
      enabled: widget.enabled,
      obscureText: widget.obscureText ? _obscureText : false,
      keyboardType: widget.keyboardType,
      onChanged: widget.onChanged,
      validator: widget.validator,
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint,
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16.0, // Increase vertical padding
          horizontal: 16.0,
        ),
        isDense: true, // Reduces the height a bit if needed
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizing.radiusM),
        ),
        constraints: const BoxConstraints(
          minHeight: 60.0, // Set minimum height
        ),
        suffixIcon: widget.obscureText
            ? IconButton(
                icon: Icon(
                  _obscureText ? Icons.visibility : Icons.visibility_off,
                  color: colorScheme.outline,
                ),
                onPressed: () {
                  setState(() {
                    _obscureText = !_obscureText;
                  });
                },
              )
            : null,
      ),
    );
  }
}
