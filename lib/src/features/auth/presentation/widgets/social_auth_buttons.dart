// lib/src/features/auth/presentation/widgets/social_auth_buttons.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/theme/constants/sizing_constants.dart';
import '../../providers/auth_controller.dart';
import 'auth_button.dart';

class SocialAuthButtons extends ConsumerWidget {
  const SocialAuthButtons({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        if (AppConfig.enableGoogleSignIn) ...[
          AuthButton(
            label: 'Continue with Google',
            onPressed: () => _handleGoogleSignIn(ref),
            isOutlined: true,
            icon: Icons.g_mobiledata,
          ),
          const SizedBox(height: AppSizing.spaceM),
        ],
        if (AppConfig.enableAppleSignIn) ...[
          AuthButton(
            label: 'Continue with Apple',
            onPressed: () => _handleAppleSignIn(ref),
            isOutlined: true,
            icon: Icons.apple,
          ),
          const SizedBox(height: AppSizing.spaceM),
        ],
        if (AppConfig.enableAnonymousSignIn)
          TextButton(
            onPressed: () => _handleAnonymousSignIn(ref),
            child: const Text('Continue as Guest'),
          ),
        // // AuthButton(
        // //   label: 'Continue as Guest',
        // //   onPressed: () => _handleAnonymousSignIn(ref),
        // //   isOutlined: true,
        // //   icon: Icons.person_outline,
        // // ),
      ],
    );
  }

  Future<void> _handleGoogleSignIn(WidgetRef ref) async {
    try {
      await ref.read(authControllerProvider.notifier).signInWithGoogle();
    } catch (e) {
      // Error handling is done in the login screen through auth state listener
    }
  }

  Future<void> _handleAppleSignIn(WidgetRef ref) async {
    // Implement Apple Sign In
  }

  Future<void> _handleAnonymousSignIn(WidgetRef ref) async {
    try {
      await ref.read(authControllerProvider.notifier).signInAnonymously();
    } catch (e) {
      // Error handling is done in the login screen through auth state listener
    }
  }
}
