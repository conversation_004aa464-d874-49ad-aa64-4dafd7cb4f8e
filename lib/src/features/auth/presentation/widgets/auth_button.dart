// lib/src/features/auth/presentation/widgets/auth_button.dart
import 'package:flutter/material.dart';
import '../../../../core/theme/constants/sizing_constants.dart';
import '../../../../core/theme/constants/constants_color.dart';

class AuthButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final IconData? icon;

  const AuthButton({
    super.key,
    required this.label,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDisabled = onPressed == null || isLoading;

    final gradientColors = [
      AppColors.gradientButton1,
      AppColors.gradientButton2,
    ];

    final buttonContent = isLoading
        ? SizedBox(
            height: AppSizing.iconM,
            width: AppSizing.iconM,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isOutlined ? colorScheme.primary : colorScheme.onPrimary,
              ),
            ),
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(icon, size: AppSizing.iconM),
                const SizedBox(width: AppSizing.spaceS),
              ],
              Text(
                label,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      color: isOutlined
                          ? colorScheme.primary
                          : colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
              ),
            ],
          );

    if (isOutlined) {
      return OutlinedButton(
        onPressed: isDisabled ? null : onPressed,
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: AppSizing.spaceM),
          side: BorderSide(
            color: isDisabled
                ? colorScheme.outline.withAlpha(128): colorScheme.primary,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizing.radiusM),
          ),
        ),
        child: buttonContent,
      );
    }

    return Container(
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDisabled
              ? [Colors.grey.shade300, Colors.grey.shade400]
              : gradientColors,
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(AppSizing.radiusM),
        boxShadow: [
          if (!isDisabled)
            BoxShadow(
              color: Colors.black.withAlpha(26),blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isDisabled ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: EdgeInsets.symmetric(vertical: AppSizing.spaceM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizing.radiusM),
          ),
        ),
        child: buttonContent,
      ),
    );
  }
}
