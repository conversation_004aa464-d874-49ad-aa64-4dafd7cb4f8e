// lib/src/features/auth/presentation/screens/splash_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/sizing_constants.dart';

// lib/src/features/auth/presentation/screens/splash_screen.dart

import 'package:vocadex/src/features/auth/service/firebase_auth_service.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    // Add minimum delay for splash screen
    await Future.delayed(const Duration(seconds: 2));
    if (!mounted) return;

    // Check auth state
    final authService = ref.read(firebaseAuthServiceProvider);
    final isLoggedIn = await authService.isUserLoggedIn();

    if (!mounted) return;

    // Navigate based on auth state
    if (isLoggedIn) {
      context.goNamed(RouteNames.home);
    } else {
      context.goNamed(RouteNames.onboarding);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GradientScaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            const Image(
              image: AssetImage('assets/images/onboarding/vocadex_logo.png'),
              width: 100,
              height: 100,
            ),

            const SizedBox(height: AppSizing.spaceL),

            // Loading indicator
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),

            const SizedBox(height: AppSizing.spaceL),

            // Loading text
            Text(
              'Loading...',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// class SplashScreen extends ConsumerStatefulWidget {
//   const SplashScreen({super.key});

//   @override
//   ConsumerState<SplashScreen> createState() => _SplashScreenState();
// }

// class _SplashScreenState extends ConsumerState<SplashScreen> {
//   @override
//   void initState() {
//     super.initState();
//     _checkAuthState();
//   }

//   Future<void> _checkAuthState() async {
//     // Minimum display time for splash screen
//     await Future.delayed(const Duration(seconds: 2));

//     if (!mounted) return;

//     // Check persisted auth state
//     ref.listen(authStateNotifierProvider, (previous, next) {
//       next.whenOrNull(
//         initial: () async {
//           final authService = ref.read(firebaseAuthServiceProvider);
//           final isLoggedIn = await authService.isUserLoggedIn();

//           if (isLoggedIn) {
//             // User is already logged in, go to home
//             if (mounted) {
//               context.goNamed(RouteNames.home);
//             }
//           } else {
//             // User needs to log in
//             if (mounted) {
//               context.goNamed(RouteNames.login);
//             }
//           }
//         },
//         authenticated: (_) => context.goNamed(RouteNames.home),
//         unauthenticated: () => context.goNamed(RouteNames.login),
//         error: (_) => context.goNamed(RouteNames.login),
//       );
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             // App Logo
//             const FlutterLogo(size: 100),

//             SizedBox(height: AppSizing.spaceL),

//             // Loading indicator
//             const CircularProgressIndicator(),

//             SizedBox(height: AppSizing.spaceL),

//             // Loading text
//             const Text(
//               'Loading...',
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),

//             // Error message (if any)
//             Consumer(
//               builder: (context, ref, child) {
//                 return ref.watch(authStateNotifierProvider).maybeWhen(
//                       error: (message) => Padding(
//                         padding: EdgeInsets.all(AppSizing.spaceM),
//                         child: Text(
//                           message,
//                           style: TextStyle(
//                             color: Theme.of(context).colorScheme.error,
//                             fontSize: 14,
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                       orElse: () => const SizedBox.shrink(),
//                     );
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
