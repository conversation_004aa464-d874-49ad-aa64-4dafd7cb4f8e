// lib/features/authentication/data/repositories/auth_repository.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:vocadex/src/core/config/app_config.dart';
import 'package:vocadex/src/features/auth/model/auth_user.dart';
import 'package:vocadex/src/features/auth/repositories/i_auth_repository.dart';
import 'package:vocadex/src/features/auth/service/firebase_auth_service.dart';
import 'package:vocadex/src/services/local_storage_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_repository.g.dart';

@Riverpod(keepAlive: true)
class AuthRepository extends _$AuthRepository implements IAuthRepository {
  late final FirebaseAuthService _authService;

  @override
  Stream<AuthUser?> build() {
    _authService = ref.watch(firebaseAuthServiceProvider);
    return _authService.authStateChanges;
  }

  @override
  Stream<AuthUser?> get authStateChanges => _authService.authStateChanges;

  @override
  Future<AuthUser?> getCurrentUser() async {
    return _authService.getCurrentUser();
  }

  @override
  Future<AuthUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (!AppConfig.enableEmailSignIn) {
      throw UnimplementedError('Email sign in is disabled');
    }
    final user = await _authService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<AuthUser?> signInWithGoogle() async {
    if (!AppConfig.enableGoogleSignIn) {
      throw UnimplementedError('Google sign in is disabled');
    }
    final user = await _authService.signInWithGoogle();
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<AuthUser?> signInAnonymously() async {
    if (!AppConfig.enableAnonymousSignIn) {
      throw UnimplementedError('Anonymous sign in is disabled');
    }
    final user = await _authService.signInAnonymously();
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<AuthUser?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (!AppConfig.enableEmailSignIn) {
      throw UnimplementedError('Email sign up is disabled');
    }
    final user = await _authService.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );
    state = AsyncValue.data(user);
    return user;
  }

  @override
  Future<void> signOut() async {
    await _authService.signOut();
    // Don't manually set state here - let the auth stream handle it
  }

  @override
  Future<void> resetPassword(String email) async {
    if (!AppConfig.enablePasswordReset) {
      throw UnimplementedError('Password reset is disabled');
    }
    await _authService.resetPassword(email);
  }

  @override
  Future<void> reauthenticateWithPassword(String password) async {
    await _authService.reauthenticateWithPassword(password);
  }

  @override
  Future<void> updateEmail(String newEmail) async {
    await _authService.updateEmail(newEmail);
  }

  @override
  Future<void> updatePassword(String newPassword) async {
    await _authService.updatePassword(newPassword);
  }

  @override
  Future<void> deleteAccount() async {
    // Get current user before deletion
    final currentUser = await getCurrentUser();
    if (currentUser == null) {
      throw Exception('No user is currently signed in');
    }

    // Delete user data from Firestore first
    try {
      final firestore = FirebaseFirestore.instance;
      await firestore.collection('users').doc(currentUser.uid).delete();
    } catch (e) {
      print('Error deleting user data from Firestore: $e');
      // Continue with account deletion even if Firestore deletion fails
    }

    // Delete the Firebase Auth account
    await _authService.deleteAccount();

    // Don't manually set state here - let the auth stream handle it
  }

  Future<void> createUserProfile({
    required String uid,
    required String firstName,
    required String lastName,
    required String email,
  }) async {
    try {
      final firestore = FirebaseFirestore.instance;

      print('Creating user profile for $firstName $lastName (uid: $uid)');

      // First check if the document already exists
      final docSnapshot = await firestore.collection('users').doc(uid).get();

      // Create a direct map with all necessary fields to ensure they're properly saved
      final userData = {
        'auth': {
          'uid': uid,
          'email': email,
          'provider': 'email',
        },
        'firstName': firstName,
        'lastName': lastName,
        'name': '$firstName $lastName', // For backward compatibility
        'email': email, // Add email directly for easier querying
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
        'diamonds': 25, // Default starting diamonds
        'languageCode': 'en',
        'themeMode': 'system',
      };

      if (docSnapshot.exists) {
        print('User document already exists, updating it');
        // Document exists, update it with merge
        await firestore.collection('users').doc(uid).set(
              userData,
              SetOptions(merge: true),
            );
      } else {
        print('Creating new user document');
        // Document doesn't exist, create it
        await firestore.collection('users').doc(uid).set(userData);

        // Initialize allocation data, weekly goals, etc.
        await firestore.collection('users').doc(uid).update({
          'allocation_used_today': 0,
          'last_allocation_reset': FieldValue.serverTimestamp(),
          'weekly_goals_last_reset': FieldValue.serverTimestamp(),
          'weekly_goals': {
            'verb': 0,
            'noun': 0,
            'adjective': 0,
            'adverb': 0,
            'preposition': 0,
            'pronoun': 0,
            'conjunction': 0,
            'interjection': 0,
          },
        });
      }

      // Transfer any locally stored data to the user's account
      await _transferLocalDataToFirebase();

      print('User profile creation completed successfully');
    } catch (e) {
      print('Error creating user profile: $e');
      // Don't rethrow - we want to continue even if there's an error
      // The user can still use the app, and we can try to fix data issues later
    }
  }

  /// Transfers locally stored data to Firebase after successful authentication
  Future<void> _transferLocalDataToFirebase() async {
    try {
      // Import the service only where it's used to avoid unused import warnings
      final localStorageService = await _getLocalStorageService();
      await localStorageService.transferDataToFirebase();
    } catch (e) {
      // Use print instead of debugPrint to avoid unused import
      print('Error transferring local data to Firebase: $e');
    }
  }

  /// Helper method to get the LocalStorageService instance
  /// This avoids the unused import warning when the import is only needed in one method
  Future<dynamic> _getLocalStorageService() async {
    // Import dynamically to avoid unused import warnings
    return LocalStorageService();
  }
}
