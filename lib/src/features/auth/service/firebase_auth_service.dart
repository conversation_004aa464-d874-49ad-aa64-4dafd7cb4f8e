import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../model/auth_user.dart';
import 'package:vocadex/src/core/utils/auth_error_utils.dart';

part 'firebase_auth_service.g.dart';

@riverpod
FirebaseAuthService firebaseAuthService(Ref ref) {
  return FirebaseAuthService(firebase_auth.FirebaseAuth.instance);
}

class FirebaseAuthService {
  final firebase_auth.FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;

  FirebaseAuthService(this._auth) : _googleSignIn = GoogleSignIn();

  Stream<AuthUser?> get authStateChanges =>
      _auth.authStateChanges().map(_mapToAuthUser);

  Future<AuthUser?> getCurrentUser() async {
    // This will return the current user from Firebase Auth's persistent storage
    final user = _auth.currentUser;
    return user != null ? _mapToAuthUser(user) : null;
  }

  Future<bool> isUserLoggedIn() async {
    final currentUser = _auth.currentUser;
    // Count both regular and anonymous users as logged in
    return currentUser != null;
  }

  Future<bool> isAnonymousUser() async {
    final currentUser = _auth.currentUser;
    return currentUser != null && currentUser.isAnonymous;
  }

  Future<AuthUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return _mapToAuthUser(result.user);
    } catch (e) {
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }

  Future<AuthUser?> signInWithGoogle() async {
    final googleUser = await _googleSignIn.signIn();
    if (googleUser == null) return null;

    final googleAuth = await googleUser.authentication;
    final credential = firebase_auth.GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );

    // This will be persisted thanks to Firebase Auth's persistence setting
    final result = await _auth.signInWithCredential(credential);
    return _mapToAuthUser(result.user);
  }

  Future<AuthUser?> signInAnonymously() async {
    final result = await _auth.signInAnonymously();
    return _mapToAuthUser(result.user);
  }

  /// Links an anonymous account with email and password to convert it to a permanent account.
  Future<AuthUser?> linkAnonymousAccountWithEmail({
    required String email,
    required String password,
  }) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('No user is currently signed in');
    }

    if (!currentUser.isAnonymous) {
      throw Exception('Only anonymous accounts can be linked with an email');
    }

    // Create an email auth credential
    final credential = firebase_auth.EmailAuthProvider.credential(
      email: email,
      password: password,
    );

    try {
      // Link the anonymous account with the email credential
      final userCredential = await currentUser.linkWithCredential(credential);

      // Set the display name
      await userCredential.user?.updateDisplayName('User');

      // Update email verification status
      await userCredential.user?.sendEmailVerification();

      return _mapToAuthUser(userCredential.user);
    } catch (e) {
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }

  Future<AuthUser?> createUserWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      return _mapToAuthUser(result.user);
    } catch (e) {
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }

  Future<void> signOut() async {
    await Future.wait([
      _auth.signOut(),
      _googleSignIn.signOut(),
    ]);
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }

  AuthUser? _mapToAuthUser(firebase_auth.User? user) {
    if (user == null) return null;

    return AuthUser(
      uid: user.uid,
      email: user.email ?? '',
      photoUrl: user.photoURL,
      isEmailVerified: user.emailVerified,
      provider: _determineProvider(user),
    );
  }

  AuthProvider _determineProvider(firebase_auth.User user) {
    if (user.isAnonymous) return AuthProvider.anonymous;
    if (user.providerData.any((info) => info.providerId == 'google.com')) {
      return AuthProvider.google;
    }
    if (user.providerData.any((info) => info.providerId == 'apple.com')) {
      return AuthProvider.apple;
    }
    return AuthProvider.email;
  }

  Future<void> reauthenticateWithPassword(String password) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    // Check if user has email/password authentication
    final hasEmailProvider =
        user.providerData.any((provider) => provider.providerId == 'password');

    if (!hasEmailProvider) {
      throw Exception(
          'User is not signed in with email/password authentication');
    }

    if (user.email == null || user.email!.isEmpty) {
      throw Exception('User email not found');
    }

    final credential = firebase_auth.EmailAuthProvider.credential(
      email: user.email!,
      password: password,
    );

    try {
      await user.reauthenticateWithCredential(credential);
    } catch (e) {
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }

  Future<void> updateEmail(String newEmail) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    await user.verifyBeforeUpdateEmail(newEmail);
  }

  Future<void> updatePassword(String newPassword) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    await user.updatePassword(newPassword);
  }

  Future<void> deleteAccount() async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not found');

    try {
      debugPrint('Attempting to delete Firebase Auth user: ${user.uid}');
      await user.delete();
      debugPrint('Firebase Auth user deleted successfully');
    } catch (e) {
      debugPrint('Firebase Auth deletion failed: $e');
      // Convert Firebase auth errors to user-friendly messages
      final friendlyMessage = AuthErrorUtils.getErrorMessage(e);
      throw Exception(friendlyMessage);
    }
  }
}
