// lib/src/features/auth/providers/signup_form_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_form_provider.g.dart';
part 'signup_form_provider.freezed.dart';

@freezed
class SignupFormState with _$SignupFormState {
  const factory SignupFormState({
    @Default('') String email,
    @Default('') String password,
    @Default('') String firstName, // Add this line
    @Default('') String lastName,
    @Default('') String confirmPassword,
    @Default(false) bool acceptedTerms,
    @Default(false) bool isLoading,
    String? error,
  }) = _SignupFormState;
}

@riverpod
class SignupForm extends _$SignupForm {
  @override
  SignupFormState build() {
    return const SignupFormState();
  }

  void setEmail(String email) {
    state = state.copyWith(email: email);
  }

  void setFirstName(String firstName) {
    state = state.copyWith(firstName: firstName);
  }

  void setLastName(String lastName) {
    state = state.copyWith(lastName: lastName);
  }

  void setPassword(String password) {
    state = state.copyWith(password: password);
  }

  void setConfirmPassword(String confirmPassword) {
    state = state.copyWith(confirmPassword: confirmPassword);
  }

  void setAcceptedTerms(bool accepted) {
    state = state.copyWith(acceptedTerms: accepted);
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }
}
