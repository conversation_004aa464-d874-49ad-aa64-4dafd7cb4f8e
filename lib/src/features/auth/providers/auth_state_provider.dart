import 'dart:async';
import 'package:vocadex/src/features/auth/model/auth_state.dart';
import 'package:vocadex/src/features/auth/model/auth_user.dart';
import 'package:vocadex/src/features/auth/repositories/auth_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_state_provider.g.dart';

// Update auth_state_provider.dart to be more efficient
@riverpod
class AuthStateNotifier extends _$AuthStateNotifier {
  StreamSubscription<AuthUser?>? _subscription;

  @override
  AuthState build() {
    // Cancel previous subscription if it exists
    ref.onDispose(() {
      _subscription?.cancel();
    });

    // Set initial loading state
    state = const AuthState.loading();

    // Get the Stream directly from the repository instead of the AsyncValue
    final authRepository = ref.read(authRepositoryProvider.notifier);

    // Subscribe to auth state changes more efficiently
    _subscription = authRepository.authStateChanges.listen((user) {
      if (user == null) {
        state = const AuthState.unauthenticated();
      } else {
        state = AuthState.authenticated(user);
      }
    }, onError: (error) => state = AuthState.error(error.toString()));

    return state;
  }
}

// @riverpod
// class AuthStateNotifier extends _$AuthStateNotifier {
//   @override
//   AuthState build() {
//     _listenToAuthChanges();
//     return const AuthState.initial();
//   }

//   void _listenToAuthChanges() {
//     ref.listen(authRepositoryProvider, (previous, next) {
//       next.when(
//         data: (user) {
//           if (user == null) {
//             state = const AuthState.unauthenticated();
//           } else {
//             state = AuthState.authenticated(user);
//           }
//         },
//         error: (error, stack) => state = AuthState.error(error.toString()),
//         loading: () => state = const AuthState.loading(),
//       );
//     });
//   }
// }
