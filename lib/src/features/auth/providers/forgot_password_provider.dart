// lib/src/features/auth/providers/forgot_password_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password_provider.g.dart';
part 'forgot_password_provider.freezed.dart';

@freezed
class ForgotPasswordState with _$ForgotPasswordState {
  const factory ForgotPasswordState({
    @Default('') String email,
    @Default(false) bool isLoading,
    @Default(false) bool isSuccess,
    String? error,
  }) = _ForgotPasswordState;
}

@riverpod
class ForgotPasswordForm extends _$ForgotPasswordForm {
  @override
  ForgotPasswordState build() {
    return const ForgotPasswordState();
  }

  void setEmail(String email) {
    state = state.copyWith(email: email);
  }

  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  void setSuccess(bool isSuccess) {
    state = state.copyWith(isSuccess: isSuccess);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }
}
