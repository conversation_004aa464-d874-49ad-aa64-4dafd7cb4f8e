// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'forgot_password_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$forgotPasswordFormHash() =>
    r'18905969ed037c3ad02e000313c8c9d6b08fa77b';

/// See also [ForgotPasswordForm].
@ProviderFor(ForgotPasswordForm)
final forgotPasswordFormProvider = AutoDisposeNotifierProvider<
    ForgotPasswordForm, ForgotPasswordState>.internal(
  ForgotPasswordForm.new,
  name: r'forgotPasswordFormProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$forgotPasswordFormHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ForgotPasswordForm = AutoDisposeNotifier<ForgotPasswordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
