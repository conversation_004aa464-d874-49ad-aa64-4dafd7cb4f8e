// lib/features/authentication/domain/providers/auth_controller.dart
import 'package:vocadex/src/features/auth/repositories/auth_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/services/analytics_service.dart';

part 'auth_controller.g.dart';

@riverpod
class AuthController extends _$AuthController {
  late final AuthRepository _authRepository;

  @override
  FutureOr<void> build() {
    _authRepository = ref.watch(authRepositoryProvider.notifier);
  }

  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await _authRepository.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Track sign in analytics
      try {
        await AnalyticsService.instance.track('User Signed In', {
          'Sign In Method': 'email',
          'Sign In Date': DateTime.now().toIso8601String(),
        });
      } catch (analyticsError) {
        print('Analytics tracking error: $analyticsError');
      }
    });
  }

  Future<void> signInWithGoogle() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() async {
      await _authRepository.signInWithGoogle();

      // Track Google sign in analytics
      try {
        await AnalyticsService.instance.track('User Signed In', {
          'Sign In Method': 'google',
          'Sign In Date': DateTime.now().toIso8601String(),
        });
      } catch (analyticsError) {
        print('Analytics tracking error: $analyticsError');
      }
    });
  }

  Future<void> signInAnonymously() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _authRepository.signInAnonymously());
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    state = const AsyncLoading();

    try {
      // Create user with email and password
      final authUser = await _authRepository.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Check if authUser is not null and store profile in Firestore
      if (authUser != null) {
        try {
          // Add a small delay to ensure Firebase Auth has fully processed the new user
          await Future.delayed(const Duration(milliseconds: 500));

          // Create the user profile in Firestore
          await _authRepository.createUserProfile(
            uid: authUser.uid,
            firstName: firstName,
            lastName: lastName,
            email: authUser.email,
          );

          // Log success for debugging
          print('User profile created successfully for ${authUser.uid}');

          // Track user sign up analytics
          try {
            await AnalyticsService.instance.identifyUser(
              authUser.uid,
              signUpDate: DateTime.now(),
            );
            await AnalyticsService.instance.track('User Signed Up', {
              'Sign Up Method': 'email',
              'Sign Up Date': DateTime.now().toIso8601String(),
            });
          } catch (analyticsError) {
            print('Analytics tracking error: $analyticsError');
          }
        } catch (profileError) {
          // Log the error but don't rethrow - we still want to consider the signup successful
          // since the Firebase Auth account was created
          print('Error creating user profile: $profileError');
          // We'll still set state to success since the auth account was created
        }
      } else {
        throw Exception('Failed to create user account');
      }

      state = AsyncValue.data(null);
    } catch (e) {
      print('Signup error in AuthController: $e');
      state = AsyncValue.error(e, StackTrace.current);
      rethrow; // Rethrow to allow proper error handling in UI
    }
  }

  Future<void> signOut() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _authRepository.signOut());
  }

  Future<void> resetPassword(String email) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _authRepository.resetPassword(email));
  }

  Future<void> reauthenticateWithPassword(String password) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
        () => _authRepository.reauthenticateWithPassword(password));
  }

  Future<void> updateEmail(String newEmail) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _authRepository.updateEmail(newEmail));
  }

  Future<void> updatePassword(String newPassword) async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(
        () => _authRepository.updatePassword(newPassword));
  }

  Future<void> deleteAccount() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => _authRepository.deleteAccount());
  }
}
