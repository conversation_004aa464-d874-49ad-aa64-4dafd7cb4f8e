import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/features/bottom_navbar/navigation_providers.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/vocab_capture_entry.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

// Provider to control the AddButton menu state
final addButtonExpandedProvider = StateProvider<bool>((ref) => false);

class BottomNavBar extends ConsumerWidget {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navBarIndexProvider);

    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20.0),
        topRight: Radius.circular(20.0),
      ),
      child: BottomAppBar(
        shape: const CircularNotchedRectangle(),
        notchMargin: 8,
        clipBehavior: Clip.antiAlias,
        color: AppColors.black,
        elevation: 8,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildNavBarItem(context, ref, 'home', 'Home', 0, currentIndex),
            _buildNavBarItem(context, ref, 'deck', 'My Deck', 1, currentIndex),
            const SizedBox(width: 60),
            _buildNavBarItem(context, ref, 'barbell', 'Train', 3, currentIndex),
            _buildNavBarItem(context, ref, 'insight', 'Stats', 4, currentIndex),
          ],
        ),
      ),
    );
  }

  Widget _buildNavBarItem(BuildContext context, WidgetRef ref, String iconName,
      String label, int index, int currentIndex) {
    final isSelected = index == currentIndex;

    return GestureDetector(
      onTap: () => _onItemTapped(context, ref, index),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 2),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                'assets/icons/$iconName${isSelected ? '_filled' : ''}.svg',
                colorFilter: ColorFilter.mode(
                  AppColors.white,
                  BlendMode.srcIn,
                ),
                width: 27,
                height: 27,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onItemTapped(BuildContext context, WidgetRef ref, int index) {
    if (index == 2) return; // Skip center button

    ref.read(navBarIndexProvider.notifier).state = index;

    // Navigate based on index
    switch (index) {
      case 0:
        context.goNamed(RouteNames.home);
        break;
      case 1:
        context.goNamed(RouteNames.vocabDeck);
        break;
      case 3:
        context.goNamed(RouteNames.exercises);
        break;
      case 4:
        context.goNamed('stats');
        break;
    }
  }
}

class AddButton extends ConsumerStatefulWidget {
  const AddButton({super.key});

  @override
  ConsumerState<AddButton> createState() => _AddButtonState();
}

class _AddButtonState extends ConsumerState<AddButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the expanded state from the provider
    final expanded = ref.watch(addButtonExpandedProvider);

    // Update the animation controller based on the expanded state
    if (expanded && _controller.status != AnimationStatus.completed) {
      _controller.forward();
    } else if (!expanded && _controller.status != AnimationStatus.dismissed) {
      _controller.reverse();
    }

    final gradient = LinearGradient(
      colors: [
        AppColors.gradientButton1,
        AppColors.gradientButton2,
      ],
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    );
    const menuWidth = 200.0;
    const menuHeight = 180.0;
    const fabSize = 70.0;

    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        alignment: Alignment.bottomCenter,
        clipBehavior: Clip.none,
        fit: StackFit.passthrough,
        children: [
          // Full screen overlay
          if (expanded)
            Positioned.fill(
              child: GestureDetector(
                onTap: _closeMenu,
                child: Container(
                  color: AppColors.black.withAlpha(128),),
              ),
            ),
          // Animated menu
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            bottom: 62,
            left: (MediaQuery.of(context).size.width - menuWidth) / 2,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: expanded ? 1 : 0,
              child: IgnorePointer(
                ignoring: !expanded,
                child: CustomPaint(
                  painter: _MenuWithNotchPainter(
                    colorGradient: gradient,
                    shadow: BoxShadow(
                      color: AppColors.white.withAlpha(66),blurRadius: 18,
                      offset: const Offset(0, 8),
                    ),
                  ),
                  child: SizedBox(
                    width: menuWidth,
                    height: menuHeight,
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: 28,
                        left: 12,
                        right: 12,
                        bottom: fabSize / 2 + 8,
                      ),
                      child: _AddMenu(
                        onClose: _closeMenu,
                        gradient: gradient,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // FAB
          Positioned(
            bottom: 22,
            child: IgnorePointer(
              ignoring: MediaQuery.of(context).viewInsets.bottom > 0,
              child: SizedBox(
                width: fabSize,
                height: fabSize,
                child: Material(
                  color: AppColors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  child: InkWell(
                    onTap: _toggleMenu,
                    customBorder: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                    splashColor: AppColors.white.withAlpha(61),highlightColor: AppColors.white.withAlpha(26),child: Container(
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(16.0)),
                        gradient: gradient,
                      ),
                      child: Center(
                        child: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            expanded ? Icons.close : Icons.add,
                            key: ValueKey(expanded),
                            color: AppColors.black,
                            size: 28,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleMenu() {
    final currentState = ref.read(addButtonExpandedProvider);
    ref.read(addButtonExpandedProvider.notifier).state = !currentState;
  }

  void _closeMenu() {
    ref.read(addButtonExpandedProvider.notifier).state = false;
  }
}

class _MenuWithNotchPainter extends CustomPainter {
  final Gradient colorGradient;
  final BoxShadow shadow;
  _MenuWithNotchPainter({required this.colorGradient, required this.shadow});

  @override
  void paint(Canvas canvas, Size size) {
    final r = 28.0; // Border radius
    final notchRadius = 36.0;
    final notchWidth = 70.0; // match FAB size
    final notchDepth = 18.0; // shallow notch

    final path = Path();
    // Top left
    path.moveTo(0, r);
    path.quadraticBezierTo(0, 0, r, 0);
    path.lineTo(size.width - r, 0);
    path.quadraticBezierTo(size.width, 0, size.width, r);
    path.lineTo(size.width, size.height - notchDepth - r);
    path.quadraticBezierTo(size.width, size.height - notchDepth, size.width - r,
        size.height - notchDepth);
    // Right to notch start
    path.lineTo((size.width + notchWidth) / 2, size.height - notchDepth);
    // Notch (convex, U shape)
    path.quadraticBezierTo(
      size.width / 2, size.height + notchDepth, // control point below the bar
      (size.width - notchWidth) / 2, size.height - notchDepth,
    );
    // Left to bottom left
    path.lineTo(r, size.height - notchDepth);
    path.quadraticBezierTo(
        0, size.height - notchDepth, 0, size.height - notchDepth - r);
    path.close();

    // Draw shadow
    if (shadow.color.opacity > 0) {
      final shadowPaint = Paint()
        ..color = shadow.color
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, shadow.blurRadius)
        ..style = PaintingStyle.fill;
      canvas.save();
      canvas.translate(shadow.offset.dx, shadow.offset.dy);
      canvas.drawPath(path, shadowPaint);
      canvas.restore();
    }

    // Draw gradient
    final paint = Paint()
      ..shader = colorGradient
          .createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _AddMenu extends StatelessWidget {
  final VoidCallback onClose;
  final Gradient gradient;
  const _AddMenu({required this.onClose, required this.gradient});

  @override
  Widget build(BuildContext context) {
    final menuItems = [
      _AddMenuItem(
        label: 'Enter Word',
        icon: Icons.edit,
        onTap: () {
          onClose();
          showManualVocabEntry(context);
        },
      ),
      _AddMenuItem(
        label: 'Choose Image',
        icon: Icons.photo_library,
        onTap: () {
          onClose();
          showGalleryCaptureDialog(context);
        },
      ),
    ];

    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 12,
      children: [
        ...menuItems,
      ],
    );
  }
}

class _AddMenuItem extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback onTap;
  const _AddMenuItem(
      {required this.label, required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: AppColors.black,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
              Icon(
                icon,
                size: 20,
                color: AppColors.black,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
