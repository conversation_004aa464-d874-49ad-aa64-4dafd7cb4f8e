import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';

/// Widget to render a spell-the-word question specifically for onboarding
/// Includes a manual submit button instead of automatic submission
class OnboardingSpellWordWidget extends StatefulWidget {
  final SpellWordQuestion question;
  final Function(String) onAnswerSelected;

  const OnboardingSpellWordWidget({
    super.key,
    required this.question,
    required this.onAnswerSelected,
  });

  @override
  State<OnboardingSpellWordWidget> createState() =>
      _OnboardingSpellWordWidgetState();
}

class _OnboardingSpellWordWidgetState extends State<OnboardingSpellWordWidget>
    with SingleTickerProviderStateMixin {
  // The current state of the spelled word
  List<String> _spelledLetters = [];

  // The available letter options
  late List<String> _availableLetters;

  // Keeps track of which letters are currently in the answer
  late List<bool> _letterUsed;

  // Animation controller for success/error animations
  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;

  // Error state
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    try {
      debugPrint(
          'Initializing OnboardingSpellWordWidget for word: ${widget.question.correctWord}');
      // Validate question data
      if (widget.question.correctWord.isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Invalid question: missing correct word';
        });
        debugPrint('Error: missing correct word');
        return;
      }

      if (widget.question.options.isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'Invalid question: missing letter options';
        });
        debugPrint('Error: missing letter options');
        return;
      }

      // Initialize available letters from the question
      _availableLetters = List<String>.from(widget.question.options);
      _letterUsed = List<bool>.filled(_availableLetters.length, false);

      // Initialize animation controller for feedback animations
      _animationController = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );

      _shakeAnimation = Tween<double>(
        begin: -5.0,
        end: 5.0,
      ).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.elasticIn,
        ),
      );

      _animationController.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _animationController.reverse();
        }
      });

      debugPrint('Successfully initialized OnboardingSpellWordWidget');
    } catch (e) {
      debugPrint('Error initializing OnboardingSpellWordWidget: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Error loading question: $e';
      });

      // Initialize with empty values to avoid null errors
      _availableLetters = [];
      _letterUsed = [];
    }
  }

  @override
  void dispose() {
    if (!_hasError) {
      _animationController.dispose();
    }
    super.dispose();
  }

  // Handle when a letter card is tapped
  void _onLetterCardTapped(String letter, int index) {
    if (!_letterUsed[index]) {
      setState(() {
        _spelledLetters.add(letter);
        _letterUsed[index] = true;
      });

      // Get the correct word and check if we've entered the right number of letters
      final correctWord = widget.question.correctWord;

      // If we've entered enough letters to form the word, check the answer
      if (_spelledLetters.length == correctWord.length) {
        debugPrint(
            'Entered all letters for ${widget.question.correctWord}, auto-submitting after delay');

        // Add a short delay before auto-submitting to let the user see the completed word
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _checkAnswer();
          }
        });
      }
    }
  }

  // Handle when a letter in the answer is tapped (to remove it)
  void _onAnswerLetterTapped(int answerIndex) {
    if (answerIndex >= _spelledLetters.length) return;

    final letter = _spelledLetters[answerIndex];

    // Find the original index of this letter in available letters
    int originalIndex = -1;
    for (int i = 0; i < _availableLetters.length; i++) {
      if (_availableLetters[i] == letter && _letterUsed[i]) {
        originalIndex = i;
        break;
      }
    }

    if (originalIndex >= 0) {
      setState(() {
        _spelledLetters.removeAt(answerIndex);
        _letterUsed[originalIndex] = false;
      });
    }
  }

  // Check if the current spelled word is complete and matches the answer
  void _checkAnswer() {
    final currentWord = _spelledLetters.join('');

    // Check if the word matches the correct word
    final isCorrect = currentWord.toLowerCase().trim() ==
        widget.question.correctWord.toLowerCase().trim();

    // Log the submission
    debugPrint(
        'Submitting spelling answer: "$currentWord", correct answer: "${widget.question.correctWord}", match: $isCorrect');

    // Always call the answer callback
    widget.onAnswerSelected(currentWord);
  }

  @override
  Widget build(BuildContext context) {
    // Show error state if there's an issue
    if (_hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: AppColors.incorrectLight, size: 48),
              const SizedBox(height: 16),
              Text(
                _errorMessage,
                style: TextStyle(
                    color: Theme.of(context).colorScheme.error, fontSize: 18),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Submit a default answer to move past this question
                  widget.onAnswerSelected("error");
                },
                child: const Text('Continue to Next Question'),
              ),
            ],
          ),
        ),
      );
    }

    // Safe access to correctWord with fallback
    final correctWord = widget.question.correctWord.isNotEmpty
        ? widget.question.correctWord
        : "fallback";

    final theme = Theme.of(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Calculate the number of blanks needed for the answer
    final totalBlanks = correctWord.length;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          // Question title
          Text(
            'Spell the word',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppColors.white
                  : AppColors.textLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Question prompt (definition or usage) - Make this prominent
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.backgroundDark.withAlpha(128): AppColors.primaryLight.withAlpha(26),borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.white.withAlpha(76): AppColors.primaryLight.withAlpha(76),),
            ),
            child: Text(
              widget.question.prompt,
              style: TextStyle(
                fontSize: 20,
                height: 1.5,
                color: isDarkMode
                    ? AppColors.white
                    : AppColors.textLight,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),

          // Answer area with blank spaces - Make this more visible
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.backgroundDark.withAlpha(128): AppColors.primaryLight.withAlpha(26),borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.white.withAlpha(76): AppColors.primaryLight.withAlpha(76),width: 1.5,
              ),
            ),
            child: AnimatedBuilder(
              animation: _shakeAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    _animationController.isAnimating
                        ? _shakeAnimation.value
                        : 0,
                    0,
                  ),
                  child: child,
                );
              },
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 8, // horizontal spacing between items
                runSpacing: 16, // vertical spacing between lines
                children: [
                  for (int i = 0; i < totalBlanks; i++)
                    GestureDetector(
                      onTap: i < _spelledLetters.length
                          ? () => _onAnswerLetterTapped(i)
                          : null,
                      child: Container(
                        width: 36,
                        height: 36,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: isDarkMode
                                  ? AppColors.white
                                  : AppColors.primaryLight,
                              width: 2,
                            ),
                          ),
                        ),
                        child: i < _spelledLetters.length
                            ? Center(
                                child: Text(
                                  _spelledLetters[i],
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? AppColors.white
                                        : AppColors.primaryLight,
                                  ),
                                ),
                              )
                            : null,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Flexible spacer that takes minimum space needed
          const SizedBox(height: 20),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Letter options
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 8,
                      runSpacing: 12,
                      children: List.generate(
                        _availableLetters.length,
                        (index) => GestureDetector(
                          onTap: _letterUsed[index]
                              ? null
                              : () => _onLetterCardTapped(
                                  _availableLetters[index], index),
                          child: Container(
                            width: 55,
                            height: 55,
                            decoration: BoxDecoration(
                              color: _letterUsed[index]
                                  ? AppColors.grey.withAlpha(76): isDarkMode
                                      ? AppColors.primaryDark.withAlpha(153): AppColors.primaryLight.withAlpha(204),borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _letterUsed[index]
                                    ? AppColors.grey.withAlpha(76): isDarkMode
                                        ? AppColors.primaryDark.withAlpha(204): AppColors.primaryLight
                                            .withAlpha(102),width: 1.5,
                              ),
                              boxShadow: [
                                if (!_letterUsed[index])
                                  BoxShadow(
                                    color: isDarkMode
                                        ? AppColors.black.withAlpha(76): AppColors.black.withAlpha(26),blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                _availableLetters[index],
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: _letterUsed[index]
                                      ? AppColors.grey
                                      : AppColors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Show a helpful message instead of the submit button
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16.0, horizontal: 32.0),
                    child: Text(
                      _spelledLetters.isEmpty
                          ? 'Tap the letters to spell the word'
                          : _spelledLetters.length < correctWord.length
                              ? 'Keep going...'
                              : 'Checking your answer...',
                      style: TextStyle(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                        color: AppColors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
