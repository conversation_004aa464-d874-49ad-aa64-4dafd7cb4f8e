import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/features/onboarding/services/onboarding_vocab_service.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/capture_success_dialog.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

/// A reusable component for displaying vocabulary previews during onboarding
class OnboardingVocabPreview extends ConsumerStatefulWidget {
  /// List of vocabulary cards to display
  final List<VocabCard> cards;

  /// Optional captured text to display
  final String? capturedText;

  /// Callback when dialog is closed/canceled
  final VoidCallback onClose;

  /// Callback after successful save
  final Function(VocabCard) onSaveSuccess;

  /// Constructor
  const OnboardingVocabPreview({
    super.key,
    required this.cards,
    this.capturedText,
    required this.onClose,
    required this.onSaveSuccess,
  });

  @override
  ConsumerState<OnboardingVocabPreview> createState() =>
      _OnboardingVocabPreviewState();
}

class _OnboardingVocabPreviewState
    extends ConsumerState<OnboardingVocabPreview> {
  /// Selected card index
  int _selectedCardIndex = 0;

  /// Page controller for the carousel
  final PageController _pageController = PageController();

  /// Service for saving vocabulary
  final OnboardingVocabService _saveService = OnboardingVocabService();

  @override
  void initState() {
    super.initState();
    // Make sure the first card is the selected one in the provider
    if (widget.cards.isNotEmpty) {
      ref.read(generatedVocabProvider.notifier).state = widget.cards[0];
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Save the currently selected vocabulary card
  Future<void> _saveVocabulary() async {
    if (widget.cards.isEmpty || _selectedCardIndex >= widget.cards.length) {
      return;
    }

    // Get the selected card
    final selectedCard = widget.cards[_selectedCardIndex];

    // Save the card to the provider to ensure it's available
    ref.read(generatedVocabProvider.notifier).state = selectedCard;

    // Set state to saving
    ref.read(captureStateProvider.notifier).state = CaptureState.saving;

    // Call the save service
    final result =
        await _saveService.saveVocabulary(context, selectedCard, ref);

    if (result['success'] && mounted) {
      // Show success dialog
      showCaptureSuccessDialog(
        context,
        selectedCard.word,
        onDismiss: () {
          // Return the saved card via callback
          widget.onSaveSuccess(selectedCard);
        },
      );
    } else if (mounted) {
      // Show error
      showFailureToast(
        context,
        title: 'Error',
        description: result['message'] ?? 'Failed to save vocabulary',
      );
      // Reset state
      ref.read(captureStateProvider.notifier).state = CaptureState.generated;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we should use carousel (only for multiple cards)
    final bool useCarousel = widget.cards.length > 1;

    // Get the current card to display
    final VocabCard currentCard = widget.cards.isNotEmpty
        ? widget.cards[_selectedCardIndex]
        : widget.cards.first;

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        // Content area - either carousel or single card
        Expanded(
          child: useCarousel
              ? _buildCarousel()
              : SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  physics: const BouncingScrollPhysics(),
                  child: VocabularyCard(card: currentCard),
                ),
        ),

        // Carousel indicators if multiple cards
        if (useCarousel)
          Padding(
            padding: const EdgeInsets.only(top: 4, bottom: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(widget.cards.length, (index) {
                return Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _selectedCardIndex == index
                        ? Theme.of(context).primaryColor
                        : AppColors.grey,
                  ),
                );
              }),
            ),
          ),

        // Action buttons - Save and Cancel
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Row(
            children: [
              // Cancel button
              OutlinedButton.icon(
                onPressed: widget.onClose,
                icon: const Icon(Icons.close),
                label: const Text('Cancel'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
              ),

              const SizedBox(width: 12),

              // Save button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed:
                      ref.watch(captureStateProvider) == CaptureState.saving
                          ? null
                          : _saveVocabulary,
                  icon: const Icon(Icons.save),
                  label: ref.watch(captureStateProvider) == CaptureState.saving
                      ? const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              height: 16,
                              width: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.white),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text('Saving'),
                          ],
                        )
                      : const Text('Save'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build the carousel of vocabulary cards
  Widget _buildCarousel() {
    return PageView.builder(
      controller: _pageController,
      itemCount: widget.cards.length,
      onPageChanged: (index) {
        setState(() {
          _selectedCardIndex = index;
        });
        // Update the provider with the currently visible card
        ref.read(generatedVocabProvider.notifier).state = widget.cards[index];
      },
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: VocabularyCard(card: widget.cards[index]),
        );
      },
    );
  }
}
