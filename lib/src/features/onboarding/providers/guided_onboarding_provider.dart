import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/features/onboarding/models/guided_onboarding_model.dart';
import 'package:vocadex/src/features/onboarding/services/onboarding_challenge_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// The different screens in our guided onboarding flow
enum OnboardingScreen {
  welcome,
  appIntro,
  languageBackground,
  englishLevel,
  levelTest,
  levelConfirmation,
  firstWord,
  challenge,
  challengeResults,
  userProfile,
  createAccount,
  success,
  dashboard
}

/// Provider for the current onboarding screen
final currentOnboardingScreenProvider =
    StateProvider<OnboardingScreen>((ref) => OnboardingScreen.welcome);

/// Provider for the user's onboarding profile
final onboardingProfileProvider = StateProvider<OnboardingUserProfile>(
    (ref) => const OnboardingUserProfile());

/// Provider for the first word generated during onboarding
final firstWordProvider = StateProvider<VocabCard?>((ref) => null);

/// Provider for the list of challenges generated for the onboarding quiz
final onboardingChallengesProvider =
    StateProvider<List<OnboardingChallenge>>((ref) => []);

/// Notifier for managing the onboarding flow
class GuidedOnboardingNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref ref;

  GuidedOnboardingNotifier(this.ref) : super(const AsyncValue.data(null));

  /// Move to the next screen in the onboarding flow
  void goToNextScreen(BuildContext context) {
    final currentScreen = ref.read(currentOnboardingScreenProvider);
    final nextScreen = _getNextScreen(currentScreen);

    if (nextScreen == OnboardingScreen.dashboard) {
      // Onboarding is complete, navigate to the dashboard
      context.goNamed(RouteNames.home);
    } else {
      // Update the current screen
      ref.read(currentOnboardingScreenProvider.notifier).state = nextScreen;
    }
  }

  /// Go to a specific screen in the onboarding flow
  void goToScreen(OnboardingScreen screen) {
    ref.read(currentOnboardingScreenProvider.notifier).state = screen;
  }
  
  /// Move to the previous screen in the onboarding flow
  void goToPreviousScreen(BuildContext context) {
    final currentScreen = ref.read(currentOnboardingScreenProvider);
    final previousScreen = _getPreviousScreen(currentScreen);
    
    // Update the current screen
    ref.read(currentOnboardingScreenProvider.notifier).state = previousScreen;
  }

  /// Update the user's profile information
  void updateProfile(OnboardingUserProfile profile) {
    ref.read(onboardingProfileProvider.notifier).state = profile;
  }

  /// Update a specific field in the user's profile
  void updateProfileField<T>({required String field, required T value}) {
    final profile = ref.read(onboardingProfileProvider);

    final updatedProfile = profile.copyWith(
      name: field == 'name' ? value as String? : profile.name,
      nativeLanguage:
          field == 'nativeLanguage' ? value as String? : profile.nativeLanguage,
      country: field == 'country' ? value as String : profile.country,
      learningDuration:
          field == 'learningDuration' ? value as int : profile.learningDuration,
      englishLevel: field == 'englishLevel'
          ? value as EnglishLevel
          : profile.englishLevel,
      tookLevelTest:
          field == 'tookLevelTest' ? value as bool : profile.tookLevelTest,
    );

    // Update the state without triggering navigation
    ref.read(onboardingProfileProvider.notifier).state = updatedProfile;
    
    // Don't navigate automatically when updating englishLevel
    // Navigation should only happen when the Next button is pressed
    // in the parent widget
  }

  /// Generate challenges for the onboarding quiz
  Future<void> generateOnboardingChallenges() async {
    final service = OnboardingChallengeService();
    final profile = ref.read(onboardingProfileProvider);

    final challenges = await service.generateChallenges(
      level: profile.englishLevel.cefrLevel.isNotEmpty
          ? profile.englishLevel.cefrLevel
          : 'B1',
    );

    ref.read(onboardingChallengesProvider.notifier).state = challenges;
  }

  /// Save the onboarding profile to Firebase
  Future<void> saveOnboardingProfile() async {
    final profile = ref.read(onboardingProfileProvider);
    final firebaseService = FirebaseService();

    // Save onboarding answers
    await firebaseService.saveOnboardingAnswers({
      'name': profile.name,
      'nativeLanguage': profile.nativeLanguage,
      'country': profile.country,
      'learningDurationMonths': profile.learningDuration,
      'englishLevel': profile.englishLevel.cefrLevel,
      'onboardingCompleted': true,
    });
    
    // Update the user's firstName in the main user document
    // This ensures the name appears in the dashboard greeting
    if (profile.name != null && profile.name!.isNotEmpty) {
      try {
        // First update the firstName field
        await firebaseService.updateUserField('firstName', profile.name!);
        
        // Also update the name field for backward compatibility
        await firebaseService.updateUserField('name', profile.name!);
        
        // Get the current user data to ensure it's updated
        final userData = await firebaseService.getUserData();
        debugPrint('Updated user profile - firstName: ${userData?.firstName}');
        
        // If the user has a lastName, update the full name
        if (userData != null && userData.lastName.isNotEmpty) {
          await firebaseService.updateUserField('name', '${profile.name!} ${userData.lastName}');
        }
      } catch (e) {
        debugPrint('Error updating user firstName: $e');
      }
    }
  }

  /// Get the next screen in the onboarding flow
  OnboardingScreen _getNextScreen(OnboardingScreen currentScreen) {
    switch (currentScreen) {
      case OnboardingScreen.welcome:
        return OnboardingScreen.appIntro;
      case OnboardingScreen.appIntro:
        return OnboardingScreen.languageBackground;
      case OnboardingScreen.languageBackground:
        return OnboardingScreen.englishLevel;
      case OnboardingScreen.englishLevel:
        return OnboardingScreen.firstWord;
      case OnboardingScreen.levelTest:
        return OnboardingScreen.firstWord;
      case OnboardingScreen.levelConfirmation:
        return OnboardingScreen.firstWord;
      case OnboardingScreen.firstWord:
        return OnboardingScreen.challenge;
      case OnboardingScreen.challenge:
        return OnboardingScreen.userProfile;
      case OnboardingScreen.challengeResults:
        return OnboardingScreen.userProfile;
      case OnboardingScreen.userProfile:
        return OnboardingScreen.createAccount;
      case OnboardingScreen.createAccount:
        return OnboardingScreen.success;
      case OnboardingScreen.success:
        return OnboardingScreen.dashboard;
      case OnboardingScreen.dashboard:
        return OnboardingScreen.dashboard; // No next screen
    }
  }
  
  /// Get the previous screen in the onboarding flow
  OnboardingScreen _getPreviousScreen(OnboardingScreen currentScreen) {
    switch (currentScreen) {
      case OnboardingScreen.welcome:
        return OnboardingScreen.welcome; // Can't go back from first screen
      case OnboardingScreen.appIntro:
        return OnboardingScreen.welcome;
      case OnboardingScreen.languageBackground:
        return OnboardingScreen.appIntro;
      case OnboardingScreen.englishLevel:
        return OnboardingScreen.languageBackground;
      case OnboardingScreen.levelTest:
        return OnboardingScreen.englishLevel;
      case OnboardingScreen.levelConfirmation:
        return OnboardingScreen.levelTest;
      case OnboardingScreen.firstWord:
        return OnboardingScreen.englishLevel;
      case OnboardingScreen.challenge:
        return OnboardingScreen.firstWord;
      case OnboardingScreen.challengeResults:
        return OnboardingScreen.challenge;
      case OnboardingScreen.userProfile:
        return OnboardingScreen.challenge;
      case OnboardingScreen.createAccount:
        return OnboardingScreen.userProfile;
      case OnboardingScreen.success:
        return OnboardingScreen.createAccount;
      case OnboardingScreen.dashboard:
        return OnboardingScreen.success;
    }
  }
}

/// Provider for the guided onboarding notifier
final guidedOnboardingNotifierProvider =
    StateNotifierProvider<GuidedOnboardingNotifier, AsyncValue<void>>((ref) {
  return GuidedOnboardingNotifier(ref);
});
