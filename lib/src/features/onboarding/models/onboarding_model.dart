// lib/features/onboarding/data/models/onboarding_model.dart

import 'package:freezed_annotation/freezed_annotation.dart';

part 'onboarding_model.freezed.dart';
part 'onboarding_model.g.dart';

@freezed
class OnboardingModel with _$OnboardingModel {
  const factory OnboardingModel({
    required String title,
    required String description,
    required String imagePath,
    @Default('Next') String nextButtonText,
    @Default('Skip') String skipButtonText,
  }) = _OnboardingModel;

  factory OnboardingModel.fromJson(Map<String, dynamic> json) =>
      _$OnboardingModelFromJson(json);
}
