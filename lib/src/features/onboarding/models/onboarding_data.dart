// // lib/features/onboarding/data/models/onboarding_data.dart

// import 'package:vocadex/src/core/theme/constants/image_strings.dart';
// import 'package:vocadex/src/features/localization/app_strings.dart';
// import 'package:vocadex/src/features/onboarding/models/onboarding_model.dart';

// final List<OnboardingModel> onboardingData = [
//   const OnboardingModel(
//     title: AppStrings.onboardingTitle1,
//     description: AppStrings.onboardingDescription1,
//     imagePath: ImageStrings.onboardingImage1,
//   ),
//   const OnboardingModel(
//     title: AppStrings.onboardingTitle2,
//     description: AppStrings.onboardingDescription2,
//     imagePath: ImageStrings.onboardingImage2,
//   ),
//   const OnboardingModel(
//     title: AppStrings.onboardingTitle3,
//     description: AppStrings.onboardingDescription3,
//     imagePath: ImageStrings.onboardingImage3,
//   ),
// ];
