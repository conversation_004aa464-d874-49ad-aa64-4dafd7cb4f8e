/// Language level options for onboarding
enum EnglishLevel { beginner, intermediate, advanced, unknown }

/// Maps EnglishLevel to CEFR levels
extension EnglishLevelExtension on EnglishLevel {
  String get cefrLevel {
    switch (this) {
      case EnglishLevel.beginner:
        return 'A1';
      case EnglishLevel.intermediate:
        return 'B1';
      case EnglishLevel.advanced:
        return 'C1';
      case EnglishLevel.unknown:
        return '';
    }
  }

  String get displayName {
    switch (this) {
      case EnglishLevel.beginner:
        return 'Beginner';
      case EnglishLevel.intermediate:
        return 'Intermediate';
      case EnglishLevel.advanced:
        return 'Advanced';
      case EnglishLevel.unknown:
        return 'Not sure yet';
    }
  }
}

/// User profile collected during onboarding
class OnboardingUserProfile {
  final String? name;
  final String? nativeLanguage;
  final String country;
  final int learningDuration; // in months
  final EnglishLevel englishLevel;
  final bool tookLevelTest;

  const OnboardingUserProfile({
    this.name,
    this.nativeLanguage,
    this.country = '',
    this.learningDuration = 0,
    this.englishLevel = EnglishLevel.unknown,
    this.tookLevelTest = false,
  });

  OnboardingUserProfile copyWith({
    String? name,
    String? nativeLanguage,
    String? country,
    int? learningDuration,
    EnglishLevel? englishLevel,
    bool? tookLevelTest,
  }) {
    return OnboardingUserProfile(
      name: name ?? this.name,
      nativeLanguage: nativeLanguage ?? this.nativeLanguage,
      country: country ?? this.country,
      learningDuration: learningDuration ?? this.learningDuration,
      englishLevel: englishLevel ?? this.englishLevel,
      tookLevelTest: tookLevelTest ?? this.tookLevelTest,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'nativeLanguage': nativeLanguage,
      'country': country,
      'learningDuration': learningDuration,
      'englishLevel': englishLevel.cefrLevel,
      'tookLevelTest': tookLevelTest,
    };
  }
}

/// Challenge data for the sample onboarding quiz
class OnboardingChallenge {
  final String word;
  final String definition;
  final List<String> examples;
  final String level;
  final double frequency;

  const OnboardingChallenge({
    required this.word,
    required this.definition,
    required this.examples,
    required this.level,
    required this.frequency,
  });

  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'definition': definition,
      'examples': examples,
      'level': level,
      'frequency': frequency,
    };
  }

  factory OnboardingChallenge.fromJson(Map<String, dynamic> json) {
    return OnboardingChallenge(
      word: json['word'] as String,
      definition: json['definition'] as String,
      examples: (json['examples'] as List).cast<String>(),
      level: json['level'] as String,
      frequency: json['frequency'] as double,
    );
  }
}
