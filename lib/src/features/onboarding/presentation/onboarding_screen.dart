// // lib/features/onboarding/presentation/pages/onboarding_page.dart

// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:go_router/go_router.dart';
// import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
// import 'package:vocadex/src/features/onboarding/models/onboarding_data.dart';
// import 'package:vocadex/src/features/onboarding/presentation/widgets/onboarding_dot_indicator.dart';
// import 'package:vocadex/src/features/onboarding/presentation/widgets/onboarding_item_widget.dart';
// import 'package:vocadex/src/features/onboarding/providers/onboarding_provider.dart';
// import 'package:vocadex/src/core/theme/constants/constants_color.dart';

// class OnboardingScreen extends ConsumerStatefulWidget {
//   const OnboardingScreen({super.key});

//   @override
//   ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
// }

// class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
//   late final PageController _pageController;

//   @override
//   void initState() {
//     super.initState();
//     _pageController = PageController();
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }

//   void _navigateToAuth() {
//     context.go('/signup');
//   }

//   @override
//   Widget build(BuildContext context) {
//     final currentPage = ref.watch(onboardingControllerProvider);

//     return GradientScaffold(
//       body: SafeArea(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             const SizedBox(
//               height: 20,
//             ),
//             OnboardingDotIndicator(
//               currentPage: currentPage,
//               pageCount: onboardingData.length,
//             ),
//             const SizedBox(
//               height: 40,
//             ),
//             Expanded(
//               child: PageView.builder(
//                 controller: _pageController,
//                 onPageChanged: (index) {
//                   ref
//                       .read(onboardingControllerProvider.notifier)
//                       .setPage(index);
//                 },
//                 itemCount: onboardingData.length,
//                 itemBuilder: (context, index) {
//                   return OnboardingItemWidget(
//                     model: onboardingData[index],
//                   );
//                 },
//               ),
//             ),
//             Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: ElevatedButton(
//                 onPressed: () {
//                   if (currentPage == onboardingData.length - 1) {
//                     _navigateToAuth();
//                   } else {
//                     _pageController.nextPage(
//                       duration: const Duration(milliseconds: 300),
//                       curve: Curves.easeInOut,
//                     );
//                   }
//                 },
//                 child: Text(
//                     currentPage == onboardingData.length - 1
//                         ? 'Get Started'
//                         : 'Next',
//                     style: Theme.of(context)
//                         .textTheme
//                         .bodyLarge
//                         ?.copyWith(color: AppColors.buttonForegroundLight)),
//               ),
//             ),
//             TextButton(
//               onPressed: _navigateToAuth,
//               child: Text(
//                 'Skip',
//                 style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                       color: Theme.of(context).colorScheme.primary,
//                     ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
