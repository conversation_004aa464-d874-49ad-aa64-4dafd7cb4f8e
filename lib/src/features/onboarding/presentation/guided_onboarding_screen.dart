import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_button.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/auth/presentation/signup_screen.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/features/auth/providers/signup_form_provider.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/app_intro_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/challenge_results_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/challenge_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/english_level_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/first_word_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/language_background_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/success_screen.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/welcome_screen.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';

/// The main screen for the guided onboarding experience
class GuidedOnboardingScreen extends ConsumerWidget {
  const GuidedOnboardingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentScreen = ref.watch(currentOnboardingScreenProvider);

    return GradientScaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: currentScreen == OnboardingScreen.welcome
            ? null
            : IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: AppColors.getTextColor(Theme.of(context).brightness),
                ),
                onPressed: () {
                  // Go to previous screen
                  ref
                      .read(guidedOnboardingNotifierProvider.notifier)
                      .goToPreviousScreen(context);
                },
              ),
        actions: [
          // Skip button in app bar
          TextButton(
            onPressed: () {
              // Skip straight to user profile
              ref
                  .read(guidedOnboardingNotifierProvider.notifier)
                  .goToScreen(OnboardingScreen.userProfile);
            },
            child: Text(
              'Skip',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.getTextColor(Theme.of(context).brightness),
                    fontSize: 16,
                  ),
            ),
          ),

          // Progress indicator
        ],
        title: SizedBox(
          width: 200, // Fixed width for the progress indicator
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Stack(
              children: [
                // Background
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.getBackgroundColor(
                        Theme.of(context).brightness),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // Progress
                LayoutBuilder(
                  builder: (context, constraints) {
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      width: constraints.maxWidth * _getProgressValue(ref),
                      height: 8,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        gradient: const LinearGradient(
                          colors: [
                            AppColors.gradientButton1,
                            AppColors.gradientButton2,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: _buildCurrentScreen(context, ref, currentScreen),
      ),
      bottomNavigationBar: _shouldShowBottomBar(currentScreen)
          ? _buildBottomBar(context, ref)
          : null,
    );
  }

  /// Build the current screen based on the onboarding state
  /// Get the progress value based on current screen position
  double _getProgressValue(WidgetRef ref) {
    final currentScreen = ref.watch(currentOnboardingScreenProvider);
    final screens = OnboardingScreen.values;
    final currentIndex = screens.indexOf(currentScreen);
    return (currentIndex + 1) / screens.length;
  }

  Widget _buildCurrentScreen(
      BuildContext context, WidgetRef ref, OnboardingScreen screen) {
    switch (screen) {
      case OnboardingScreen.welcome:
        return const WelcomeScreen();
      case OnboardingScreen.appIntro:
        return const AppIntroScreen();
      case OnboardingScreen.languageBackground:
        return LanguageBackgroundScreen(
          onLanguageSelected: (language) {
            ref
                .read(guidedOnboardingNotifierProvider.notifier)
                .updateProfileField(
                  field: 'nativeLanguage',
                  value: language,
                );
            ref
                .read(guidedOnboardingNotifierProvider.notifier)
                .goToNextScreen(context);
          },
        );
      case OnboardingScreen.englishLevel:
        return const EnglishLevelScreen();
      case OnboardingScreen.firstWord:
        return const FirstWordScreen();
      case OnboardingScreen.challenge:
        return ChallengeScreen(
          onComplete: () {
            ref
                .read(guidedOnboardingNotifierProvider.notifier)
                .goToNextScreen(context);
          },
        );
      case OnboardingScreen.challengeResults:
        return OnboardingQuizResultsScreen(
          onComplete: () {
            ref
                .read(guidedOnboardingNotifierProvider.notifier)
                .goToNextScreen(context);
          },
        );
      case OnboardingScreen.userProfile:
        // Get the user's profile data from onboarding
        final onboardingProfile = ref.watch(onboardingProfileProvider);

        // Pre-populate the signup form with the user's name from onboarding
        if (onboardingProfile.name != null &&
            onboardingProfile.name!.isNotEmpty) {
          // Split the name into first and last name if possible
          final nameParts = onboardingProfile.name!.split(' ');
          if (nameParts.isNotEmpty) {
            // Set the first name
            ref.read(signupFormProvider.notifier).setFirstName(nameParts.first);

            // If there's a last name, set it too
            if (nameParts.length > 1) {
              ref.read(signupFormProvider.notifier).setLastName(
                    nameParts.sublist(1).join(' '),
                  );
            }
          }
        }

        // Use the signup screen with onboarding context
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(height: 24),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppColors.getTextColor(
                            Theme.of(context).brightness),
                      ),
                  children: [
                    const TextSpan(text: 'Create an account to save your '),
                    TextSpan(
                      text: 'progress',
                      style: TextStyle(
                        color: AppColors.getPrimaryColor(
                            Theme.of(context).brightness),
                      ),
                    ),
                    // const TextSpan(text: '?'),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              Text(
                'Create an account to access your vocabulary anywhere',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 16,
                      color:
                          AppColors.getTextColor(Theme.of(context).brightness)
                              .withValues(alpha: 0.7),
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              // Use SignupScreen with listener for successful signup
              Expanded(
                child: Consumer(builder: (context, ref, _) {
                  // Listen for auth state changes to proceed to next screen
                  ref.listen(authStateNotifierProvider, (previous, next) {
                    next.maybeWhen(
                        authenticated: (_) {
                          // When authenticated, move to success screen
                          ref
                              .read(guidedOnboardingNotifierProvider.notifier)
                              .goToNextScreen(context);
                        },
                        orElse: () {});
                  });

                  return const SignupScreen();
                }),
              ),
            ],
          ),
        );
      case OnboardingScreen.success:
        return const SuccessScreen();
      default:
        // For other screens, show a placeholder that advances to the next screen
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Next: ${screen.toString().split('.').last}',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  ref
                      .read(guidedOnboardingNotifierProvider.notifier)
                      .goToNextScreen(context);
                },
                child: Text(
                  'Continue',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.black,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
        );
    }
  }

  /// Determine if we should show the bottom bar for the current screen
  bool _shouldShowBottomBar(OnboardingScreen currentScreen) {
    // Hide bottom bar for screens that handle their own navigation
    return currentScreen == OnboardingScreen.welcome ||
        currentScreen == OnboardingScreen.appIntro ||
        currentScreen == OnboardingScreen.languageBackground ||
        currentScreen == OnboardingScreen.englishLevel;
  }

  /// Determine if we should show the navigation buttons for the current screen
  bool _shouldShowNavigationButtons(OnboardingScreen currentScreen) {
    // Show navigation buttons for early onboarding screens
    return currentScreen == OnboardingScreen.welcome ||
        currentScreen == OnboardingScreen.appIntro ||
        currentScreen == OnboardingScreen.languageBackground ||
        currentScreen == OnboardingScreen.englishLevel;
  }

  /// Build the bottom navigation bar
  Widget _buildBottomBar(
    BuildContext context,
    WidgetRef ref,
  ) {
    // Calculate progress based on current screen

    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36.0),
            child: GradientButton(
              width: double.infinity,
              height: 60,
              text: 'Next',
              onPressed: () {
                ref
                    .read(guidedOnboardingNotifierProvider.notifier)
                    .goToNextScreen(context);
              },
            ),
          ),
          // Bottom bar with Next button only
          // Padding(
          //   padding: const EdgeInsets.all(4.0),
          //   child: SizedBox(
          //     width: double.infinity,
          //     height: 60,
          //     child: Padding(
          //       padding:
          //           const EdgeInsets.symmetric(vertical: 1.0, horizontal: 24),
          //       child: ElevatedButton(
          //         onPressed: () {
          //           ref
          //               .read(guidedOnboardingNotifierProvider.notifier)
          //               .goToNextScreen(context);
          //         },
          //         style: ElevatedButton.styleFrom(
          //           backgroundColor: AppColors.getButtonBackgroundColor(
          //               Theme.of(context).brightness),
          //           foregroundColor: AppColors.getButtonForegroundColor(
          //               Theme.of(context).brightness),
          //           padding: const EdgeInsets.symmetric(vertical: 16.0),
          //           // shape: RoundedRectangleBorder(
          //           //   borderRadius: BorderRadius.circular(12.0),
          //           // ),
          //         ),
          //         child: Text(
          //           'Next',
          //           style: Theme.of(context).textTheme.titleMedium?.copyWith(
          //                 color: AppColors.getButtonForegroundColor(
          //                     Theme.of(context).brightness),
          //                 fontWeight: FontWeight.bold,
          //                 fontSize: 20,
          //               ),
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
