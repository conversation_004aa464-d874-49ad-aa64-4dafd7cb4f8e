// lib/features/onboarding/presentation/widgets/onboarding_item_widget.dart

import 'package:flutter/material.dart';

import 'package:vocadex/src/features/onboarding/models/onboarding_model.dart';

class OnboardingItemWidget extends StatelessWidget {
  final OnboardingModel model;

  const OnboardingItemWidget({
    required this.model,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          Text(
            model.title,
            style: Theme.of(context).textTheme.headlineMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            model.description,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          Image.asset(
            model.imagePath,
            height: MediaQuery.of(context).size.height * 0.4,
            fit: BoxFit.cover,
          ),
        ],
      ),
    );
  }
}
