// lib/features/onboarding/presentation/widgets/onboarding_dot_indicator.dart

import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class OnboardingDotIndicator extends StatelessWidget {
  final int currentPage;
  final int pageCount;

  const OnboardingDotIndicator({
    required this.currentPage,
    required this.pageCount,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSmoothIndicator(
      activeIndex: currentPage,
      count: pageCount,
      effect: SlideEffect(
        activeDotColor: Theme.of(context).primaryColor,
        dotColor: Theme.of(context).disabledColor,
        dotHeight: 4,
        dotWidth: 40,
        spacing: 16,
      ),
      // You can also try other effects:
      // SwapEffect()
      // JumpingDotEffect()
      // ScrollingDotEffect()
      // SlideEffect()
      // ScaleEffect()
      // ExpandingDotsEffect()
    );
  }
}
