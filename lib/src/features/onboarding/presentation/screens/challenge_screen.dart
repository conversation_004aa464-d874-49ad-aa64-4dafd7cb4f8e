import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_button.dart';

import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/card_drag_and_drop_quiz.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/true_false_question.dart';
import 'package:vocadex/src/features/onboarding/widgets/onboarding_spell_word_widget.dart';
import 'package:vocadex/src/services/local_storage_service.dart';

/// Challenge screen for the onboarding flow
class ChallengeScreen extends ConsumerStatefulWidget {
  final VoidCallback onComplete;

  const ChallengeScreen({
    super.key,
    required this.onComplete,
  });

  @override
  ConsumerState<ChallengeScreen> createState() => _ChallengeScreenState();
}

class _ChallengeScreenState extends ConsumerState<ChallengeScreen> {
  bool _isLoading = true;
  int _currentQuestionIndex = 0;
  List<QuizQuestion> _questions = [];
  Quiz? _quiz;
  final LocalStorageService _localStorageService = LocalStorageService();

  // Track screen state
  bool _showIntroScreen = true;
  bool _showFeedback = false;
  bool _showResults = false;
  bool _lastAnswerCorrect = false;
  String _feedbackMessage = '';

  @override
  void initState() {
    super.initState();
    _generateQuestions();
  }

  /// Generate quiz questions from the user's vocabulary word
  Future<void> _generateQuestions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the vocabulary card the user added
      final vocabCard = ref.read(firstWordProvider);

      if (vocabCard == null) {
        throw Exception("No vocabulary card found");
      }

      // Create questions based on the vocabulary card
      _questions = await _createQuestionsFromCard(vocabCard);

      // Create a quiz with these questions
      _quiz = Quiz(
        id: _generateUID(),
        title: 'Practice Challenge',
        type: QuizType.mixed,
        questions: _questions,
        createdAt: DateTime.now(),
        totalQuestions: _questions.length,
      );

      // Save the quiz to local storage
      await _localStorageService.saveTemporaryQuiz(_quiz!);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error generating questions: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Generate a unique ID for a document
  String _generateUID() {
    return 'quiz_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Create different types of questions from a vocabulary card
  Future<List<QuizQuestion>> _createQuestionsFromCard(VocabCard card) async {
    final List<QuizQuestion> questions = [];

    try {
      // Log for debugging
      debugPrint('Creating questions for word: ${card.word}');

      // Create questions in a specific order for easier debugging
      // 1. Create a match definition question with distinct words instead of distractors
      final List<String> distinctOptions = [
        card.word,
        _generateDistinctWord(1),
        _generateDistinctWord(2),
      ];
      distinctOptions.shuffle();

      final matchQuestion = MatchDefinitionQuestion(
        id: _generateUID(),
        card: card,
        definition: card.definition,
        options: distinctOptions,
      );
      questions.add(matchQuestion);
      debugPrint('Created match definition question: ${matchQuestion.id}');

      // 2. Create a true/false question
      final isTrue =
          DateTime.now().millisecond % 2 == 0; // Randomly true or false
      final statement = isTrue
          ? " ${card.definition}"
          : " ${_generateAlternativeDefinition(card.definition)}";

      final trueFalseQuestion = TrueFalseQuestion(
        id: _generateUID(),
        card: card,
        statement: statement,
        answer: isTrue,
      );
      questions.add(trueFalseQuestion);
      debugPrint('Created true/false question: ${trueFalseQuestion.id}');

      // 3. Create a spell-the-word question
      // Prepare letter options for the spelling question
      final List<String> letterOptions =
          _generateBetterSpellingLetterOptions(card.word);

      final spellQuestion = SpellWordQuestion(
        id: _generateUID(),
        card: card,
        prompt: card.definition,
        correctWord: card.word,
        options: letterOptions,
        isDefinition: true,
      );
      questions.add(spellQuestion);
      debugPrint('Created spell word question: ${spellQuestion.id}');

      // Log for debugging
      debugPrint('Successfully created ${questions.length} questions:');
      for (int i = 0; i < questions.length; i++) {
        debugPrint(
            '  Question ${i + 1}: ${questions[i].runtimeType} - ID: ${questions[i].id}');
      }

      return questions;
    } catch (e) {
      // Log error and return any questions created so far
      debugPrint('Error creating questions: $e');
      if (questions.isEmpty) {
        // If no questions were created, add a simple true/false question as fallback
        questions.add(
          TrueFalseQuestion(
            id: _generateUID(),
            card: card,
            statement: "${card.word} is a word in English.",
            answer: true,
          ),
        );
      }
      return questions;
    }
  }

  /// Generate better letter options for a spell-the-word question with no duplicates
  List<String> _generateBetterSpellingLetterOptions(String word) {
    // Convert word to lowercase
    final String normalizedWord = word.toLowerCase().trim();

    // Create a frequency map to count how many times each letter appears in the word
    Map<String, int> letterFrequency = {};
    for (final char in normalizedWord.split('')) {
      letterFrequency[char] = (letterFrequency[char] ?? 0) + 1;
    }

    debugPrint('Letter frequency in "$normalizedWord": $letterFrequency');

    // Create a list with the exact letter frequencies needed
    List<String> requiredLetters = [];
    letterFrequency.forEach((letter, count) {
      for (int i = 0; i < count; i++) {
        requiredLetters.add(letter);
      }
    });

    // Add some distractor letters (3-5 extra letters)
    final List<String> alphabetLetters = 'abcdefghijklmnopqrstuvwxyz'.split('');
    alphabetLetters.shuffle(); // Randomize the alphabet

    // How many random letters to add (3-5)
    final int extraLettersCount = 3 + (DateTime.now().millisecond % 3);
    int addedCount = 0;

    for (final letter in alphabetLetters) {
      // Skip letters that are already in the word to avoid confusion
      if (!letterFrequency.containsKey(letter)) {
        requiredLetters.add(letter);
        addedCount++;
        if (addedCount >= extraLettersCount) break;
      }
    }

    // Shuffle the final list of letters
    requiredLetters.shuffle();

    debugPrint('Word: $normalizedWord, Letter options: $requiredLetters');

    return requiredLetters;
  }

  /// Generate a completely different word instead of a distractor
  String _generateDistinctWord(int seed) {
    final List<String> distinctWords = [
      'apple',
      'happy',
      'yellow',
      'mountain',
      'river',
      'computer',
      'flower',
      'ocean',
      'book',
      'chair',
      'window',
      'music',
      'forest',
      'diamond',
      'journey',
      'cloud',
      'picture',
      'garden',
      'coffee',
      'island'
    ];

    final index = (DateTime.now().millisecond + seed) % distinctWords.length;
    return distinctWords[index];
  }

  /// Generate an alternative definition
  String _generateAlternativeDefinition(String definition) {
    // In a real app, you'd use a more sophisticated approach or an AI service
    final alternatives = [
      "something completely different",
      "the opposite of what you expect",
      "a type of food or beverage",
      "a rare species of animal",
      "a place where people gather",
    ];

    final random = DateTime.now().millisecond % alternatives.length;
    return alternatives[random];
  }

  /// Handle answering a question
  void _handleAnswer(dynamic answer) {
    if (_quiz == null || _currentQuestionIndex >= _questions.length) {
      debugPrint(
          'Invalid quiz state: quiz is null or currentQuestionIndex ($_currentQuestionIndex) >= questions length (${_questions.length})');
      return;
    }

    final currentQuestion = _questions[_currentQuestionIndex];

    // Log the question type and answer for debugging
    debugPrint(
        'Handling answer for question type: ${currentQuestion.runtimeType}, index: $_currentQuestionIndex');
    debugPrint('Answer received: $answer');

    // Special handling for SpellWordQuestion
    if (currentQuestion is SpellWordQuestion && answer is String) {
      // Normalize both strings for comparison (trim whitespace, lowercase)
      final userAnswer = answer.toLowerCase().trim();
      final correctAnswer = currentQuestion.correctWord.toLowerCase().trim();

      // Check if the answer matches the correct word (case insensitive)
      final isCorrect = userAnswer == correctAnswer;

      debugPrint(
          'Spell word comparison: "$userAnswer" vs "$correctAnswer" = $isCorrect');

      // Only update the question state if it wasn't already answered
      if (!currentQuestion.isAnswered) {
        // Update the question's internal state
        currentQuestion.isAnswered = true;
        currentQuestion.isCorrect = isCorrect;

        // Update quiz score
        int newScore = _quiz!.score;
        if (isCorrect) {
          newScore++;
        }

        setState(() {
          _quiz = _quiz!.copyWith(score: newScore);
        });
      } else {
        debugPrint('Question was already answered, not updating score');
      }

      setState(() {
        _showFeedback = true;
        _lastAnswerCorrect = isCorrect;
        _feedbackMessage = isCorrect
            ? 'Correct! You spelled "${currentQuestion.correctWord}" correctly.'
            : 'Not quite right. The correct spelling was: "${currentQuestion.correctWord}"';
      });
    } else {
      // Only update if the question wasn't already answered
      if (!currentQuestion.isAnswered) {
        // Normal handling for other question types
        final isCorrect = currentQuestion.checkAnswer(answer);

        // Update the quiz with the result
        int newScore = _quiz!.score;
        if (isCorrect) {
          newScore++;
        }

        setState(() {
          _quiz = _quiz!.copyWith(score: newScore);
        });
      } else {
        debugPrint('Question was already answered, not updating score');
      }

      // The visual feedback should always be shown, even if score isn't updated
      final isCorrect = currentQuestion.isCorrect;
      setState(() {
        _showFeedback = true;
        _lastAnswerCorrect = isCorrect;
        _feedbackMessage = isCorrect
            ? 'Correct! Well done.'
            : 'Not quite right. The correct answer was: ${_getCorrectAnswer(currentQuestion)}';
      });
    }

    // Show feedback for a moment before continuing
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showFeedback = false;
          // Move to the next question or complete the quiz
          if (_currentQuestionIndex < _questions.length - 1) {
            _currentQuestionIndex++;
            debugPrint(
                'Moving to next question, index: $_currentQuestionIndex, type: ${_questions[_currentQuestionIndex].runtimeType}');
          } else {
            // Quiz is complete
            debugPrint('Quiz complete, moving to results screen');
            _completeQuiz();
          }
        });
      }
    });
  }

  /// Get the correct answer string for feedback
  String _getCorrectAnswer(QuizQuestion question) {
    if (question is MatchDefinitionQuestion) {
      return question.card.word;
    } else if (question is TrueFalseQuestion) {
      return question.answer ? 'True' : 'False';
    } else if (question is SpellWordQuestion) {
      return question.correctWord;
    } else if (question is FillInBlankQuestion) {
      return question.blankWord;
    }
    return 'unknown';
  }

  /// Complete the quiz and save results
  Future<void> _completeQuiz() async {
    if (_quiz == null) return;

    // Verify the score is correct by recounting
    int correctAnswers = 0;
    for (final question in _questions) {
      if (question.isAnswered && question.isCorrect) {
        correctAnswers++;
      }
    }

    // If the score doesn't match the count of correct answers, update it
    if (_quiz!.score != correctAnswers) {
      debugPrint(
          'Fixing incorrect score: was ${_quiz!.score}, should be $correctAnswers');
      _quiz = _quiz!.copyWith(score: correctAnswers);
    }

    debugPrint(
        'Completing quiz with score ${_quiz!.score}/${_questions.length}');

    // Check if all questions have been answered
    bool allQuestionsAnswered = true;
    for (int i = 0; i < _questions.length; i++) {
      if (!_questions[i].isAnswered) {
        allQuestionsAnswered = false;
        debugPrint(
            'Question $i (${_questions[i].runtimeType}) not answered yet');
        // If we're trying to complete but not all questions are answered,
        // make sure we're on the right question
        if (_currentQuestionIndex != i) {
          setState(() {
            _currentQuestionIndex = i;
          });
          debugPrint('Correcting current question index to $i');
        }
        break;
      }
    }

    // Only proceed to results if all questions are answered
    if (!allQuestionsAnswered) {
      debugPrint('Not all questions answered, staying on current question');
      return;
    }

    // Update quiz as completed
    final updatedQuiz = _quiz!.copyWith(isCompleted: true);

    // Save to local storage
    await _localStorageService.saveTemporaryQuiz(updatedQuiz);

    // Show results screen
    setState(() {
      _showResults = true;
    });
  }

  /// Start the challenge after the intro screen
  void _startChallenge() {
    setState(() {
      _showIntroScreen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showIntroScreen) {
      return _buildIntroScreen();
    } else if (_showResults) {
      return _buildResultsScreen();
    } else {
      return _buildChallengeScreen();
    }
  }

  /// Build the intro screen explaining the challenge
  Widget _buildIntroScreen() {
    final vocabCard = ref.read(firstWordProvider);
    final wordText = vocabCard?.word ?? 'your word';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          // Header section
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextColor(Theme.of(context).brightness),
                  ),
              children: [
                const TextSpan(text: 'Train your '),
                WidgetSpan(
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [
                        AppColors.gradientButton1,
                        AppColors.gradientButton2,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ).createShader(bounds),
                    child: Text(
                      'card',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors
                                    .white, // This will be masked by the shader
                              ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Screen title
          const SizedBox(height: 16),
          Text(
            'Let\'s test your knowledge of the word you just added.',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 32),

          // Challenge explanation
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Icon
                  Icon(
                    Icons.psychology,
                    size: 72,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 24),

                  // Challenge overview
                  Text(
                    'Challenge Overview',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // Explanation card
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withAlpha(13),blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildChallengeTypeItem(
                          icon: Icons.drag_indicator,
                          title: 'Drag & Drop',
                          description:
                              'Match "${wordText}" with its definition',
                        ),
                        const Divider(),
                        _buildChallengeTypeItem(
                          icon: Icons.swipe,
                          title: 'True or False',
                          description: 'Swipe to identify correct statements',
                        ),
                        const Divider(),
                        _buildChallengeTypeItem(
                          icon: Icons.text_fields,
                          title: 'Spelling',
                          description: 'Spell "${wordText}" correctly',
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Tips section
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primaryContainer
                          .withAlpha(76),borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Tips',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Take your time to think about each question\n'
                          '• Read the definition carefully\n'
                          '• Don\'t worry if you make a mistake - this is just practice!',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Start button
          GradientButton(
            width: double.infinity,
            height: 60,
            onPressed: _startChallenge,
            text: 'Start Challenge',
          ),
          // SizedBox(
          //   width: double.infinity,
          //   height: 60,
          //   child: ElevatedButton(
          //     onPressed: _startChallenge,
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor:
          //           Theme.of(context).brightness == Brightness.light
          //               ? AppColors.primaryLight
          //               : AppColors.primaryDark,
          //       padding: const EdgeInsets.symmetric(vertical: 16.0),
          //     ),
          //     child: Text(
          //       'Start Challenge',
          //       style: Theme.of(context).textTheme.titleMedium?.copyWith(
          //             color: AppColors.white,
          //             fontWeight: FontWeight.bold,
          //             fontSize: 20,
          //           ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  /// Build a challenge type item for the intro screen
  Widget _buildChallengeTypeItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            // decoration: BoxDecoration(
            //   color: Theme.of(context).colorScheme.primaryContainer,
            //   borderRadius: BorderRadius.circular(8),
            // ),
            child: Icon(
              icon,
              color: Theme.of(context).brightness == Brightness.light
                  ? AppColors.primaryLight
                  : AppColors.primaryDark,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build the main challenge screen
  Widget _buildChallengeScreen() {
    return Column(
      children: [
        // Quiz content - full screen
        Expanded(
          child: _buildQuizContent(),
        ),
      ],
    );
  }

  /// Build the quiz content based on loading state
  Widget _buildQuizContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Preparing your challenge...'),
          ],
        ),
      );
    }

    if (_questions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to create challenge questions.',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _generateQuestions,
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    // Show current question
    final currentQuestion = _questions[_currentQuestionIndex];
    debugPrint(
        'Building question widget for index: $_currentQuestionIndex, type: ${currentQuestion.runtimeType}');

    return Stack(
      children: [
        Column(
          children: [
            // Progress indicator
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Question ${_currentQuestionIndex + 1} of ${_questions.length}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: (_currentQuestionIndex + 1) / _questions.length,
                    backgroundColor: AppColors.grey,
                    borderRadius: BorderRadius.circular(10),
                    minHeight: 8,
                  ),
                ],
              ),
            ),

            // Question - use appropriate widget based on question type
            Expanded(
              child: _showFeedback
                  ? _buildFeedbackWidget()
                  : _buildQuestionWidget(currentQuestion),
            ),
          ],
        ),
      ],
    );
  }

  /// Build feedback widget after answering a question
  Widget _buildFeedbackWidget() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      // decoration: BoxDecoration(
      //   color: _lastAnswerCorrect ? AppColors.successLightest : AppColors.errorLight,
      //   borderRadius: BorderRadius.circular(16),
      //   border: Border.all(
      //     color:
      //         _lastAnswerCorrect ? AppColors.successLight : AppColors.errorLightest,
      //     width: 2,
      //   ),
      // ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _lastAnswerCorrect ? Icons.check_circle : Icons.cancel,
            size: 64,
            color:
                _lastAnswerCorrect ? AppColors.green : AppColors.incorrectLight,
          ),
          const SizedBox(height: 24),
          Text(
            _lastAnswerCorrect ? 'Correct!' : 'Oops!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: _lastAnswerCorrect
                  ? AppColors.successLight
                  : AppColors.failureLight,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _feedbackMessage,
            style: TextStyle(
              fontSize: 18,
              color: _lastAnswerCorrect
                  ? AppColors.successDark
                  : AppColors.failureDark,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Text(
            'Moving to the next question...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the appropriate question widget based on question type
  Widget _buildQuestionWidget(QuizQuestion question) {
    debugPrint('Building widget for question type: ${question.runtimeType}');

    if (question is MatchDefinitionQuestion) {
      // Convert to CardDragAndDropQuiz format
      final dragDropQuestion = CardDragAndDropQuizQuestion(
        questionText: question.definition,
        options: question.options,
        correctAnswer: question.card.word,
      );

      return CardDragAndDropQuiz(
        question: dragDropQuestion,
        onCheck: _handleAnswer,
      );
    } else if (question is TrueFalseQuestion) {
      return TrueFalseQuestionWidget(
        question: question,
        onAnswerSelected: (answer) => _handleAnswer(answer),
      );
    } else if (question is SpellWordQuestion) {
      // Use the onboarding-specific spell word widget with manual submit button
      debugPrint(
          'Building OnboardingSpellWordWidget with correct word: ${question.correctWord}');
      debugPrint('Letter options: ${question.options}');

      // Safety check - ensure the question state is correctly set
      if (question.isAnswered) {
        debugPrint(
            'WARNING: SpellWordQuestion is already marked as answered before user interaction!');
        // Reset the answered state to allow user input
        question.isAnswered = false;
      }

      return OnboardingSpellWordWidget(
        key: ValueKey('spell_word_${_currentQuestionIndex}_${question.id}'),
        question: question,
        onAnswerSelected: (answer) => _handleAnswer(answer),
      );
    } else {
      return Center(
        child: Text('Unsupported question type: ${question.runtimeType}'),
      );
    }
  }

  /// Build results screen for the onboarding challenge
  Widget _buildResultsScreen() {
    if (_quiz == null) {
      return const Center(child: Text('No quiz data available'));
    }

    // Calculate percentage score, avoiding NaN by checking for zero
    final totalQuestions = _questions.length;
    final score = _quiz!.score;

    // Make sure score doesn't exceed total questions
    final adjustedScore = score > totalQuestions ? totalQuestions : score;

    final percentageScore =
        totalQuestions > 0 ? (adjustedScore / totalQuestions * 100).round() : 0;

    debugPrint(
        'Showing results: $adjustedScore/$totalQuestions = $percentageScore%');

    return Column(
      children: [
        // Header
        Text(
          'Challenge Results',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Text(
          'You\'ve completed the vocabulary challenge!',
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),

        // Results card
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  // Score display
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withAlpha(13),blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Score circle
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: percentageScore >= 70
                                ? AppColors.successLight.withAlpha(76): percentageScore >= 40
                                    ? AppColors.warningLight.withAlpha(76): AppColors.failureLight.withAlpha(76),border: Border.all(
                              color: percentageScore >= 70
                                  ? AppColors.green
                                  : percentageScore >= 40
                                      ? AppColors.warningLight
                                      : AppColors.incorrectLight,
                              width: 4,
                            ),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  '$percentageScore%',
                                  style: TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: percentageScore >= 70
                                        ? AppColors.green
                                        : percentageScore >= 40
                                            ? AppColors.warningLight
                                            : AppColors.incorrectLight,
                                  ),
                                ),
                                Text(
                                  '${adjustedScore}/$totalQuestions',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Feedback based on score
                        Text(
                          _getResultFeedback(percentageScore),
                          style: Theme.of(context).textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'You\'ve successfully added your first vocabulary word. '
                          'Continue to create your account and start building your vocabulary!',
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Learning tip
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .primaryContainer
                          .withAlpha(76),borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Learning Tip',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Regular practice with different question types helps strengthen your vocabulary retention. '
                          'Try to review new words within 24 hours of learning them.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Continue button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36.0),
          child: GradientButton(
            width: double.infinity,
            height: 60,
            text: 'Continue',
            onPressed: widget.onComplete,
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        //   child: SizedBox(
        //     width: double.infinity,
        //     height: 60,
        //     child: ElevatedButton(
        //       onPressed: widget.onComplete,
        //       style: ElevatedButton.styleFrom(
        //         padding: const EdgeInsets.symmetric(vertical: 16.0),
        //       ),
        //       child: Text(
        //         'Continue',
        //         style: Theme.of(context).textTheme.titleMedium?.copyWith(
        //               color: AppColors.white,
        //               fontWeight: FontWeight.bold,
        //               fontSize: 20,
        //             ),
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }

  /// Get feedback message based on the score percentage
  String _getResultFeedback(int percentage) {
    if (percentage >= 90) {
      return 'Excellent! You\'ve mastered this word!';
    } else if (percentage >= 70) {
      return 'Great job! You\'re on your way to mastering this word.';
    } else if (percentage >= 50) {
      return 'Good effort! Keep practicing to improve.';
    } else {
      return 'Nice try! With more practice, you\'ll master this word.';
    }
  }
}
