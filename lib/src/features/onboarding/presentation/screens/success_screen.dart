import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/services/local_storage_service.dart';

/// Screen shown after successful account creation
class SuccessScreen extends ConsumerStatefulWidget {
  const SuccessScreen({super.key});

  @override
  ConsumerState<SuccessScreen> createState() => _SuccessScreenState();
}

class _SuccessScreenState extends ConsumerState<SuccessScreen> {
  final LocalStorageService _localStorageService = LocalStorageService();
  bool _isTransferring = false;
  bool _isFinishing = false;
  final FirebaseService _firebaseService = FirebaseService();
  final ShowPaywall _showPaywall = ShowPaywall();

  @override
  void initState() {
    super.initState();
    // Use a slightly longer delay to ensure auth is complete
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _transferLocalDataToFirebase();
      }
    });

    // Auto-redirect after a short delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _finishOnboarding();
      }
    });
  }

  /// Transfer vocabulary cards and quiz data from local storage to Firebase
  Future<void> _transferLocalDataToFirebase() async {
    if (!mounted) return;

    setState(() {
      _isTransferring = true;
    });

    try {
      final firebaseService = FirebaseService();

      // Ensure the user document exists before proceeding
      // Use the createIfNotExists parameter to automatically create the user document if it doesn't exist
      final userExists = await firebaseService
          .isUserExists(firebaseService.userId, createIfNotExists: true);
      if (!userExists) {
        debugPrint(
            'Failed to create or find user document. Waiting before trying again...');
        // Wait a moment to ensure the auth process has completed
        await Future.delayed(const Duration(milliseconds: 1000));
        // Try one more time
        await firebaseService.isUserExists(firebaseService.userId,
            createIfNotExists: true);
      }

      // First, save the onboarding profile to ensure user data is available
      // This will update the firstName in the user document
      if (mounted) {
        await ref
            .read(guidedOnboardingNotifierProvider.notifier)
            .saveOnboardingProfile();
        debugPrint('Saved onboarding profile to user document');
      }

      // Get the vocabulary cards from local storage
      final cards = await _localStorageService.getTemporaryVocabCards();
      debugPrint('Found ${cards.length} cards in local storage to transfer');

      if (cards.isNotEmpty) {
        // Save each card to Firebase by adding it to the vocabulary collection
        for (final card in cards) {
          if (firebaseService.userId.isNotEmpty) {
            try {
              // Use the proper method to add cards to the user's vocabulary
              await firebaseService.addVocabCard(card);
              debugPrint(
                  'Successfully added card ${card.word} to user vocabulary');
            } catch (cardError) {
              debugPrint('Error adding card ${card.word}: $cardError');
            }
          }
        }
      }

      // Force refresh user data to ensure it's up to date
      final userData = await firebaseService.getUserData();
      debugPrint('User data after transfer: ${userData?.firstName}');

      // Clear temporary storage
      await _localStorageService.clearTemporaryStorage();
      debugPrint('Cleared temporary storage after successful transfer');
    } catch (e) {
      debugPrint('Error transferring data to Firebase: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isTransferring = false;
        });
      }
    }
  }

  /// Complete the onboarding process and navigate to the dashboard
  Future<void> _finishOnboarding() async {
    if (_isFinishing) return;

    setState(() {
      _isFinishing = true;
    });

    try {
      // Mark onboarding as completed
      await _firebaseService.saveOnboardingAnswers({
        'onboardingCompleted': true,
      });

      // Track onboarding completion
      try {
        await AnalyticsService.instance.trackOnboardingComplete({
          'completion_date': DateTime.now().toIso8601String(),
          'completion_method': 'guided_onboarding',
        });
      } catch (analyticsError) {
        debugPrint('Analytics tracking error: $analyticsError');
      }

      // Show the paywall
      await _showPaywall.presentPaywall();

      // Navigate to dashboard
      if (mounted) {
        context.goNamed(RouteNames.home);
      }
    } catch (e) {
      debugPrint('Error finishing onboarding: $e');
      // Navigate anyway, even if there was an error
      if (mounted) {
        context.goNamed(RouteNames.home);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success icon
          Icon(
            Icons.check_circle_outline,
            size: 100,
            color: Theme.of(context).colorScheme.primary,
          ),

          const SizedBox(height: 24),

          // Success message
          Text(
            'Account Created!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              'Your account has been created successfully. Your progress has been saved.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ),

          // Data transfer indicator
          if (_isTransferring)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Saving your vocabulary...',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),

          const SizedBox(height: 40),

          // Summary of what was accomplished
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Vocadex Journey So Far:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 16),

                // List of accomplishments
                _buildAccomplishmentItem(
                  context,
                  icon: Icons.check_circle,
                  text: 'Completed onboarding',
                ),
                _buildAccomplishmentItem(
                  context,
                  icon: Icons.menu_book,
                  text: 'Added your first vocabulary word',
                ),
                _buildAccomplishmentItem(
                  context,
                  icon: Icons.quiz,
                  text: 'Completed a practice quiz',
                ),
                _buildAccomplishmentItem(
                  context,
                  icon: Icons.person,
                  text: 'Created your account',
                ),
              ],
            ),
          ),

          const SizedBox(height: 40),

          // Button to dashboard
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isTransferring || _isFinishing
                  ? null // Disable the button while transferring data or finishing onboarding
                  : _finishOnboarding,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
              ),
              child: Text(
                'Go to Dashboard',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build an accomplishment item with icon and text
  Widget _buildAccomplishmentItem(
    BuildContext context, {
    required IconData icon,
    required String text,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
