import 'package:flutter/material.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Screen for selecting the user's native language
class LanguageBackgroundScreen extends StatefulWidget {
  final Function(String) onLanguageSelected;

  const LanguageBackgroundScreen({
    super.key,
    required this.onLanguageSelected,
  });

  @override
  State<LanguageBackgroundScreen> createState() =>
      _LanguageBackgroundScreenState();
}

class _LanguageBackgroundScreenState extends State<LanguageBackgroundScreen> {
  final _languageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? _selectedLanguage;
  bool _showOtherField = false;

  // List of languages with their country codes for flags
  final List<Map<String, String>> _languages = [
    {'language': 'English', 'code': 'GB'},
    {'language': 'Spanish', 'code': 'ES'},
    {'language': 'French', 'code': 'FR'},
    {'language': 'Italian', 'code': 'IT'},
    {'language': 'German', 'code': 'DE'},
    {'language': 'Korean', 'code': 'KR'},
    {'language': 'Japanese', 'code': 'JP'},
    {'language': 'Hindi', 'code': 'IN'},
    {'language': 'Other', 'code': 'UN'}, // Using UN flag for Other
  ];

  @override
  void dispose() {
    _languageController.dispose();
    super.dispose();
  }

  // void _handleContinue() {
  //   if (_formKey.currentState!.validate()) {
  //     widget.onLanguageSelected(_languageController.text.trim());
  //   }
  // }

  void _selectLanguage(String language) {
    setState(() {
      if (language == 'Other') {
        _showOtherField = true;
        _selectedLanguage = null;
        _languageController.clear();
      } else {
        _showOtherField = false;
        _selectedLanguage = language;
        _languageController.text = language;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(height: 24),

              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.getTextColor(
                            Theme.of(context).brightness),
                      ),
                  children: [
                    const TextSpan(text: 'What is your native '),
                    WidgetSpan(
                      child: ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            AppColors.gradientButton1,
                            AppColors.gradientButton2,
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ).createShader(bounds),
                        child: Text(
                          'language',
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors
                                    .white, // This will be masked by the shader
                              ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '?'),
                  ],
                ),
              ),
              // Screen title

              const SizedBox(height: 32),

              // Language selection grid of cards
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 1.0,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: _languages.length,
                itemBuilder: (context, index) {
                  final language = _languages[index]['language']!;
                  final isSelected = _selectedLanguage == language;
                  final brightness = Theme.of(context).brightness;

                  return Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.getPrimaryColor(brightness)
                              .withAlpha(26): AppColors.getBackgroundColor(brightness),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? AppColors.getPrimaryColor(brightness)
                            : AppColors.getTextColor(brightness)
                                .withAlpha(26),width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => _selectLanguage(language),
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Stack(
                                alignment: Alignment.center,
                                children: [
                                  language == 'Other'
                                      ? Container(
                                          width: 64,
                                          height: 67,
                                          decoration: BoxDecoration(
                                            color: AppColors.getPrimaryColor(
                                                    brightness)
                                                .withAlpha(26),borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Icon(
                                            Icons.translate_rounded,
                                            color: AppColors.getPrimaryColor(
                                                brightness),
                                            size: 32,
                                          ),
                                        )
                                      : ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Image.network(
                                            'https://flagsapi.com/${_languages[index]['code']}/flat/64.png',
                                            width: 64,
                                            height: 64,
                                            fit: BoxFit.cover,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Container(
                                                width: 64,
                                                height: 64,
                                                decoration: BoxDecoration(
                                                  color:
                                                      AppColors.getPrimaryColor(
                                                              brightness)
                                                          .withAlpha(26),borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: Icon(
                                                  Icons.language,
                                                  color:
                                                      AppColors.getPrimaryColor(
                                                          brightness),
                                                  size: 32,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                language,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      color: AppColors.getTextColor(brightness),
                                      fontWeight: isSelected
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              // Other language input field
              if (_showOtherField) ...[
                const SizedBox(height: 24),
                TextFormField(
                  controller: _languageController,
                  decoration: InputDecoration(
                    labelText: 'Enter your language',
                    hintText: 'e.g., Portuguese, Russian, etc.',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your native language';
                    }
                    return null;
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
