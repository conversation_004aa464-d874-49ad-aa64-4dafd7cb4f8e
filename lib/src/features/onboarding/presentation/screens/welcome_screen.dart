import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// The app introduction screen that explains what Vocadex does
class WelcomeScreen extends ConsumerWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 24),

        // Title with colored text
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.getTextColor(Theme.of(context).brightness),
                ),
            children: [
              const TextSpan(text: 'Welcome to '),
              WidgetSpan(
                child: ShaderMask(
                  shaderCallback: (bounds) => const LinearGradient(
                    colors: [
                      AppColors.gradientButton1,
                      AppColors.gradientButton2,
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ).createShader(bounds),
                  child: Text(
                    'Vocadex',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              Colors.white, // This will be masked by the shader
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Subtitle
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40.0),
          child: Text(
            'Start your vocabulary journey – learn, grow, and master words with ease!',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.getTextColor(Theme.of(context).brightness)
                    .withAlpha(178),
                fontSize: 16),
          ),
        ),

        const SizedBox(height: 32),

        // Images section with center image and partial side images
        Expanded(
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Left partial image - using SVG with clipRect to show only left portion
              Positioned(
                left: 0,
                bottom: 90,
                child: ClipRect(
                  child: Align(
                    alignment: Alignment.centerRight,
                    widthFactor: 0.3, // Show only 30% of the left side
                    child: SvgPicture.asset(
                      'assets/images/onboarding/Frame 1.svg',
                      fit: BoxFit.fitHeight,
                      height: 250,
                    ),
                  ),
                ),
              ),

              // Right partial image - using SVG with clipRect to show only right portion
              Positioned(
                right: 0,
                bottom: 90,
                child: ClipRect(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    widthFactor: 0.3, // Show only 30% of the right side
                    child: SvgPicture.asset(
                      'assets/images/onboarding/Frame 1.svg',
                      fit: BoxFit.fitHeight,
                      height: 250,
                    ),
                  ),
                ),
              ),

              // Center main image with padding to create gap between side cards
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 80.0),
                child: SvgPicture.asset(
                  'assets/images/onboarding/Frame 1.svg',
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Dots indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Already have an account?'),
            TextButton(
              onPressed: () => context.pushNamed(RouteNames.login),
              child: const Text('Login'),
            ),
          ],
        ),
      ],
    );
  }
}
