import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/common/widgets/gradient_button.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/onboarding/presentation/screens/welcome_screen.dart';

/// Standalone welcome screen that includes navigation buttons
/// Used when accessing the welcome screen directly (e.g., after sign out)
class StandaloneWelcomeScreen extends ConsumerWidget {
  const StandaloneWelcomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GradientScaffold(
      body: Column(
        children: [
          // Main welcome content
          const Expanded(
            child: WelcomeScreen(),
          ),

          // Bottom navigation buttons
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                // Get Started button
                SizedBox(
                  width: double.infinity,
                  child: GradientButton(
                    onPressed: () {
                      // Navigate to onboarding flow
                      context.goNamed(RouteNames.onboarding);
                    },
                    text: 'Get Started',
                    height: 56,
                  ),
                ),

                // Sign In button
              ],
            ),
          ),
        ],
      ),
    );
  }
}
