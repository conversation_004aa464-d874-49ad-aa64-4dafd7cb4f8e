import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';

/// The user profile screen to collect the user's name
class UserProfileScreen extends ConsumerStatefulWidget {
  const UserProfileScreen({super.key});

  @override
  ConsumerState<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends ConsumerState<UserProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  bool _isNameValid = false;

  @override
  void initState() {
    super.initState();
    // Initialize the controller with existing name if available
    final profile = ref.read(onboardingProfileProvider);
    if (profile.name != null && profile.name!.isNotEmpty) {
      _nameController.text = profile.name!;
      _isNameValid = true;
    }

    // Add listener to validate input
    _nameController.addListener(_validateName);
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  /// Validate the name input
  void _validateName() {
    setState(() {
      _isNameValid = _nameController.text.trim().length >= 2;
    });

    // Update the profile if the name is valid
    if (_isNameValid) {
      ref.read(guidedOnboardingNotifierProvider.notifier).updateProfileField(
            field: 'name',
            value: _nameController.text.trim(),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Screen title
          Text(
            'What\'s your name?',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            'We\'ll use this to personalize your experience.',
            style: Theme.of(context).textTheme.bodyLarge,
          ),

          const SizedBox(height: 48),

          // Name input
          TextField(
            controller: _nameController,
            style: Theme.of(context).textTheme.titleLarge,
            decoration: InputDecoration(
              hintText: 'Enter your name',
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 20,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                ),
              ),
              suffixIcon: _isNameValid
                  ? Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  : null,
            ),
            textCapitalization: TextCapitalization.words,
            textInputAction: TextInputAction.done,
            onEditingComplete: () {
              FocusScope.of(context).unfocus();
            },
          ),

          const SizedBox(height: 48),

          // Illustration
          Center(
            child: Image.asset(
              'assets/images/onboarding/user_profile.png',
              height: 200,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
