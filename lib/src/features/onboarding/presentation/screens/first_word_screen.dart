import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_button.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/core/utils/ui_utils.dart';
import 'package:vocadex/src/features/onboarding/models/guided_onboarding_model.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/vocab_capture_entry.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_card/vocabulary_card.dart';
import 'package:vocadex/src/services/local_storage_service.dart';
import 'package:vocadex/src/features/onboarding/services/onboarding_allocation_utils.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';

/// Screen for adding the first word to the user's vocabulary
class FirstWordScreen extends ConsumerStatefulWidget {
  const FirstWordScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<FirstWordScreen> createState() => _FirstWordScreenState();
}

class _FirstWordScreenState extends ConsumerState<FirstWordScreen> {
  bool _isLoading = false;
  VocabCard? _vocabCard;
  String _errorMessage = '';
  final LocalStorageService _localStorageService = LocalStorageService();
  final OnboardingAllocationUtils _allocationUtils =
      OnboardingAllocationUtils();

  @override
  void initState() {
    super.initState();
    _initializeAllocation();
  }

  /// Initialize the allocation utils for onboarding
  Future<void> _initializeAllocation() async {
    try {
      await _allocationUtils.loadUserInfo(ref);
      // Set a reasonable remaining card count for onboarding
      ref.read(remainingCardsProvider.notifier).state = 5;
    } catch (e) {
      debugPrint('Error initializing allocation: $e');
    }
  }

  /// Add a new word using the vocab capture feature
  Future<void> _addNewWord() async {
    try {
      // Open the manual entry dialog with isOnboarding set to true
      final vocabCard = await showManualVocabEntry(
        context,
        isOnboarding: true,
      );

      // If the user cancelled the dialog or there was an error
      if (vocabCard == null) {
        return;
      }

      // Ensure the card has required fields
      final updatedCard = vocabCard.copyWith(
        examples: vocabCard.examples.isEmpty
            ? ['Example sentence with ${vocabCard.word}']
            : vocabCard.examples,
        type: vocabCard.type.isEmpty ? ['noun'] : vocabCard.type,
        level: vocabCard.level.isEmpty ? 'B1' : vocabCard.level,
        color: vocabCard.color.isEmpty ? 'blue' : vocabCard.color,
      );

      setState(() {
        _vocabCard = updatedCard;
      });

      // Update the provider with the new card
      ref.read(firstWordProvider.notifier).state = updatedCard;

      // Save the card to local storage temporarily
      await _localStorageService.saveVocabCard(updatedCard);
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'An error occurred. Please try again.';
        });
      }
      debugPrint('Error adding word: $e');
    }
  }

  /// Add the word to the user's vocabulary
  Future<void> _addWordToVocabulary() async {
    if (_vocabCard == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // The card is already saved to local storage when it's created

      // Move to the next screen
      if (mounted) {
        ref
            .read(guidedOnboardingNotifierProvider.notifier)
            .goToNextScreen(context);
      }
    } catch (e) {
      if (mounted) {
        UiUtils.showSnackBar(context, 'Failed to add word. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Header
          const SizedBox(height: 24),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextColor(Theme.of(context).brightness),
                  ),
              children: [
                const TextSpan(text: 'Capture your first '),
                WidgetSpan(
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [
                        AppColors.gradientButton1,
                        AppColors.gradientButton2,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ).createShader(bounds),
                    child: Text(
                      'word',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors
                                    .white, // This will be masked by the shader
                              ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Screen title
          const SizedBox(height: 16),
          Text(
            'Let\'s create your first vocabulary card. Choose a word you want to learn or remember.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 16,
                  color: AppColors.getTextColor(brightness).withValues(
                    alpha: 0.7,
                  ),
                ),
          ),
          const SizedBox(height: 40),
          // Word card or add button
          _buildContent(),

          // Button to continue
          if (!_isLoading && _vocabCard != null)
            // SizedBox(
            //   width: double.infinity,
            //   height: 60,
            //   child: ElevatedButton(
            //     onPressed: _addWordToVocabulary,
            //     style: ElevatedButton.styleFrom(
            //       backgroundColor:
            //           Theme.of(context).brightness == Brightness.light
            //               ? AppColors.primaryLight
            //               : AppColors.primaryDark,
            //       padding: const EdgeInsets.symmetric(vertical: 16.0),
            //     ),
            //     child: Text(
            //       'Next',
            //       style: Theme.of(context).textTheme.titleMedium?.copyWith(
            //             color: AppColors.white,
            //             fontWeight: FontWeight.bold,
            //             fontSize: 20,
            //           ),
            //     ),
            //   ),
            // ),

            GradientButton(
              width: double.infinity,
              height: 60,
              text: 'Next',
              onPressed: _addWordToVocabulary,
            ),
        ],
      ),
    );
  }

  /// Build the main content based on state
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _addNewWord,
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (_vocabCard != null) {
      // Display the vocabulary card
      return Expanded(
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // The vocabulary card
                Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: VocabularyCard(
                    card: _vocabCard!,
                    isFlippable: true,
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      );
    }

    // Show the add button when no card is present
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_circle_outline,
            size: 80,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 24),
          Text(
            'Your First Vocabulary Word',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'This word will be added to your vocabulary deck',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          GradientButton(
            height: 60,
            onPressed: _addNewWord,
            text: 'Add a Word',
          ),
          // ElevatedButton.icon(
          //   onPressed: _addNewWord,
          //   icon: const Icon(Icons.add),
          //   label: const Text('Add a Word'),
          //   style: ElevatedButton.styleFrom(
          //     padding: const EdgeInsets.symmetric(
          //       horizontal: 24,
          //       vertical: 16,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
