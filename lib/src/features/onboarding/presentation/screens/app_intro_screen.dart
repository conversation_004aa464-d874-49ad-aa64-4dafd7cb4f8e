import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// The app introduction screen that explains what Vocadex does
class AppIntroScreen extends ConsumerWidget {
  const AppIntroScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final brightness = Theme.of(context).brightness;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          // Screen title

          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextColor(Theme.of(context).brightness),
                  ),
              children: [
                const TextSpan(text: 'What is '),
                WidgetSpan(
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [
                        AppColors.gradientButton1,
                        AppColors.gradientButton2,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ).createShader(bounds),
                    child: Text(
                      'Vocadex',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors
                                    .white, // This will be masked by the shader
                              ),
                    ),
                  ),
                ),
                const TextSpan(text: '?'),
              ],
            ),
          ),
          const SizedBox(height: 8),

          Text(
            'Your all-in-one tool for mastering words effortlessly',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 16,
                  color: AppColors.getTextColor(brightness).withAlpha(178),),
          ),

          const SizedBox(height: 32),

          // First feature
          _buildFeature(
            context,
            svgAsset: 'assets/icons/deck_filled.svg',
            title: 'Capture Vocabulary',
            description: 'Save and organize words with ease.',
            backgroundColor: AppColors.levelA1.withAlpha(76),iconColor: AppColors.primaryLight,
          ),

          const SizedBox(height: 24),

          // Second feature
          _buildFeature(
            context,
            svgAsset: 'assets/icons/barbell_filled.svg',
            title: 'Train Your Skills',
            description: 'Sharpen your knowledge through fun exercises.',
            backgroundColor: AppColors.levelB1.withAlpha(76),iconColor: AppColors.getPrimaryColor(brightness),
          ),

          const SizedBox(height: 24),

          // Third feature
          _buildFeature(
            context,
            svgAsset: 'assets/icons/insight_filled.svg',
            title: 'Track Progress',
            description: 'See your growth and celebrate milestones.',
            backgroundColor: AppColors.levelC1.withAlpha(76),iconColor: AppColors.getPrimaryColor(brightness),
          ),
        ],
      ),
    );
  }

  /// Helper to build a feature item with icon and text
  Widget _buildFeature(
    BuildContext context, {
    required String svgAsset,
    required String title,
    required String description,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    final brightness = Theme.of(context).brightness;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Feature icon
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: SvgPicture.asset(
            svgAsset,
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
            width: 28,
            height: 28,
          ),
        ),

        const SizedBox(width: 16),

        // Feature text
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.getTextColor(brightness),
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color:
                          AppColors.getTextColor(brightness).withAlpha(178),),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
