import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/services/local_storage_service.dart';

/// Results screen for the onboarding challenge
class OnboardingQuizResultsScreen extends ConsumerStatefulWidget {
  final VoidCallback onComplete;

  const OnboardingQuizResultsScreen({
    super.key,
    required this.onComplete,
  });

  @override
  ConsumerState<OnboardingQuizResultsScreen> createState() =>
      _OnboardingQuizResultsScreenState();
}

class _OnboardingQuizResultsScreenState
    extends ConsumerState<OnboardingQuizResultsScreen> {
  bool _isLoading = true;
  Quiz? _quiz;
  final LocalStorageService _localStorageService = LocalStorageService();

  @override
  void initState() {
    super.initState();
    _loadQuizResults();
  }

  /// Load quiz results from local storage
  Future<void> _loadQuizResults() async {
    try {
      // In a real implementation, this would load the quiz from storage
      final tempQuiz = await _localStorageService.getTemporaryQuiz();

      setState(() {
        _quiz = tempQuiz ?? _createFallbackQuiz();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading quiz results: $e');
      setState(() {
        _quiz = _createFallbackQuiz();
        _isLoading = false;
      });
    }
  }

  /// Create a fallback quiz if none is found
  Quiz _createFallbackQuiz() {
    return Quiz(
      id: 'sample',
      title: 'Onboarding Sample Quiz',
      score: 2,
      questions: const [],
      createdAt: DateTime.now(),
      isCompleted: true,
      totalQuestions: 3,
      type: QuizType.mixed,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Results',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 24),

        // Custom results display instead of using the app's existing quiz results screen
        Expanded(
          child: _buildCustomQuizResults(),
        ),

        // Information about creating an account
        // Container removed as per requirements

        const SizedBox(height: 16),

        // Button to continue
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: SizedBox(
            width: double.infinity,
            height: 60,
            child: ElevatedButton(
              onPressed: widget.onComplete,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                // shape: RoundedRectangleBorder(
                //   borderRadius: BorderRadius.circular(12.0),
                // ),
              ),
              child: Text(
                'Continue',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build custom quiz results to avoid NaN errors
  Widget _buildCustomQuizResults() {
    // Safely calculate percentage
    final int totalQuestions =
        _quiz?.totalQuestions ?? 1; // Avoid division by zero
    final int score = _quiz?.score ?? 0;
    final double percentage =
        totalQuestions > 0 ? (score / totalQuestions * 100) : 0; // Avoid NaN

    return Column(
      children: [
        // Score circle
        Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withAlpha(26),blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: _getResultColor(percentage),
              width: 8,
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${percentage.toInt()}%',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: _getResultColor(percentage),
                  ),
                ),
                Text(
                  '$score / $totalQuestions',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 32),

        // Result message
        Text(
          _getResultMessage(percentage),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getResultColor(percentage),
              ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),

        // Result description
        Text(
          _getResultDescription(percentage),
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 24),

        // Tips container
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withAlpha(128),),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Learning Tips',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '• Review your vocabulary regularly\n'
                '• Practice using words in sentences\n'
                '• Connect words to images or memories\n'
                '• Test yourself with flashcards',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Get color based on percentage score
  Color _getResultColor(double percentage) {
    if (percentage >= 80) {
      return AppColors.successLight;
    } else if (percentage >= 60) {
      return AppColors.correctDark;
    } else {
      return AppColors.incorrectDark;
    }
  }

  /// Get message based on percentage score
  String _getResultMessage(double percentage) {
    if (percentage >= 80) {
      return 'Great job!';
    } else if (percentage >= 60) {
      return 'Good effort!';
    } else {
      return 'Keep practicing!';
    }
  }

  /// Get description based on percentage score
  String _getResultDescription(double percentage) {
    if (percentage >= 80) {
      return 'You have an excellent understanding of this word!';
    } else if (percentage >= 60) {
      return 'You\'re on the right track with this word.';
    } else {
      return 'Don\'t worry, vocabulary learning takes time and practice.';
    }
  }
}
