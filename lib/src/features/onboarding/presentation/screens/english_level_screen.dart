import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/onboarding/models/guided_onboarding_model.dart';
import 'package:vocadex/src/features/onboarding/providers/guided_onboarding_provider.dart';

/// The English level selection screen
class EnglishLevelScreen extends ConsumerWidget {
  const EnglishLevelScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profile = ref.watch(onboardingProfileProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Screen title
          const SizedBox(height: 24),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextColor(Theme.of(context).brightness),
                  ),
              children: [
                const TextSpan(text: 'What is your English '),
                WidgetSpan(
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [
                        AppColors.gradientButton1,
                        AppColors.gradientButton2,
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ).createShader(bounds),
                    child: Text(
                      'level',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors
                                    .white, // This will be masked by the shader
                              ),
                    ),
                  ),
                ),
                const TextSpan(text: '?'),
              ],
            ),
          ),
          // Screen title

          const SizedBox(height: 16),

          // Description
          Text(
            'We\'ll personalize your vocabulary based on your level.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 16,
                  color: AppColors.getTextColor(Theme.of(context).brightness)
                      .withValues(
                    alpha: 0.7,
                  ),
                ),
          ),

          const SizedBox(height: 32),

          // Level selection options
          Expanded(
            child: ListView(
              shrinkWrap: true,
              children: EnglishLevel.values
                  .map((level) => _buildLevelOption(
                        context,
                        level: level,
                        isSelected: profile.englishLevel == level,
                        onTap: () {
                          ref
                              .read(guidedOnboardingNotifierProvider.notifier)
                              .updateProfileField(
                                field: 'englishLevel',
                                value: level,
                              );
                        },
                      ))
                  .toList(),
            ),
          ),

          // Next button
          // Padding(
          //   padding: const EdgeInsets.only(top: 24, bottom: 32),
          //   child: SizedBox(
          //     width: double.infinity,
          //     child: ElevatedButton(
          //       onPressed: () {
          //         // Navigate to the next screen
          //         ref
          //             .read(guidedOnboardingNotifierProvider.notifier)
          //             .goToNextScreen(context);
          //       },
          //       style: ElevatedButton.styleFrom(
          //         padding: const EdgeInsets.symmetric(vertical: 16),
          //         shape: RoundedRectangleBorder(
          //           borderRadius: BorderRadius.circular(12),
          //         ),
          //       ),
          //       child: const Text('Next'),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  /// Build a level selection option
  Widget _buildLevelOption(
    BuildContext context, {
    required EnglishLevel level,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final brightness = Theme.of(context).brightness;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 14,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.getPrimaryColor(brightness).withAlpha(26): AppColors.getBackgroundColor(brightness),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppColors.getPrimaryColor(brightness)
                : AppColors.getTextColor(brightness).withAlpha(26),width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            // Radio indicator
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? AppColors.getPrimaryColor(brightness)
                    : AppColors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppColors.getPrimaryColor(brightness)
                      : AppColors.getTextColor(brightness).withAlpha(76),width: 2,
                ),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check_rounded,
                      size: 16,
                      color: AppColors.getButtonForegroundColor(brightness),
                    )
                  : null,
            ),

            const SizedBox(width: 16),

            // Level text
            Expanded(
              child: Text(
                level.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.getTextColor(brightness),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
