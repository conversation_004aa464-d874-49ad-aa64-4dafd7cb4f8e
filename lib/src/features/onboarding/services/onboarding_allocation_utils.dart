import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';

/// Utility class for handling card allocation during onboarding (without Firebase)
class OnboardingAllocationUtils {
  /// Constants
  static const int freeUserDailyCards = 5;

  /// Load user information and allocation data for onboarding
  /// During onboarding, we set a default value for remaining cards
  Future<void> loadUserInfo(WidgetRef ref) async {
    try {
      // During onboarding, set a default value (user isn't authenticated yet)
      ref.read(remainingCardsProvider.notifier).state = freeUserDailyCards;

      // Log for debugging
      debugPrint(
          'Loaded onboarding allocation info: $freeUserDailyCards cards');
    } catch (e) {
      debugPrint('Error loading onboarding user info: $e');
    }
  }

  /// Check if the user can add more cards during onboarding
  /// Always returns true during onboarding
  Future<bool> canAddCard(WidgetRef ref) async {
    // During onboarding, users can always add cards
    return true;
  }

  /// Get information about the next card allocation reset
  /// Returns placeholder data during onboarding
  Future<Map<String, dynamic>> getNextResetInfo() async {
    try {
      // Calculate when the next reset will happen (next day at midnight)
      final now = DateTime.now();
      final nextResetDate = DateTime(
        now.year,
        now.month,
        now.day + 1,
      );

      // Calculate time remaining until next reset
      final duration = nextResetDate.difference(now);

      return {
        'nextResetDate': nextResetDate,
        'hoursRemaining': duration.inHours,
        'minutesRemaining': duration.inMinutes % 60,
      };
    } catch (e) {
      debugPrint('Error getting next reset info: $e');

      // Return a default value if there's an error
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);

      return {
        'nextResetDate': tomorrow,
        'hoursRemaining': 24,
        'minutesRemaining': 0,
      };
    }
  }

  /// Show premium upgrade dialog when user is out of cards
  /// During onboarding, this should never be called
  void showUpgradeDialog(BuildContext context) {
    // No-op during onboarding
    debugPrint('Upgrade dialog not shown during onboarding');
  }

  /// Show the remaining card allocation to the user
  /// During onboarding, this should never be called
  void showRemainingCardAllocation(BuildContext context, WidgetRef ref) {
    // No-op during onboarding
    debugPrint('Remaining allocation not shown during onboarding');
  }
}
