import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_capture/providers/capture_providers.dart';
import 'package:vocadex/src/services/local_storage_service.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/features/onboarding/services/onboarding_allocation_utils.dart';

/// Service for saving vocabulary cards to local storage during onboarding
class OnboardingVocabService {
  final LocalStorageService _localStorageService = LocalStorageService();
  final OnboardingAllocationUtils _allocationUtils =
      OnboardingAllocationUtils();

  /// Initialize allocation data for onboarding
  Future<void> initializeAllocation(WidgetRef ref) async {
    await _allocationUtils.loadUserInfo(ref);
  }

  /// Save a vocabulary card to local storage
  /// Returns a success flag and a map with additional information
  Future<Map<String, dynamic>> saveVocabulary(
    BuildContext context,
    VocabCard vocabCard,
    WidgetRef ref,
  ) async {
    try {
      // Initialize allocation data if needed
      await initializeAllocation(ref);

      // Update state to saving
      ref.read(captureStateProvider.notifier).state = CaptureState.saving;

      // Save the card to local storage
      await _localStorageService.saveVocabCard(vocabCard);

      // Update state to saved
      ref.read(captureStateProvider.notifier).state = CaptureState.saved;

      // Show success message
      if (context.mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Vocabulary saved for your onboarding!',
        );
      }

      // Return success result
      return {
        'success': true,
        'message': 'Vocabulary saved successfully!',
        'vocabCard': vocabCard,
      };
    } catch (e) {
      // Handle errors
      debugPrint('Error saving vocabulary: $e');
      ref.read(captureErrorProvider.notifier).state =
          'Error saving vocabulary: $e';
      ref.read(captureStateProvider.notifier).state = CaptureState.error;

      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: e.toString(),
        );
      }

      return {
        'success': false,
        'message': 'Error: $e',
      };
    }
  }

  /// Reset the save state
  void resetState(WidgetRef ref) {
    ref.read(captureStateProvider.notifier).state = CaptureState.initial;
    ref.read(captureErrorProvider.notifier).state = null;
  }
}

/// Provider for the onboarding vocabulary save service
final onboardingVocabServiceProvider = Provider<OnboardingVocabService>((ref) {
  return OnboardingVocabService();
});
