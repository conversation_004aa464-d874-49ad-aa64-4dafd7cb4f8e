import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/onboarding/models/guided_onboarding_model.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Service to generate sample challenges for onboarding
class OnboardingChallengeService {
  /// Generate a default vocabulary card
  VocabCard get defaultCard => VocabCard(
        id: FirebaseService.getUID('vocab'),
        word: 'journey',
        definition: 'An act of traveling from one place to another.',
        examples: [
          'We had a long journey ahead of us.',
          'Life is a journey, not a destination.'
        ],
        type: ['noun'],
        pronunciation: 'jur-nee',
        level: 'B1',
        color: 'blue',
        frequency: 3,
      );

  /// Generate a list of sample challenges for the onboarding flow
  Future<List<OnboardingChallenge>> generateChallenges({
    required String level,
    int count = 5,
  }) async {
    try {
      // In a real implementation, this would call an AI service to generate challenges
      // For now, we'll return sample data
      return [
        OnboardingChallenge(
          word: 'journey',
          definition: 'An act of traveling from one place to another.',
          examples: [
            'We had a long journey ahead of us.',
            'Life is a journey, not a destination.'
          ],
          level: level,
          frequency: 3.0,
        ),
        OnboardingChallenge(
          word: 'achieve',
          definition: 'Successfully bring about or reach a desired objective.',
          examples: [
            'He achieved his goal of becoming a doctor.',
            'She achieved great success in her career.'
          ],
          level: level,
          frequency: 2.5,
        ),
        OnboardingChallenge(
          word: 'discover',
          definition: 'Find unexpectedly or during a search.',
          examples: [
            'They discovered a new species of bird.',
            'She discovered her passion for painting.'
          ],
          level: level,
          frequency: 2.8,
        ),
      ];
    } catch (e) {
      debugPrint('Error generating challenges: $e');
      return [
        OnboardingChallenge(
          word: 'journey',
          definition: 'An act of traveling from one place to another.',
          examples: [
            'We had a long journey ahead of us.',
            'Life is a journey, not a destination.'
          ],
          level: level,
          frequency: 3.0,
        ),
      ];
    }
  }

  /// Convert an OnboardingChallenge to a VocabCard
  VocabCard challengeToVocabCard(OnboardingChallenge challenge) {
    return VocabCard(
      id: FirebaseService.getUID('vocab'),
      word: challenge.word,
      definition: challenge.definition,
      examples: challenge.examples,
      type: ['noun'], // Default type
      pronunciation: '', // Empty pronunciation
      level: challenge.level,
      color: 'blue', // Default color
      frequency: challenge.frequency.toInt(),
    );
  }

  /// Helper function to extract JSON from text
  Map<String, dynamic> _extractJsonFromText(String text) {
    try {
      // Try to parse the entire text as JSON
      return json.decode(text);
    } catch (e) {
      // If that fails, try to extract JSON using regex
      final regex = RegExp(r'{[\s\S]*}');
      final match = regex.firstMatch(text);
      if (match != null) {
        try {
          return json.decode(match.group(0) ?? '{}');
        } catch (e) {
          return {};
        }
      }
      return {};
    }
  }
}
