import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/sharedpref_service.dart';

/// Manages the weekly goals for capturing cards of each type
class WeeklyGoalsManager {
  static const int targetCardsPerType = 5;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final SharedPreferencesService _prefsService = SharedPreferencesService();

  /// Gets the current user's ID
  String get _userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  /// Get the start of the current week (Monday 00:00:00)
  DateTime _getStartOfWeek() {
    final now = DateTime.now();
    final day = now.weekday;

    // If today is Monday (1), we don't need to go back
    // Otherwise go back by (day - 1) days to reach Monday
    final daysToSubtract = day - 1;
    final startOfWeek =
        DateTime(now.year, now.month, now.day - daysToSubtract, 0, 0, 0);

    return startOfWeek;
  }

  /// Get the end of the current week (Sunday 23:59:59)
  DateTime _getEndOfWeek() {
    final startOfWeek = _getStartOfWeek();
    // Add 6 days to Monday to get to Sunday
    final endOfWeek = DateTime(
        startOfWeek.year, startOfWeek.month, startOfWeek.day + 6, 23, 59, 59);

    return endOfWeek;
  }

  /// Reset weekly goals if needed
  Future<bool> _checkAndResetWeeklyGoals() async {
    try {
      // Get last reset date from SharedPreferences
      final lastReset = await _prefsService.getWeeklyGoalsLastReset();
      final startOfWeek = _getStartOfWeek();

      // If last reset is null or before this week, we need to reset
      if (lastReset == null || lastReset.isBefore(startOfWeek)) {
        debugPrint('Resetting weekly goals for a new week');

        // Create default empty weekly goals
        final emptyGoals = {
          'verb': 0,
          'noun': 0,
          'adjective': 0,
          'adverb': 0,
          // 'preposition': 0,
          // 'pronoun': 0,
          // 'conjunction': 0,
          // 'interjection': 0,
        };

        // Save locally
        await _prefsService.saveWeeklyGoalsData(emptyGoals);

        // If user is authenticated, also save to Firestore
        if (_auth.currentUser != null) {
          try {
            await _firestore.collection('users').doc(_userId).update({
              'weekly_goals_last_reset': Timestamp.fromDate(startOfWeek),
              'weekly_goals': emptyGoals,
            });
          } catch (e) {
            debugPrint('Warning: Could not update Firestore: $e');
            // Continue anyway since we have local data
          }
        }

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking weekly goals: $e');
      await _initializeWeeklyGoals();
      return false;
    }
  }

  /// Initialize weekly goals
  Future<void> _initializeWeeklyGoals() async {
    try {
      final startOfWeek = _getStartOfWeek();
      final emptyGoals = {
        'verb': 0,
        'noun': 0,
        'adjective': 0,
        'adverb': 0,
        // 'preposition': 0,
        // 'pronoun': 0,
        // 'conjunction': 0,
        // 'interjection': 0,
      };

      // Save locally first
      await _prefsService.saveWeeklyGoalsData(emptyGoals);

      // Then try to save to Firestore if available
      if (_auth.currentUser != null) {
        try {
          await _firestore.collection('users').doc(_userId).set({
            'weekly_goals_last_reset': Timestamp.fromDate(startOfWeek),
            'weekly_goals': emptyGoals,
          }, SetOptions(merge: true));
        } catch (e) {
          debugPrint('Warning: Could not save to Firestore: $e');
        }
      }

      debugPrint('Initialized weekly goals');
    } catch (e) {
      debugPrint('Error initializing weekly goals: $e');
    }
  }

  /// Get current week's progress for card type goals
  Future<Map<String, int>> getWeeklyTypeGoals() async {
    // Check if we need to reset goals for a new week
    await _checkAndResetWeeklyGoals();

    try {
      // First try to get from local storage
      final localData = await _prefsService.getWeeklyGoalsData();
      if (localData != null) {
        // Convert to Map<String, int>
        Map<String, int> result = {};
        localData.forEach((key, value) {
          if (value is num) {
            result[key] = value.toInt();
          } else {
            result[key] = 0;
          }
        });
        return result;
      }

      // Fallback to Firestore if local data doesn't exist
      if (_auth.currentUser != null) {
        final userDoc = await _firestore.collection('users').doc(_userId).get();
        if (userDoc.exists) {
          final userData = userDoc.data();
          if (userData != null && userData['weekly_goals'] is Map) {
            final goals = userData['weekly_goals'] as Map<String, dynamic>;

            // Convert to Map<String, int>
            Map<String, int> result = {};
            goals.forEach((key, value) {
              if (value is num) {
                result[key] = value.toInt();
              } else {
                result[key] = 0;
              }
            });

            // Save to local storage for future use
            await _prefsService.saveWeeklyGoalsData(goals);

            return result;
          }
        }
      }

      // If we get here, initialize with empty data
      await _initializeWeeklyGoals();
      return _getDefaultGoalsMap();
    } catch (e) {
      debugPrint('Error getting weekly goals: $e');
      return _getDefaultGoalsMap();
    }
  }

  /// Get default empty goals map
  Map<String, int> _getDefaultGoalsMap() {
    return {
      'verb': 0,
      'noun': 0,
      'adjective': 0,
      'adverb': 0,
      // 'preposition': 0,
      // 'pronoun': 0,
      // 'conjunction': 0,
      // 'interjection': 0,
    };
  }

  /// Update weekly goals when a new card is added
  Future<void> updateWeeklyGoalsForNewCard(VocabCard card) async {
    await _checkAndResetWeeklyGoals();

    try {
      // Get the current goals from local storage
      var weeklyGoals = await _prefsService.getWeeklyGoalsData();
      weeklyGoals ??= _getDefaultGoalsMap();

      // Update the goals for each type in the card
      bool updated = false;
      for (final type in card.type) {
        final normalizedType = type.toLowerCase();
        final currentCount = weeklyGoals[normalizedType] ?? 0;
        weeklyGoals[normalizedType] = currentCount + 1;
        updated = true;
      }

      // Save updated goals locally
      if (updated) {
        await _prefsService.saveWeeklyGoalsData(weeklyGoals);

        // Also try to update Firestore if available
        if (_auth.currentUser != null) {
          try {
            await _firestore.collection('users').doc(_userId).update({
              'weekly_goals': weeklyGoals,
            });
          } catch (e) {
            debugPrint('Warning: Could not update Firestore: $e');
          }
        }
        debugPrint('Weekly goals updated for card: ${card.word}');
      }
    } catch (e) {
      debugPrint('Error updating weekly goals: $e');
      await _initializeWeeklyGoals();
    }
  }

  /// Fix weekly goals data if it has the wrong format
  Future<void> _fixWeeklyGoalsFormat() async {
    try {
      debugPrint('Fixing weekly goals format');

      final startOfWeek = _getStartOfWeek();

      // Reset to correct format
      await _firestore.collection('users').doc(_userId).update({
        'weekly_goals_last_reset': Timestamp.fromDate(startOfWeek),
        'weekly_goals': {
          'verb': 0,
          'noun': 0,
          'adjective': 0,
          'adverb': 0,
          // 'preposition': 0,
          // 'pronoun': 0,
          // 'conjunction': 0,
          // 'interjection': 0,
        },
      });

      debugPrint('Fixed weekly goals format');
    } catch (e) {
      debugPrint('Error fixing weekly goals format: $e');
      // Try with set instead if update fails
      await _initializeWeeklyGoals();
    }
  }

  /// Get default empty goals map

  // /// Update card types after fixing weekly goals format
  // Future<void> _updateCardTypesAfterFix(List<String> types) async {
  //   try {
  //     // First get the newly initialized goals
  //     final userDoc = await _firestore.collection('users').doc(_userId).get();
  //     final userData = userDoc.data();

  //     if (userData == null || userData['weekly_goals'] == null) {
  //       return;
  //     }

  //     Map<String, dynamic> weeklyGoals = Map<String, dynamic>.from(
  //         userData['weekly_goals'] as Map<String, dynamic>);

  //     // Update the goals for each type
  //     for (final type in types) {
  //       final normalizedType = type.toLowerCase();
  //       final currentCount = weeklyGoals[normalizedType] ?? 0;
  //       weeklyGoals[normalizedType] = currentCount + 1;
  //     }

  //     // Save the updated goals
  //     await _firestore.collection('users').doc(_userId).update({
  //       'weekly_goals': weeklyGoals,
  //     });

  //     debugPrint('Updated card types after fixing format');
  //   } catch (e) {
  //     debugPrint('Error updating card types after fix: $e');
  //   }
  // }

  /// Get remaining days in the current week
  Future<Map<String, dynamic>> getWeeklyGoalsInfo() async {
    final now = DateTime.now();
    final endOfWeek = _getEndOfWeek();

    // Calculate remaining days
    final remainingDays =
        endOfWeek.difference(now).inDays + 1; // +1 to include today

    // Get current progress
    final weeklyGoals = await getWeeklyTypeGoals();

    // Calculate completion percentages for each type
    Map<String, double> completionPercentages = {};
    weeklyGoals.forEach((type, count) {
      completionPercentages[type] = (count / targetCardsPerType) * 100;
      if (completionPercentages[type]! > 100) {
        completionPercentages[type] = 100;
      }
    });

    // Format dates for display
    final startOfWeek = _getStartOfWeek();
    final dateFormat = DateFormat('MMM d');
    final weekRange =
        '${dateFormat.format(startOfWeek)} - ${dateFormat.format(endOfWeek)}';

    return {
      'startOfWeek': startOfWeek,
      'endOfWeek': endOfWeek,
      'weekRange': weekRange,
      'remainingDays': remainingDays,
      'targetPerType': targetCardsPerType,
      'currentProgress': weeklyGoals,
      'completionPercentages': completionPercentages,
    };
  }

  /// Check if adding a card would meet a weekly goal
  Future<Map<String, dynamic>> checkGoalCompletion(
      List<String> cardTypes) async {
    final weeklyGoals = await getWeeklyTypeGoals();

    List<String> completedTypes = [];
    List<String> progressedTypes = [];

    for (final type in cardTypes) {
      final normalizedType = type.toLowerCase();
      final currentCount = weeklyGoals[normalizedType] ?? 0;

      // Check if this card will complete the goal
      if (currentCount == targetCardsPerType - 1) {
        completedTypes.add(normalizedType);
      } else if (currentCount < targetCardsPerType) {
        progressedTypes.add(normalizedType);
      }
    }

    return {
      'hasCompletedGoals': completedTypes.isNotEmpty,
      'completedTypes': completedTypes,
      'progressedTypes': progressedTypes,
    };
  }

  /// Debug method to clear weekly goals (for testing)
  Future<void> clearWeeklyGoals() async {
    try {
      await _firestore.collection('users').doc(_userId).update({
        'weekly_goals': {
          'verb': 0,
          'noun': 0,
          'adjective': 0,
          'adverb': 0,
          // 'preposition': 0,
          // 'pronoun': 0,
          // 'conjunction': 0,
          // 'interjection': 0,
        },
      });
      debugPrint('Weekly goals cleared for testing');
    } catch (e) {
      debugPrint('Error clearing weekly goals: $e');
    }
  }

  /// Sync local weekly goals to Firestore when online
  Future<void> syncWeeklyGoalsToFirestore() async {
    if (_auth.currentUser == null) return;

    try {
      // Get local data
      final localData = await _prefsService.getWeeklyGoalsData();
      final lastReset = await _prefsService.getWeeklyGoalsLastReset();

      if (localData != null && lastReset != null) {
        // Sync to Firestore
        await _firestore.collection('users').doc(_userId).update({
          'weekly_goals': localData,
          'weekly_goals_last_reset': Timestamp.fromDate(lastReset),
        });
        debugPrint('Synced weekly goals to Firestore');
      }
    } catch (e) {
      debugPrint('Error syncing weekly goals to Firestore: $e');
    }
  }
}
