import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/dashboard/widgets/buttons.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/vocab_capture_entry.dart';
import 'package:vocadex/src/features/weekly_target/weekly_goals_manager.dart';
import 'dart:math' as math;
import 'package:visibility_detector/visibility_detector.dart';
import 'package:vocadex/src/features/vocab_capture/widgets/capture_options_dialog.dart';

// Provider for the WeeklyGoalsManager to ensure we use a single instance
final weeklyGoalsManagerProvider = Provider<WeeklyGoalsManager>((ref) {
  return WeeklyGoalsManager();
});

// Create a dedicated provider for the weekly goals data
final weeklyGoalsInfoProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final goalsManager = ref.watch(weeklyGoalsManagerProvider);
  return await goalsManager.getWeeklyGoalsInfo();
});

/// A widget that displays weekly targets for different word types
class WeeklyTargetsWidget extends ConsumerStatefulWidget {
  const WeeklyTargetsWidget({super.key});

  @override
  ConsumerState<WeeklyTargetsWidget> createState() =>
      _WeeklyTargetsWidgetState();
}

class _WeeklyTargetsWidgetState extends ConsumerState<WeeklyTargetsWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final String _visibilityKey = 'weekly_targets_visibility_key';
  bool _hasAnimated = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller but don't start it yet
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Start animation when the widget becomes visible
  void _handleVisibilityChanged(VisibilityInfo info) {
    if (info.visibleFraction > 0.2 && !_hasAnimated) {
      _animationController.forward();
      _hasAnimated = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final goalsDataAsync = ref.watch(weeklyGoalsInfoProvider);

    return VisibilityDetector(
      key: Key(_visibilityKey),
      onVisibilityChanged: _handleVisibilityChanged,
      child: goalsDataAsync.when(
        loading: () => const SizedBox(
          height: 200,
          child: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        error: (error, stackTrace) => SizedBox(
          height: 200,
          child: Center(
            child: Text('Error: $error'),
          ),
        ),
        data: (goalsData) {
          final currentProgress =
              goalsData['currentProgress'] as Map<String, int>;
          final targetPerType = goalsData['targetPerType'] as int;

          // Word types to display with their corresponding icons and colors
          final wordTypes = [
            {
              'key': 'verb',
              'display': 'Verbs',
              'iconPath': 'assets/icons/verb.svg',
              'color': AppColors.infoLight,
              'count': currentProgress['verb'] ?? 0,
            },
            {
              'key': 'noun',
              'display': 'Nouns',
              'iconPath': 'assets/icons/noun.svg',
              'color': AppColors.successLight,
              'count': currentProgress['noun'] ?? 0,
            },
            {
              'key': 'adjective',
              'display': 'Adjectives',
              'iconPath': 'assets/icons/adjective.svg',
              'color': AppColors.failureLight,
              'count': currentProgress['adjective'] ?? 0,
            },
            {
              'key': 'adverb',
              'display': 'Adverbs',
              'iconPath': 'assets/icons/adverb.svg',
              'color': AppColors.primaryLight,
              'count': currentProgress['adverb'] ?? 0,
            },
          ];

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and date range
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Weekly Targets',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withAlpha(26),borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        goalsData['weekRange'] as String,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context)
                              .primaryColor
                              .withValues(alpha: 1),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // Card with weekly targets
              Card(
                color: AppColors.cardBackgroundLight,
                elevation: 2,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: wordTypes.map((type) {
                          final int count = type['count'] as int;
                          final double progress = count / targetPerType;
                          final String display = type['display'] as String;
                          final String iconPath = type['iconPath'] as String;
                          final Color color = type['color'] as Color;

                          return Expanded(
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 70,
                                  height: 70,
                                  child: CircularProgressWithIcon(
                                    progress: progress,
                                    iconPath: iconPath,
                                    color: color,
                                    animation: _animationController,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  display,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  "$count/$targetPerType",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.grey,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),
                      ActionButton(
                        icon: Icons.add_circle_outline,
                        label: 'Add New Card',
                        color1: AppColors.gradientButton1.withAlpha(153),color2: AppColors.gradientButton2.withAlpha(153),iconColor: Colors.white,
                        onTap: () => showVocabEntryOptions(context),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// Custom widget that combines a circular progress indicator with an icon
class CircularProgressWithIcon extends StatelessWidget {
  final double progress;
  final String iconPath;
  final Color color;
  final Animation<double> animation;
  final bool isComplete;

  const CircularProgressWithIcon({
    super.key,
    required this.progress,
    required this.iconPath,
    required this.color,
    required this.animation,
    this.isComplete = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Circular progress indicator
        AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return CustomPaint(
              size: const Size(70, 70),
              painter: CircularProgressPainter(
                progress: animation.value * (progress > 1.0 ? 1.0 : progress),
                color: isComplete || progress >= 1.0
                    ? AppColors.successLight
                    : color,
                backgroundColor: AppColors.grey.withAlpha(76),strokeWidth: 6,
              ),
            );
          },
        ),

        // Icon in the center with colored background
        Container(
          width: 45,
          height: 45,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                (isComplete || progress >= 1.0 ? AppColors.successLight : color)
                    .withAlpha(38),),
          child: Center(
            child: isComplete || progress >= 1.0
                ? const Icon(
                    Icons.check,
                    color: AppColors.successLight,
                    size: 24,
                  )
                : SvgPicture.asset(
                    iconPath,
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                  ),
          ),
        ),
      ],
    );
  }
}

/// Custom painter for the circular progress indicator
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;
  final double strokeWidth;

  CircularProgressPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the center and radius
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width / 2, size.height / 2) - strokeWidth / 2;

    // Draw the background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw the progress arc if progress > 0
    if (progress > 0) {
      final progressPaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = StrokeCap.round;

      // Draw the arc from top (-90 degrees) clockwise
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2, // Start from top (-90 degrees)
        progress * 2 * math.pi, // Progress angle in radians (full circle = 2π)
        false, // Don't fill the center
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(CircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
