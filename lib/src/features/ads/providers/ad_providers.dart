import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/ad_service.dart';
import '../services/ad_frequency_manager.dart';

/// Provider for the main AdService instance
final adServiceProvider = Provider<AdService>((ref) {
  return AdService();
});

/// Provider for the AdFrequencyManager instance
final adFrequencyManagerProvider = Provider<AdFrequencyManager>((ref) {
  return AdFrequencyManager();
});

/// Provider to track if ads are initialized
final adInitializationProvider = FutureProvider<bool>((ref) async {
  final adService = ref.read(adServiceProvider);
  await adService.initialize();
  return adService.isInitialized;
});

/// Provider to check if an ad is currently loaded and ready
final adLoadedProvider = Provider<bool>((ref) {
  final adService = ref.read(adServiceProvider);
  return adService.isAdLoaded;
});
