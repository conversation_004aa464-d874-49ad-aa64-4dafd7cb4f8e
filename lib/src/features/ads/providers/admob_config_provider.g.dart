// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admob_config_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$admobConfigHash() => r'eeb89152f12f1febf62a75dc43a872c4cfb43adb';

/// See also [admobConfig].
@ProviderFor(admobConfig)
final admobConfigProvider = AutoDisposeFutureProvider<AdMobConfig>.internal(
  admobConfig,
  name: r'admobConfigProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$admobConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdmobConfigRef = AutoDisposeFutureProviderRef<AdMobConfig>;
String _$admobConfigStreamHash() => r'bd87c22e764decde2c4b2e33d05f328990404762';

/// See also [admobConfigStream].
@ProviderFor(admobConfigStream)
final admobConfigStreamProvider =
    AutoDisposeStreamProvider<AdMobConfig>.internal(
  admobConfigStream,
  name: r'admobConfigStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$admobConfigStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdmobConfigStreamRef = AutoDisposeStreamProviderRef<AdMobConfig>;
String _$admobConfigServiceHash() =>
    r'7658c478758c9f9d65f831eba3ed8384ddafe086';

/// See also [admobConfigService].
@ProviderFor(admobConfigService)
final admobConfigServiceProvider =
    AutoDisposeProvider<AdMobConfigService>.internal(
  admobConfigService,
  name: r'admobConfigServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$admobConfigServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdmobConfigServiceRef = AutoDisposeProviderRef<AdMobConfigService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
