import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/ads/models/admob_config_model.dart';

part 'admob_config_provider.g.dart';

@riverpod
Future<AdMobConfig> admobConfig(Ref ref) async {
  try {
    final admobConfigDoc = await FirebaseFirestore.instance
        .collection('admin_config')
        .doc('admob_settings')
        .get();

    if (!admobConfigDoc.exists) {
      debugPrint('AdMob config document does not exist, using defaults');
      return const AdMobConfig();
    }

    return AdMobConfig.fromFirestore(admobConfigDoc.data()!);
  } catch (e) {
    debugPrint('Error fetching AdMob config: $e');
    return const AdMobConfig();
  }
}

@riverpod
Stream<AdMobConfig> admobConfigStream(Ref ref) {
  return FirebaseFirestore.instance
      .collection('admin_config')
      .doc('admob_settings')
      .snapshots()
      .map((doc) {
    if (!doc.exists) {
      debugPrint('AdMob config document does not exist, using defaults');
      return const AdMobConfig();
    }
    return AdMobConfig.fromFirestore(doc.data()!);
  });
}

/// Service class to manage AdMob configuration
class AdMobConfigService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Update AdMob configuration in Firestore
  Future<void> updateAdMobConfig(AdMobConfig config) async {
    try {
      await _firestore
          .collection('admin_config')
          .doc('admob_settings')
          .set(config.toFirestore());
      debugPrint('AdMob config updated successfully');
    } catch (e) {
      debugPrint('Error updating AdMob config: $e');
      rethrow;
    }
  }

  /// Initialize AdMob configuration with default values
  Future<void> initializeAdMobConfig() async {
    try {
      final doc = await _firestore
          .collection('admin_config')
          .doc('admob_settings')
          .get();

      if (!doc.exists) {
        const defaultConfig = AdMobConfig();
        await _firestore
            .collection('admin_config')
            .doc('admob_settings')
            .set(defaultConfig.toFirestore());
        debugPrint('AdMob config initialized with default values');
      }
    } catch (e) {
      debugPrint('Error initializing AdMob config: $e');
    }
  }
}

@riverpod
AdMobConfigService admobConfigService(Ref ref) {
  return AdMobConfigService();
}
