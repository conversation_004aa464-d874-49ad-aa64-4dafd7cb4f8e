import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ad_trigger.dart';

/// Manages ad display frequency and limits to ensure good user experience
class AdFrequencyManager {
  static const String _quizCompletionCount = 'ad_quiz_completion_count';
  static const String _lastQuizAdDate = 'ad_last_quiz_ad_date';
  static const String _wordCountMilestoneShownToday =
      'ad_word_count_milestone_shown_today';
  static const String _dailyWordLimitShownToday =
      'ad_daily_word_limit_shown_today';
  static const String _dailyQuizLimitShownToday =
      'ad_daily_quiz_limit_shown_today';
  static const String _appLaunchAdShownToday = 'ad_app_launch_shown_today';
  static const String _lastAppLaunchAdTime = 'ad_last_app_launch_time';
  static const String _statsNavigationCount = 'ad_stats_navigation_count';
  static const String _lastStatsAdDate = 'ad_last_stats_ad_date';
  static const String _lastResetDate = 'ad_last_reset_date';

  /// Check if an ad should be shown for the given trigger
  Future<bool> shouldShowAd(AdTrigger trigger) async {
    await _resetDailyCountersIfNeeded();

    switch (trigger) {
      case AdTrigger.quizCompletion:
        return await _shouldShowQuizAd();
      case AdTrigger.wordCountMilestone:
        return await _shouldShowDailyLimitAd(_wordCountMilestoneShownToday);
      case AdTrigger.dailyWordLimit:
        return await _shouldShowDailyLimitAd(_dailyWordLimitShownToday);
      case AdTrigger.dailyQuizLimit:
        return await _shouldShowDailyLimitAd(_dailyQuizLimitShownToday);
      case AdTrigger.appLaunch:
        return await _shouldShowAppLaunchAd();
      case AdTrigger.statsNavigation:
        return await _shouldShowStatsAd();
    }
  }

  /// Record that an ad was shown for the given trigger
  Future<void> recordAdShown(AdTrigger trigger) async {
    final prefs = await SharedPreferences.getInstance();
    final today = _getTodayString();

    switch (trigger) {
      case AdTrigger.quizCompletion:
        await prefs.setInt(_quizCompletionCount, 0); // Reset counter
        await prefs.setString(_lastQuizAdDate, today);
        break;
      case AdTrigger.wordCountMilestone:
        await prefs.setBool(_wordCountMilestoneShownToday, true);
        break;
      case AdTrigger.dailyWordLimit:
        await prefs.setBool(_dailyWordLimitShownToday, true);
        break;
      case AdTrigger.dailyQuizLimit:
        await prefs.setBool(_dailyQuizLimitShownToday, true);
        break;
      case AdTrigger.appLaunch:
        await prefs.setBool(_appLaunchAdShownToday, true);
        await prefs.setInt(
            _lastAppLaunchAdTime, DateTime.now().millisecondsSinceEpoch);
        break;
      case AdTrigger.statsNavigation:
        await prefs.setInt(_statsNavigationCount, 0); // Reset counter
        await prefs.setString(_lastStatsAdDate, today);
        break;
    }

    debugPrint(
        'AdFrequencyManager: Recorded ad shown for ${trigger.debugName}');
  }

  /// Record a quiz completion (increment counter)
  Future<void> recordQuizCompletion() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt(_quizCompletionCount) ?? 0;
    await prefs.setInt(_quizCompletionCount, currentCount + 1);
    debugPrint(
        'AdFrequencyManager: Quiz completion count: ${currentCount + 1}');
  }

  /// Record a stats screen navigation (increment counter)
  Future<void> recordStatsNavigation() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt(_statsNavigationCount) ?? 0;
    await prefs.setInt(_statsNavigationCount, currentCount + 1);
    debugPrint(
        'AdFrequencyManager: Stats navigation count: ${currentCount + 1}');
  }

  /// Check if quiz completion ad should be shown (every 2 quizzes)
  Future<bool> _shouldShowQuizAd() async {
    final prefs = await SharedPreferences.getInstance();
    final completionCount = prefs.getInt(_quizCompletionCount) ?? 0;
    return completionCount >= 2;
  }

  /// Check if daily limit ad should be shown (once per session)
  Future<bool> _shouldShowDailyLimitAd(String prefKey) async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(prefKey) ?? false);
  }

  /// Check if app launch ad should be shown (once per session, 30min cooldown)
  Future<bool> _shouldShowAppLaunchAd() async {
    final prefs = await SharedPreferences.getInstance();

    // Check if already shown today
    if (prefs.getBool(_appLaunchAdShownToday) ?? false) {
      return false;
    }

    // Check 30-minute cooldown
    final lastAdTime = prefs.getInt(_lastAppLaunchAdTime) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final thirtyMinutesInMs = 30 * 60 * 1000;

    return (now - lastAdTime) >= thirtyMinutesInMs;
  }

  /// Check if stats navigation ad should be shown (every 3 navigations)
  Future<bool> _shouldShowStatsAd() async {
    final prefs = await SharedPreferences.getInstance();
    final navigationCount = prefs.getInt(_statsNavigationCount) ?? 0;
    return navigationCount >= 3;
  }

  /// Reset daily counters if a new day has started
  Future<void> _resetDailyCountersIfNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final today = _getTodayString();
    final lastResetDate = prefs.getString(_lastResetDate);

    if (lastResetDate != today) {
      // New day - reset daily counters
      await prefs.setBool(_wordCountMilestoneShownToday, false);
      await prefs.setBool(_dailyWordLimitShownToday, false);
      await prefs.setBool(_dailyQuizLimitShownToday, false);
      await prefs.setBool(_appLaunchAdShownToday, false);
      await prefs.setString(_lastResetDate, today);
      debugPrint('AdFrequencyManager: Reset daily counters for $today');
    }
  }

  /// Get today's date as a string (YYYY-MM-DD)
  String _getTodayString() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// Clear all ad frequency data (for testing/debugging)
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_quizCompletionCount);
    await prefs.remove(_lastQuizAdDate);
    await prefs.remove(_wordCountMilestoneShownToday);
    await prefs.remove(_dailyWordLimitShownToday);
    await prefs.remove(_dailyQuizLimitShownToday);
    await prefs.remove(_appLaunchAdShownToday);
    await prefs.remove(_lastAppLaunchAdTime);
    await prefs.remove(_statsNavigationCount);
    await prefs.remove(_lastStatsAdDate);
    await prefs.remove(_lastResetDate);
    debugPrint('AdFrequencyManager: Cleared all data');
  }
}
