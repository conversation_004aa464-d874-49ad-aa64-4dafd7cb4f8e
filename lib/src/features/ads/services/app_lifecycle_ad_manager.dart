import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ad_trigger.dart';
import '../providers/ad_providers.dart';
import '../../subscriptions/providers/subscription_notifier.dart';

/// Manages app lifecycle events for ad triggers
class AppLifecycleAdManager extends WidgetsBindingObserver {
  final Ref _ref;
  DateTime _lastActivityTime = DateTime.now();
  bool _hasShownAppLaunchAd = false;

  AppLifecycleAdManager(this._ref) {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _lastActivityTime = DateTime.now();
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        break;
    }
  }

  /// Handle app resumed event
  void _handleAppResumed() {
    final now = DateTime.now();
    final timeSinceLastActivity = now.difference(_lastActivityTime);

    // Show ad if app was inactive for more than 30 minutes or on first launch
    if (!_hasShownAppLaunchAd || timeSinceLastActivity.inMinutes >= 30) {
      _showAppLaunchAd();
    }

    _lastActivityTime = now;
  }

  /// Show app launch ad
  void _showAppLaunchAd() async {
    // Add a small delay to ensure UI is ready
    await Future.delayed(const Duration(seconds: 2));

    try {
      final adService = _ref.read(adServiceProvider);
      final isPremium = _ref.read(subscriptionStateProvider);

      await adService.showAdIfEligible(
        AdTrigger.appLaunch,
        isPremium,
        onAdShown: () {
          debugPrint('App launch ad shown');
          _hasShownAppLaunchAd = true;
        },
        onAdClosed: () {
          debugPrint('App launch ad closed');
        },
        onAdFailed: () {
          debugPrint('App launch ad failed to show');
        },
        onSkipped: () {
          debugPrint('App launch ad skipped');
        },
      );
    } catch (e) {
      debugPrint('Error showing app launch ad: $e');
    }
  }

  /// Dispose of the observer
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }
}

/// Provider for the app lifecycle ad manager
final appLifecycleAdManagerProvider = Provider<AppLifecycleAdManager>((ref) {
  final manager = AppLifecycleAdManager(ref);
  ref.onDispose(() {
    manager.dispose();
  });
  return manager;
});
