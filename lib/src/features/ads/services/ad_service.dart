import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../../../core/config/ad_config.dart';
import '../models/ad_trigger.dart';
import 'ad_frequency_manager.dart';

/// Main service for managing ads in the Vocadex app
/// Only shows ads to free users (not premium users)
class AdService {
  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  final AdFrequencyManager _frequencyManager = AdFrequencyManager();
  InterstitialAd? _interstitialAd;
  bool _isInitialized = false;
  bool _isLoadingAd = false;

  /// Initialize the ad service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await MobileAds.instance.initialize();
      await _loadInterstitialAd();
      _isInitialized = true;
      debugPrint('AdService: Initialized successfully');
    } catch (e) {
      debugPrint('AdService: Failed to initialize: $e');
    }
  }

  /// Check if an ad should be shown for the given trigger and premium status
  Future<bool> shouldShowAd(AdTrigger trigger, bool isPremium) async {
    // Never show ads to premium users
    if (isPremium) {
      debugPrint('AdService: Skipping ad for premium user');
      return false;
    }

    // Check if service is initialized
    if (!_isInitialized) {
      debugPrint('AdService: Not initialized, skipping ad');
      return false;
    }

    // Check frequency limits
    final shouldShow = await _frequencyManager.shouldShowAd(trigger);
    debugPrint('AdService: Should show ${trigger.debugName} ad: $shouldShow');
    return shouldShow;
  }

  /// Show an interstitial ad for the given trigger
  Future<void> showInterstitialAd(AdTrigger trigger, {
    VoidCallback? onAdShown,
    VoidCallback? onAdClosed,
    VoidCallback? onAdFailed,
  }) async {
    if (_interstitialAd == null) {
      debugPrint('AdService: No interstitial ad loaded');
      onAdFailed?.call();
      return;
    }

    try {
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (ad) {
          debugPrint('AdService: Interstitial ad shown for ${trigger.debugName}');
          onAdShown?.call();
        },
        onAdDismissedFullScreenContent: (ad) {
          debugPrint('AdService: Interstitial ad dismissed for ${trigger.debugName}');
          ad.dispose();
          _interstitialAd = null;
          onAdClosed?.call();
          
          // Record that ad was shown and preload next ad
          _frequencyManager.recordAdShown(trigger);
          _loadInterstitialAd();
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          debugPrint('AdService: Failed to show interstitial ad: $error');
          ad.dispose();
          _interstitialAd = null;
          onAdFailed?.call();
          
          // Try to load a new ad
          _loadInterstitialAd();
        },
      );

      await _interstitialAd!.show();
    } catch (e) {
      debugPrint('AdService: Error showing interstitial ad: $e');
      onAdFailed?.call();
    }
  }

  /// Show ad with automatic premium check and frequency management
  Future<void> showAdIfEligible(AdTrigger trigger, bool isPremium, {
    VoidCallback? onAdShown,
    VoidCallback? onAdClosed,
    VoidCallback? onAdFailed,
    VoidCallback? onSkipped,
  }) async {
    final shouldShow = await shouldShowAd(trigger, isPremium);
    
    if (shouldShow) {
      await showInterstitialAd(
        trigger,
        onAdShown: onAdShown,
        onAdClosed: onAdClosed,
        onAdFailed: onAdFailed,
      );
    } else {
      debugPrint('AdService: Ad skipped for ${trigger.debugName}');
      onSkipped?.call();
    }
  }

  /// Record a quiz completion for frequency tracking
  Future<void> recordQuizCompletion() async {
    await _frequencyManager.recordQuizCompletion();
  }

  /// Record a stats navigation for frequency tracking
  Future<void> recordStatsNavigation() async {
    await _frequencyManager.recordStatsNavigation();
  }

  /// Load an interstitial ad
  Future<void> _loadInterstitialAd() async {
    if (_isLoadingAd) return;
    
    _isLoadingAd = true;
    
    try {
      await InterstitialAd.load(
        adUnitId: AdConfig.getInterstitialAdUnitId(),
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            _isLoadingAd = false;
            debugPrint('AdService: Interstitial ad loaded');
          },
          onAdFailedToLoad: (error) {
            _isLoadingAd = false;
            debugPrint('AdService: Failed to load interstitial ad: $error');
            
            // Retry loading after a delay
            Future.delayed(const Duration(seconds: 30), () {
              if (_interstitialAd == null && _isInitialized) {
                _loadInterstitialAd();
              }
            });
          },
        ),
      );
    } catch (e) {
      _isLoadingAd = false;
      debugPrint('AdService: Error loading interstitial ad: $e');
    }
  }

  /// Dispose of resources
  void dispose() {
    _interstitialAd?.dispose();
    _interstitialAd = null;
    _isInitialized = false;
    debugPrint('AdService: Disposed');
  }

  /// Get current ad loading status
  bool get isAdLoaded => _interstitialAd != null;
  
  /// Get initialization status
  bool get isInitialized => _isInitialized;

  /// Clear all frequency data (for testing/debugging)
  Future<void> clearFrequencyData() async {
    await _frequencyManager.clearAllData();
  }
}
