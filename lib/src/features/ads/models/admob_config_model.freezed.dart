// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admob_config_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdMobConfig _$AdMobConfigFromJson(Map<String, dynamic> json) {
  return _AdMobConfig.fromJson(json);
}

/// @nodoc
mixin _$AdMobConfig {
// Android AdMob IDs
  String get appIdAndroid => throw _privateConstructorUsedError;
  String get interstitialAdUnitIdAndroid => throw _privateConstructorUsedError;
  String get bannerAdUnitIdAndroid =>
      throw _privateConstructorUsedError; // iOS AdMob IDs
  String get appIdiOS => throw _privateConstructorUsedError;
  String get interstitialAdUnitIdiOS => throw _privateConstructorUsedError;
  String get bannerAdUnitIdiOS => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this AdMobConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdMobConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdMobConfigCopyWith<AdMobConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdMobConfigCopyWith<$Res> {
  factory $AdMobConfigCopyWith(
          AdMobConfig value, $Res Function(AdMobConfig) then) =
      _$AdMobConfigCopyWithImpl<$Res, AdMobConfig>;
  @useResult
  $Res call(
      {String appIdAndroid,
      String interstitialAdUnitIdAndroid,
      String bannerAdUnitIdAndroid,
      String appIdiOS,
      String interstitialAdUnitIdiOS,
      String bannerAdUnitIdiOS,
      DateTime? lastUpdated});
}

/// @nodoc
class _$AdMobConfigCopyWithImpl<$Res, $Val extends AdMobConfig>
    implements $AdMobConfigCopyWith<$Res> {
  _$AdMobConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdMobConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appIdAndroid = null,
    Object? interstitialAdUnitIdAndroid = null,
    Object? bannerAdUnitIdAndroid = null,
    Object? appIdiOS = null,
    Object? interstitialAdUnitIdiOS = null,
    Object? bannerAdUnitIdiOS = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      appIdAndroid: null == appIdAndroid
          ? _value.appIdAndroid
          : appIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      interstitialAdUnitIdAndroid: null == interstitialAdUnitIdAndroid
          ? _value.interstitialAdUnitIdAndroid
          : interstitialAdUnitIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      bannerAdUnitIdAndroid: null == bannerAdUnitIdAndroid
          ? _value.bannerAdUnitIdAndroid
          : bannerAdUnitIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      appIdiOS: null == appIdiOS
          ? _value.appIdiOS
          : appIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      interstitialAdUnitIdiOS: null == interstitialAdUnitIdiOS
          ? _value.interstitialAdUnitIdiOS
          : interstitialAdUnitIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      bannerAdUnitIdiOS: null == bannerAdUnitIdiOS
          ? _value.bannerAdUnitIdiOS
          : bannerAdUnitIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdMobConfigImplCopyWith<$Res>
    implements $AdMobConfigCopyWith<$Res> {
  factory _$$AdMobConfigImplCopyWith(
          _$AdMobConfigImpl value, $Res Function(_$AdMobConfigImpl) then) =
      __$$AdMobConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String appIdAndroid,
      String interstitialAdUnitIdAndroid,
      String bannerAdUnitIdAndroid,
      String appIdiOS,
      String interstitialAdUnitIdiOS,
      String bannerAdUnitIdiOS,
      DateTime? lastUpdated});
}

/// @nodoc
class __$$AdMobConfigImplCopyWithImpl<$Res>
    extends _$AdMobConfigCopyWithImpl<$Res, _$AdMobConfigImpl>
    implements _$$AdMobConfigImplCopyWith<$Res> {
  __$$AdMobConfigImplCopyWithImpl(
      _$AdMobConfigImpl _value, $Res Function(_$AdMobConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdMobConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appIdAndroid = null,
    Object? interstitialAdUnitIdAndroid = null,
    Object? bannerAdUnitIdAndroid = null,
    Object? appIdiOS = null,
    Object? interstitialAdUnitIdiOS = null,
    Object? bannerAdUnitIdiOS = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$AdMobConfigImpl(
      appIdAndroid: null == appIdAndroid
          ? _value.appIdAndroid
          : appIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      interstitialAdUnitIdAndroid: null == interstitialAdUnitIdAndroid
          ? _value.interstitialAdUnitIdAndroid
          : interstitialAdUnitIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      bannerAdUnitIdAndroid: null == bannerAdUnitIdAndroid
          ? _value.bannerAdUnitIdAndroid
          : bannerAdUnitIdAndroid // ignore: cast_nullable_to_non_nullable
              as String,
      appIdiOS: null == appIdiOS
          ? _value.appIdiOS
          : appIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      interstitialAdUnitIdiOS: null == interstitialAdUnitIdiOS
          ? _value.interstitialAdUnitIdiOS
          : interstitialAdUnitIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      bannerAdUnitIdiOS: null == bannerAdUnitIdiOS
          ? _value.bannerAdUnitIdiOS
          : bannerAdUnitIdiOS // ignore: cast_nullable_to_non_nullable
              as String,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdMobConfigImpl extends _AdMobConfig {
  const _$AdMobConfigImpl(
      {this.appIdAndroid = 'ca-app-pub-3940256099942544~3347511713',
      this.interstitialAdUnitIdAndroid =
          'ca-app-pub-3940256099942544/1033173712',
      this.bannerAdUnitIdAndroid = 'ca-app-pub-3940256099942544/6300978111',
      this.appIdiOS = 'ca-app-pub-3940256099942544~1458002511',
      this.interstitialAdUnitIdiOS = 'ca-app-pub-3940256099942544/4411468910',
      this.bannerAdUnitIdiOS = 'ca-app-pub-3940256099942544/2934735716',
      this.lastUpdated})
      : super._();

  factory _$AdMobConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdMobConfigImplFromJson(json);

// Android AdMob IDs
  @override
  @JsonKey()
  final String appIdAndroid;
  @override
  @JsonKey()
  final String interstitialAdUnitIdAndroid;
  @override
  @JsonKey()
  final String bannerAdUnitIdAndroid;
// iOS AdMob IDs
  @override
  @JsonKey()
  final String appIdiOS;
  @override
  @JsonKey()
  final String interstitialAdUnitIdiOS;
  @override
  @JsonKey()
  final String bannerAdUnitIdiOS;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'AdMobConfig(appIdAndroid: $appIdAndroid, interstitialAdUnitIdAndroid: $interstitialAdUnitIdAndroid, bannerAdUnitIdAndroid: $bannerAdUnitIdAndroid, appIdiOS: $appIdiOS, interstitialAdUnitIdiOS: $interstitialAdUnitIdiOS, bannerAdUnitIdiOS: $bannerAdUnitIdiOS, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdMobConfigImpl &&
            (identical(other.appIdAndroid, appIdAndroid) ||
                other.appIdAndroid == appIdAndroid) &&
            (identical(other.interstitialAdUnitIdAndroid,
                    interstitialAdUnitIdAndroid) ||
                other.interstitialAdUnitIdAndroid ==
                    interstitialAdUnitIdAndroid) &&
            (identical(other.bannerAdUnitIdAndroid, bannerAdUnitIdAndroid) ||
                other.bannerAdUnitIdAndroid == bannerAdUnitIdAndroid) &&
            (identical(other.appIdiOS, appIdiOS) ||
                other.appIdiOS == appIdiOS) &&
            (identical(
                    other.interstitialAdUnitIdiOS, interstitialAdUnitIdiOS) ||
                other.interstitialAdUnitIdiOS == interstitialAdUnitIdiOS) &&
            (identical(other.bannerAdUnitIdiOS, bannerAdUnitIdiOS) ||
                other.bannerAdUnitIdiOS == bannerAdUnitIdiOS) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      appIdAndroid,
      interstitialAdUnitIdAndroid,
      bannerAdUnitIdAndroid,
      appIdiOS,
      interstitialAdUnitIdiOS,
      bannerAdUnitIdiOS,
      lastUpdated);

  /// Create a copy of AdMobConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdMobConfigImplCopyWith<_$AdMobConfigImpl> get copyWith =>
      __$$AdMobConfigImplCopyWithImpl<_$AdMobConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdMobConfigImplToJson(
      this,
    );
  }
}

abstract class _AdMobConfig extends AdMobConfig {
  const factory _AdMobConfig(
      {final String appIdAndroid,
      final String interstitialAdUnitIdAndroid,
      final String bannerAdUnitIdAndroid,
      final String appIdiOS,
      final String interstitialAdUnitIdiOS,
      final String bannerAdUnitIdiOS,
      final DateTime? lastUpdated}) = _$AdMobConfigImpl;
  const _AdMobConfig._() : super._();

  factory _AdMobConfig.fromJson(Map<String, dynamic> json) =
      _$AdMobConfigImpl.fromJson;

// Android AdMob IDs
  @override
  String get appIdAndroid;
  @override
  String get interstitialAdUnitIdAndroid;
  @override
  String get bannerAdUnitIdAndroid; // iOS AdMob IDs
  @override
  String get appIdiOS;
  @override
  String get interstitialAdUnitIdiOS;
  @override
  String get bannerAdUnitIdiOS;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of AdMobConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdMobConfigImplCopyWith<_$AdMobConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
