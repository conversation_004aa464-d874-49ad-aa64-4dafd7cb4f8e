// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admob_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdMobConfigImpl _$$AdMobConfigImplFromJson(Map<String, dynamic> json) =>
    _$AdMobConfigImpl(
      appIdAndroid: json['appIdAndroid'] as String? ??
          'ca-app-pub-3940256099942544~3347511713',
      interstitialAdUnitIdAndroid:
          json['interstitialAdUnitIdAndroid'] as String? ??
              'ca-app-pub-3940256099942544/1033173712',
      bannerAdUnitIdAndroid: json['bannerAdUnitIdAndroid'] as String? ??
          'ca-app-pub-3940256099942544/6300978111',
      appIdiOS: json['appIdiOS'] as String? ??
          'ca-app-pub-3940256099942544~1458002511',
      interstitialAdUnitIdiOS: json['interstitialAdUnitIdiOS'] as String? ??
          'ca-app-pub-3940256099942544/4411468910',
      bannerAdUnitIdiOS: json['bannerAdUnitIdiOS'] as String? ??
          'ca-app-pub-3940256099942544/2934735716',
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$AdMobConfigImplToJson(_$AdMobConfigImpl instance) =>
    <String, dynamic>{
      'appIdAndroid': instance.appIdAndroid,
      'interstitialAdUnitIdAndroid': instance.interstitialAdUnitIdAndroid,
      'bannerAdUnitIdAndroid': instance.bannerAdUnitIdAndroid,
      'appIdiOS': instance.appIdiOS,
      'interstitialAdUnitIdiOS': instance.interstitialAdUnitIdiOS,
      'bannerAdUnitIdiOS': instance.bannerAdUnitIdiOS,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };
