import 'package:freezed_annotation/freezed_annotation.dart';

part 'admob_config_model.freezed.dart';
part 'admob_config_model.g.dart';

@freezed
class AdMobConfig with _$AdMobConfig {
  const factory AdMobConfig({
    // Android AdMob IDs
    @Default('ca-app-pub-3940256099942544~3347511713') String appIdAndroid,
    @Default('ca-app-pub-3940256099942544/1033173712') String interstitialAdUnitIdAndroid,
    @Default('ca-app-pub-3940256099942544/6300978111') String bannerAdUnitIdAndroid,
    
    // iOS AdMob IDs
    @Default('ca-app-pub-3940256099942544~1458002511') String appIdiOS,
    @Default('ca-app-pub-3940256099942544/4411468910') String interstitialAdUnitIdiOS,
    @Default('ca-app-pub-3940256099942544/2934735716') String bannerAdUnitIdiOS,
    
    DateTime? lastUpdated,
  }) = _AdMobConfig;

  const AdMobConfig._();

  factory AdMobConfig.fromJson(Map<String, dynamic> json) =>
      _$AdMobConfigFromJson(json);

  factory AdMobConfig.fromFirestore(Map<String, dynamic> data) {
    return AdMobConfig(
      appIdAndroid: data['appIdAndroid'] ?? 'ca-app-pub-3940256099942544~3347511713',
      interstitialAdUnitIdAndroid: data['interstitialAdUnitIdAndroid'] ?? 'ca-app-pub-3940256099942544/1033173712',
      bannerAdUnitIdAndroid: data['bannerAdUnitIdAndroid'] ?? 'ca-app-pub-3940256099942544/6300978111',
      appIdiOS: data['appIdiOS'] ?? 'ca-app-pub-3940256099942544~1458002511',
      interstitialAdUnitIdiOS: data['interstitialAdUnitIdiOS'] ?? 'ca-app-pub-3940256099942544/4411468910',
      bannerAdUnitIdiOS: data['bannerAdUnitIdiOS'] ?? 'ca-app-pub-3940256099942544/2934735716',
      lastUpdated: data['lastUpdated']?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'appIdAndroid': appIdAndroid,
      'interstitialAdUnitIdAndroid': interstitialAdUnitIdAndroid,
      'bannerAdUnitIdAndroid': bannerAdUnitIdAndroid,
      'appIdiOS': appIdiOS,
      'interstitialAdUnitIdiOS': interstitialAdUnitIdiOS,
      'bannerAdUnitIdiOS': bannerAdUnitIdiOS,
      'lastUpdated': DateTime.now(),
    };
  }
}
