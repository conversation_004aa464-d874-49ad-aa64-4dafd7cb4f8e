/// Enum defining different triggers for showing ads
enum AdTrigger {
  /// Show ad after completing a quiz (every 2 quizzes)
  quizCompletion,

  /// Show ad after user adds 3rd word in a day
  wordCountMilestone,

  /// Show ad when free user tries to add 6th word in a day
  dailyWordLimit,

  /// Show ad when free user tries to start 4th quiz in a day
  dailyQuizLimit,

  /// Show ad on app launch or after 30 minutes of inactivity
  appLaunch,

  /// Show ad when navigating to stats screen (every 3 navigations)
  statsNavigation,
}

/// Extension to provide human-readable names for ad triggers
extension AdTriggerExtension on AdTrigger {
  String get displayName {
    switch (this) {
      case AdTrigger.quizCompletion:
        return 'Quiz Completion';
      case AdTrigger.wordCountMilestone:
        return 'Word Count Milestone';
      case AdTrigger.dailyWordLimit:
        return 'Daily Word Limit';
      case AdTrigger.dailyQuizLimit:
        return 'Daily Quiz Limit';
      case AdTrigger.appLaunch:
        return 'App Launch';
      case AdTrigger.statsNavigation:
        return 'Stats Navigation';
    }
  }

  String get debugName {
    switch (this) {
      case AdTrigger.quizCompletion:
        return 'quiz_completion';
      case AdTrigger.wordCountMilestone:
        return 'word_count_milestone';
      case AdTrigger.dailyWordLimit:
        return 'daily_word_limit';
      case AdTrigger.dailyQuizLimit:
        return 'daily_quiz_limit';
      case AdTrigger.appLaunch:
        return 'app_launch';
      case AdTrigger.statsNavigation:
        return 'stats_navigation';
    }
  }
}
