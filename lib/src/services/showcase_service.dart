import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

/// Service to manage the app's showcase experience for first-time users
class ShowcaseService {
  static const String _hasSeenShowcaseKey = 'has_seen_showcase';
  static const String _hasSeenDashboardShowcaseKey =
      'has_seen_dashboard_showcase';
  static const String _hasSeenNavBarShowcaseKey = 'has_seen_navbar_showcase';
  static const String _hasSeenAddButtonShowcaseKey =
      'has_seen_add_button_showcase';
  static const String _hasSeenVocabDeckShowcaseKey =
      'has_seen_vocab_deck_showcase';

  /// Check if user has seen the entire showcase experience
  Future<bool> hasSeenShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenShowcaseKey) ?? false;
  }

  /// Mark the entire showcase as seen
  Future<void> markShowcaseAsSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenShowcaseKey, true);
  }

  /// Check if user has seen the dashboard showcase
  Future<bool> hasSeenDashboardShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenDashboardShowcaseKey) ?? false;
  }

  /// Mark the dashboard showcase as seen
  Future<void> markDashboardShowcaseAsSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenDashboardShowcaseKey, true);
  }

  /// Check if user has seen the navbar showcase
  Future<bool> hasSeenNavBarShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenNavBarShowcaseKey) ?? false;
  }

  /// Mark the navbar showcase as seen
  Future<void> markNavBarShowcaseAsSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenNavBarShowcaseKey, true);
  }

  /// Check if user has seen the add button showcase
  Future<bool> hasSeenAddButtonShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenAddButtonShowcaseKey) ?? false;
  }

  /// Mark the add button showcase as seen
  Future<void> markAddButtonShowcaseAsSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenAddButtonShowcaseKey, true);
  }

  /// Check if user has seen the vocab deck showcase
  Future<bool> hasSeenVocabDeckShowcase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_hasSeenVocabDeckShowcaseKey) ?? false;
  }

  /// Mark the vocab deck showcase as seen
  Future<void> markVocabDeckShowcaseAsSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_hasSeenVocabDeckShowcaseKey, true);
  }

  /// Reset all showcase preferences (for testing)
  Future<void> resetAllShowcases() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_hasSeenShowcaseKey);
    await prefs.remove(_hasSeenDashboardShowcaseKey);
    await prefs.remove(_hasSeenNavBarShowcaseKey);
    await prefs.remove(_hasSeenAddButtonShowcaseKey);
    await prefs.remove(_hasSeenVocabDeckShowcaseKey);
  }
}

/// Provider for the showcase service
final showcaseServiceProvider = Provider<ShowcaseService>((ref) {
  return ShowcaseService();
});

/// Provider to check if the user has seen the entire showcase
final hasSeenShowcaseProvider = FutureProvider<bool>((ref) async {
  final showcaseService = ref.read(showcaseServiceProvider);
  return await showcaseService.hasSeenShowcase();
});

/// Provider to check if the user has seen the dashboard showcase
final hasSeenDashboardShowcaseProvider = FutureProvider<bool>((ref) async {
  final showcaseService = ref.read(showcaseServiceProvider);
  return await showcaseService.hasSeenDashboardShowcase();
});

/// Provider to check if the user has seen the navbar showcase
final hasSeenNavBarShowcaseProvider = FutureProvider<bool>((ref) async {
  final showcaseService = ref.read(showcaseServiceProvider);
  return await showcaseService.hasSeenNavBarShowcase();
});

/// Provider to check if the user has seen the add button showcase
final hasSeenAddButtonShowcaseProvider = FutureProvider<bool>((ref) async {
  final showcaseService = ref.read(showcaseServiceProvider);
  return await showcaseService.hasSeenAddButtonShowcase();
});

/// Provider to check if the user has seen the vocab deck showcase
final hasSeenVocabDeckShowcaseProvider = FutureProvider<bool>((ref) async {
  final showcaseService = ref.read(showcaseServiceProvider);
  return await showcaseService.hasSeenVocabDeckShowcase();
});
