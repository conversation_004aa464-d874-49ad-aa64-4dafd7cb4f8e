import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  Future clearLocalData() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.clear();
  }

  Future setNotificationSubscription(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('n_subscribe', value);
  }

  Future<bool> getNotificationSubscription() async {
    final prefs = await SharedPreferences.getInstance();
    bool value = prefs.getBool('n_subscribe') ?? false;
    return value;
  }

  Future<bool> isGuestUser() async {
    final prefs = await SharedPreferences.getInstance();
    final bool isGuest = prefs.getBool('guest_user') ?? false;
    return isGuest;
  }

  Future setGuestUser() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('guest_user', true);
  }

  Future<bool> hasCompletedOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    final hasCompletedOnboarding =
        prefs.getBool('hasCompletedOnboarding') ?? false;
    return hasCompletedOnboarding;
  }

  Future<void> setOnboardingComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasCompletedOnboarding', true);
  }

  Future<void> saveOnboardingDataLocally(
      Map<String, dynamic> onboardingData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('onboardingData', jsonEncode(onboardingData));
  }

  Future<Map<String, dynamic>?> getOnboardingDataLocally() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = prefs.getString('onboardingData');
    if (jsonData != null) {
      return jsonDecode(jsonData) as Map<String, dynamic>;
    }
    return null;
  }

  // Save weekly goals data locally
  Future<void> saveWeeklyGoalsData(Map<String, dynamic> weeklyGoals) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('weekly_goals', jsonEncode(weeklyGoals));

    // Also save the last reset date
    final now = DateTime.now();
    await prefs.setString('weekly_goals_last_reset', now.toIso8601String());
  }

  // Get weekly goals data from local storage
  Future<Map<String, dynamic>?> getWeeklyGoalsData() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = prefs.getString('weekly_goals');
    if (jsonData != null) {
      return jsonDecode(jsonData) as Map<String, dynamic>;
    }
    return null;
  }

  // Get the last reset date for weekly goals
  Future<DateTime?> getWeeklyGoalsLastReset() async {
    final prefs = await SharedPreferences.getInstance();
    final dateStr = prefs.getString('weekly_goals_last_reset');
    if (dateStr != null) {
      try {
        return DateTime.parse(dateStr);
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}
