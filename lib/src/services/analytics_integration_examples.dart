import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';

/// Examples of how to integrate analytics into your existing services
/// Copy these patterns into your actual service files

class AnalyticsIntegrationExamples {
  
  // === 1. ONBOARDING COMPLETION ===
  // Add this to your onboarding completion logic
  static Future<void> trackOnboardingComplete(Map<String, dynamic> answers) async {
    await AnalyticsService.instance.trackOnboardingComplete({
      'learning_goals': answers['learning_goals'],
      'proficiency_level': answers['proficiency_level'],
      'daily_time_commitment': answers['daily_time_commitment'],
      'preferred_word_types': answers['preferred_word_types'],
      'completion_time_minutes': answers['completion_time_minutes'] ?? 0,
    });
  }

  // === 2. WORD ADDITION TRACKING ===
  // Add this to your VocabSaveService.saveVocabulary method
  static Future<void> trackWordAddition(
    VocabCard card, 
    WidgetRef ref,
    bool isFromGlobalRepo,
  ) async {
    final isPremium = ref.read(subscriptionStateProvider);
    
    // Get daily count (you'll need to implement this counter)
    final dailyWordsCount = await _getDailyWordsCount();
    
    await AnalyticsService.instance.trackWordAdded(
      card,
      isPremium: isPremium,
      isFromGlobalRepo: isFromGlobalRepo,
      dailyWordsCount: dailyWordsCount,
    );
    
    // Track daily active users
    await AnalyticsService.instance.track('Daily Active User - Word Added');
  }

  // === 3. QUIZ TRACKING ===
  // Add this to your quiz service
  static Future<void> trackQuizStart(String quizType, int questionsCount, WidgetRef ref) async {
    final isPremium = ref.read(subscriptionStateProvider);
    final dailyQuizzesCount = await _getDailyQuizzesCount();
    
    await AnalyticsService.instance.trackQuizAttempted(
      quizType: quizType,
      questionsCount: questionsCount,
      isPremium: isPremium,
      dailyQuizzesCount: dailyQuizzesCount,
    );
  }

  static Future<void> trackQuizComplete(
    String quizType,
    int score,
    int totalQuestions,
    int timeSpent,
    WidgetRef ref,
  ) async {
    final isPremium = ref.read(subscriptionStateProvider);
    
    await AnalyticsService.instance.trackQuizCompleted(
      quizType: quizType,
      score: score,
      totalQuestions: totalQuestions,
      timeSpent: timeSpent,
      isPremium: isPremium,
    );
    
    // Track daily active users
    await AnalyticsService.instance.track('Daily Active User - Quiz Completed');
  }

  // === 4. APP USAGE TRACKING ===
  // Add this to your main app lifecycle
  static Future<void> trackAppLaunch() async {
    await AnalyticsService.instance.track('App Launched', {
      'Launch Time': DateTime.now().toIso8601String(),
      'Day of Week': DateTime.now().weekday,
      'Hour of Day': DateTime.now().hour,
    });
  }

  static Future<void> trackAppBackground() async {
    await AnalyticsService.instance.trackSessionEnd();
  }

  // === 5. PREMIUM CONVERSION TRACKING ===
  // Add this to your paywall logic
  static Future<void> trackPaywallShown(String trigger) async {
    await AnalyticsService.instance.trackPaywallShown(trigger);
  }

  static Future<void> trackSubscriptionPurchase(String productId, double price) async {
    await AnalyticsService.instance.trackSubscriptionPurchased(productId, price);
  }

  // === 6. FEATURE USAGE TRACKING ===
  static Future<void> trackFeatureUsage(String featureName, [Map<String, dynamic>? properties]) async {
    await AnalyticsService.instance.trackFeatureUsed(featureName, properties);
  }

  // Examples of feature tracking:
  static Future<void> trackPronunciationUsage(String word, String source) async {
    await AnalyticsService.instance.trackPronunciationPlayed(word, source);
  }

  static Future<void> trackSearchUsage(String query, int resultsCount) async {
    await AnalyticsService.instance.track('Search Performed', {
      'Query': query,
      'Results Count': resultsCount,
      'Query Length': query.length,
    });
  }

  static Future<void> trackFilterUsage(Map<String, dynamic> filters) async {
    await AnalyticsService.instance.track('Filters Applied', {
      'Active Filters': filters.keys.toList(),
      'Filter Count': filters.length,
      ...filters,
    });
  }

  // === 7. LEARNING PROGRESS TRACKING ===
  static Future<void> trackMasteryChange(
    String word,
    int fromLevel,
    int toLevel,
    int totalMasteredWords,
  ) async {
    final fromLevelName = _getMasteryLevelName(fromLevel);
    final toLevelName = _getMasteryLevelName(toLevel);
    
    await AnalyticsService.instance.trackWordMasteryChanged(
      word: word,
      fromLevel: fromLevelName,
      toLevel: toLevelName,
      totalMasteredWords: totalMasteredWords,
    );
  }

  static Future<void> trackWeeklyGoalCompletion(String goalType, int targetCount) async {
    await AnalyticsService.instance.trackWeeklyGoalCompleted(goalType, targetCount);
  }

  static Future<void> trackStreakAchievement(int streakDays, String streakType) async {
    await AnalyticsService.instance.track('Learning Streak Achieved', {
      'Streak Days': streakDays,
      'Streak Type': streakType, // 'daily_words', 'daily_quizzes', 'daily_login'
      'Achievement Date': DateTime.now().toIso8601String(),
    });
  }

  // === 8. AD TRACKING ===
  static Future<void> trackAdShown(String adType, String placement) async {
    await AnalyticsService.instance.trackAdShown(adType, placement);
  }

  static Future<void> trackAdClicked(String adType, String placement) async {
    await AnalyticsService.instance.trackAdClicked(adType, placement);
  }

  // === 9. ERROR TRACKING ===
  static Future<void> trackError(String errorType, String errorMessage, [Map<String, dynamic>? context]) async {
    await AnalyticsService.instance.track('Error Occurred', {
      'Error Type': errorType,
      'Error Message': errorMessage,
      'Timestamp': DateTime.now().toIso8601String(),
      ...?context,
    });
  }

  // === 10. USER AUTHENTICATION TRACKING ===
  static Future<void> trackUserSignUp(String method) async {
    await AnalyticsService.instance.track('User Signed Up', {
      'Sign Up Method': method, // 'email', 'google', 'apple'
      'Sign Up Date': DateTime.now().toIso8601String(),
    });
  }

  static Future<void> trackUserSignIn(String method) async {
    await AnalyticsService.instance.track('User Signed In', {
      'Sign In Method': method,
      'Sign In Date': DateTime.now().toIso8601String(),
    });
  }

  // === HELPER METHODS ===
  static String _getMasteryLevelName(int level) {
    if (level <= 3) return 'Wild';
    if (level <= 6) return 'Tamed';
    return 'Mastered';
  }

  static Future<int> _getDailyWordsCount() async {
    // Implement this to get today's word count from your database
    // This is a placeholder
    return 0;
  }

  static Future<int> _getDailyQuizzesCount() async {
    // Implement this to get today's quiz count from your database
    // This is a placeholder
    return 0;
  }
}

/// App Lifecycle Analytics Mixin
/// Add this to your main app widget to track app lifecycle events
mixin AppLifecycleAnalytics<T extends StatefulWidget> on State<T>, WidgetsBindingObserver {
  DateTime? _sessionStartTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _sessionStartTime = DateTime.now();
    AnalyticsIntegrationExamples.trackAppLaunch();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground
        _sessionStartTime = DateTime.now();
        AnalyticsService.instance.track('App Resumed');
        break;
      case AppLifecycleState.paused:
        // App went to background
        if (_sessionStartTime != null) {
          final sessionDuration = DateTime.now().difference(_sessionStartTime!);
          AnalyticsService.instance.track('App Paused', {
            'Session Duration (minutes)': sessionDuration.inMinutes,
          });
        }
        break;
      case AppLifecycleState.detached:
        // App is being terminated
        AnalyticsIntegrationExamples.trackAppBackground();
        break;
      default:
        break;
    }
  }
}
