import 'package:flutter/foundation.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// Centralized analytics service using Mixpanel
class AnalyticsService {
  static AnalyticsService? _instance;
  static AnalyticsService get instance => _instance ??= AnalyticsService._();

  AnalyticsService._();

  Mixpanel? _mixpanel;
  DateTime? _sessionStartTime;

  /// Initialize Mixpanel with your project token
  Future<void> initialize(String projectToken) async {
    try {
      _mixpanel = await Mixpanel.init(
        projectToken,
        trackAutomaticEvents: true, // Automatically track app opens, etc.
      );

      // Set session start time
      _sessionStartTime = DateTime.now();

      debugPrint('Mixpanel initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Mixpanel: $e');
    }
  }

  /// Set user properties (call after authentication)
  Future<void> identifyUser(
    String userId, {
    bool? isPremium,
    DateTime? signUpDate,
    String? onboardingCompleted,
  }) async {
    if (_mixpanel == null) return;

    try {
      await _mixpanel!.identify(userId);

      // Set user properties
      final properties = <String, dynamic>{
        'User ID': userId,
        'Platform': defaultTargetPlatform.name,
        'App Version': '1.0.0', // You can get this from package_info_plus
      };

      if (isPremium != null) properties['Is Premium'] = isPremium;
      if (signUpDate != null) {
        properties['Sign Up Date'] = signUpDate.toIso8601String();
      }
      if (onboardingCompleted != null) {
        properties['Onboarding Completed'] = onboardingCompleted;
      }

      for (final entry in properties.entries) {
        _mixpanel!.getPeople().set(entry.key, entry.value);
      }
    } catch (e) {
      debugPrint('Failed to identify user: $e');
    }
  }

  /// Track events with properties
  Future<void> track(String eventName,
      [Map<String, dynamic>? properties]) async {
    if (_mixpanel == null) return;

    try {
      final enrichedProperties = <String, dynamic>{
        'Timestamp': DateTime.now().toIso8601String(),
        'Platform': defaultTargetPlatform.name,
        ...?properties,
      };

      _mixpanel!.track(eventName, properties: enrichedProperties);
      debugPrint('Tracked event: $eventName');
    } catch (e) {
      debugPrint('Failed to track event $eventName: $e');
    }
  }

  /// Track session end with duration
  Future<void> trackSessionEnd() async {
    if (_sessionStartTime == null) return;

    final sessionDuration = DateTime.now().difference(_sessionStartTime!);

    await track('Session Ended', {
      'Session Duration (seconds)': sessionDuration.inSeconds,
      'Session Duration (minutes)': sessionDuration.inMinutes,
    });
  }

  /// Update user properties
  Future<void> updateUserProperty(String key, dynamic value) async {
    if (_mixpanel == null) return;

    try {
      _mixpanel!.getPeople().set(key, value);
    } catch (e) {
      debugPrint('Failed to update user property: $e');
    }
  }

  /// Increment user properties (useful for counters)
  Future<void> incrementUserProperty(String key, [int value = 1]) async {
    if (_mixpanel == null) return;

    try {
      _mixpanel!.getPeople().increment(key, value.toDouble());
    } catch (e) {
      debugPrint('Failed to increment user property: $e');
    }
  }

  // === SPECIFIC EVENT TRACKING METHODS ===

  /// Track onboarding completion
  Future<void> trackOnboardingComplete(
      Map<String, dynamic> onboardingData) async {
    await track('Onboarding Completed', {
      'Completion Date': DateTime.now().toIso8601String(),
      ...onboardingData,
    });

    // Update user property
    await updateUserProperty(
        'Onboarding Completed', DateTime.now().toIso8601String());
  }

  /// Track word addition
  Future<void> trackWordAdded(
    VocabCard card, {
    required bool isPremium,
    required bool isFromGlobalRepo,
    required int dailyWordsCount,
  }) async {
    await track('Word Added', {
      'Word': card.word,
      'Word Type': card.type.join(', '),
      'CEFR Level': card.level,
      'Is Premium User': isPremium,
      'From Global Repository': isFromGlobalRepo,
      'Daily Words Count': dailyWordsCount,
      'Has Audio': card.audioUrl != null,
      'Word Frequency': card.frequency,
    });

    // Increment daily counter
    await incrementUserProperty('Total Words Added');
  }

  /// Track quiz attempt
  Future<void> trackQuizAttempted({
    required String quizType,
    required int questionsCount,
    required bool isPremium,
    required int dailyQuizzesCount,
  }) async {
    await track('Quiz Attempted', {
      'Quiz Type': quizType,
      'Questions Count': questionsCount,
      'Is Premium User': isPremium,
      'Daily Quizzes Count': dailyQuizzesCount,
    });
  }

  /// Track quiz completion
  Future<void> trackQuizCompleted({
    required String quizType,
    required int score,
    required int totalQuestions,
    required int timeSpent,
    required bool isPremium,
  }) async {
    final accuracy =
        totalQuestions > 0 ? (score / totalQuestions * 100).round() : 0;

    await track('Quiz Completed', {
      'Quiz Type': quizType,
      'Score': score,
      'Total Questions': totalQuestions,
      'Accuracy Percentage': accuracy,
      'Time Spent (seconds)': timeSpent,
      'Is Premium User': isPremium,
    });

    // Increment counters
    await incrementUserProperty('Total Quizzes Completed');
  }

  /// Track word mastery change
  Future<void> trackWordMasteryChanged({
    required String word,
    required String fromLevel,
    required String toLevel,
    required int totalMasteredWords,
  }) async {
    await track('Word Mastery Changed', {
      'Word': word,
      'From Level': fromLevel,
      'To Level': toLevel,
      'Total Mastered Words': totalMasteredWords,
    });
  }

  /// Track premium conversion events
  Future<void> trackPaywallShown(String trigger) async {
    await track('Paywall Shown', {
      'Trigger': trigger,
    });
  }

  Future<void> trackSubscriptionPurchased(
      String productId, double price) async {
    await track('Subscription Purchased', {
      'Product ID': productId,
      'Price': price,
      'Currency': 'USD', // Adjust as needed
    });

    await updateUserProperty('Is Premium', true);
    await updateUserProperty(
        'Premium Purchase Date', DateTime.now().toIso8601String());
  }

  /// Track ad events
  Future<void> trackAdShown(String adType, String placement) async {
    await track('Ad Shown', {
      'Ad Type': adType,
      'Placement': placement,
    });
  }

  Future<void> trackAdClicked(String adType, String placement) async {
    await track('Ad Clicked', {
      'Ad Type': adType,
      'Placement': placement,
    });
  }

  /// Track feature usage
  Future<void> trackFeatureUsed(String featureName,
      [Map<String, dynamic>? properties]) async {
    await track('Feature Used', {
      'Feature Name': featureName,
      ...?properties,
    });
  }

  /// Track pronunciation audio usage
  Future<void> trackPronunciationPlayed(String word, String source) async {
    await track('Pronunciation Played', {
      'Word': word,
      'Audio Source': source, // 'stored' or 'tts'
    });
  }

  /// Track weekly goal completion
  Future<void> trackWeeklyGoalCompleted(
      String goalType, int targetCount) async {
    await track('Weekly Goal Completed', {
      'Goal Type': goalType,
      'Target Count': targetCount,
      'Completion Date': DateTime.now().toIso8601String(),
    });
  }
}
