import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:vocadex/src/features/user/models/user_model.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// Enhanced Firebase service with improved allocation persistence
class FirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Gets the current user's ID
  String get userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception("User not authenticated");
    return user.uid;
  }

  /// Generate a unique ID for a document in a collection
  static String getUID(String collectionName) =>
      FirebaseFirestore.instance.collection(collectionName).doc().id;

  // The isUserPremium() method has been removed.
  // Use ref.watch(subscriptionStateProvider) directly in your code instead.

  Stream<void> watchVocabularyChanges() {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('vocabulary')
        .snapshots()
        .map((_) => null); // We only care about the event, not the data
  }

  /// Retrieves the current user's data from Firestore
  /// [forceRefresh] - If true, forces a fetch from server instead of using cache
  Future<UserModel?> getUserData({bool forceRefresh = false}) async {
    UserModel? user;
    try {
      final String userId = _auth.currentUser!.uid;

      // Determine source based on forceRefresh parameter
      final source = forceRefresh ? Source.server : Source.serverAndCache;
      final DocumentSnapshot snap = await _firestore
          .collection('users')
          .doc(userId)
          .get(GetOptions(source: source));

      // Check if document exists and has data
      if (snap.exists && snap.data() != null) {
        user = UserModel.fromFirestore(snap);
        if (forceRefresh) {
          debugPrint(
              'Retrieved user data for ${user.firstName} (${user.auth.uid})');
        }
      } else {
        debugPrint('User document does not exist or has no data');
        return null;
      }
    } catch (e) {
      debugPrint('Error on getting user data: $e');
    }
    return user;
  }

  /// Saves a user's complete data to Firestore
  Future<void> saveUserData(UserModel user) async {
    try {
      final data = UserModel.toFirestore(user);

      // Save user data
      await _firestore.collection('users').doc(user.auth.uid).set(data);

      // Initialize allocation and weekly goals in a separate operation to avoid type conflicts
      await _initializeAllocationForNewUser(user.auth.uid);
    } catch (e) {
      debugPrint('Error on saving user data: $e');
    }
  }

  /// Initialize allocation for a new user
  Future<void> _initializeAllocationForNewUser(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      // Check if allocation data already exists
      if (userData == null || userData['allocation_used_today'] == null) {
        // Initialize allocation data
        await _firestore.collection('users').doc(userId).update({
          'allocation_used_today': 0,
          'last_allocation_reset': FieldValue.serverTimestamp(),
        });

        debugPrint('Initialized allocation data for new user');
      }

      // Initialize weekly goals data if needed
      if (userData == null || userData['weekly_goals'] == null) {
        // Set initial weekly goals
        await _firestore.collection('users').doc(userId).update({
          'weekly_goals_last_reset': FieldValue.serverTimestamp(),
          'weekly_goals': {
            'verb': 0,
            'noun': 0,
            'adjective': 0,
            'adverb': 0,
            'preposition': 0,
            'pronoun': 0,
            'conjunction': 0,
            'interjection': 0,
          },
        });

        debugPrint('Initialized weekly goals for new user');
      }

      // Initialize diamonds for new user if not present
      if (userData == null || userData['diamonds'] == null) {
        await _firestore.collection('users').doc(userId).set({
          'diamonds': 25,
        }, SetOptions(merge: true));
        debugPrint('Initialized diamonds for new user');
      }
    } catch (e) {
      debugPrint('Error initializing user data: $e');
      // If the update fails (likely because the document doesn't exist yet),
      // try using set with merge instead
      try {
        await _firestore.collection('users').doc(userId).set({
          'allocation_used_today': 0,
          'last_allocation_reset': FieldValue.serverTimestamp(),
          'weekly_goals_last_reset': FieldValue.serverTimestamp(),
          'weekly_goals': {
            'verb': 0,
            'noun': 0,
            'adjective': 0,
            'adverb': 0,
            'preposition': 0,
            'pronoun': 0,
            'conjunction': 0,
            'interjection': 0,
          },
          'diamonds': 25,
        }, SetOptions(merge: true));
        debugPrint('Initialized user data using SetOptions(merge)');
      } catch (mergeError) {
        debugPrint('Error initializing data with merge: $mergeError');
      }
    }
  }

  /// Updates specific profile information for a user
  Future<void> updateUserProfile(UserModel user) async {
    try {
      await _firestore
          .collection('users')
          .doc(user.auth.uid)
          .update({'name': user.firstName, 'image_url': user.imageUrl});
    } catch (e) {
      debugPrint('Error on updating user profile: $e');
    }
  }

  /// Updates a specific field in the user document
  Future<void> updateUserField(String field, dynamic value) async {
    try {
      await _firestore.collection('users').doc(userId).update({field: value});

      // If updating firstName, also update the 'name' field for backward compatibility
      if (field == 'firstName') {
        await _firestore
            .collection('users')
            .doc(userId)
            .update({'name': value});
      }

      debugPrint('Updated user field $field to: $value');
    } catch (e) {
      debugPrint('Error updating user field $field: $e');
    }
  }

  /// Set the user as a premium subscriber
  Future<void> setUserPremium(bool isPremium,
      {Map<String, dynamic>? subscriptionDetails}) async {
    try {
      Map<String, dynamic> updateData = {
        'is_premium': isPremium,
        'premium_updated_at': FieldValue.serverTimestamp(),
      };

      // Include subscription details if provided
      if (subscriptionDetails != null) {
        // Add expiration date and other details if available
        if (subscriptionDetails['expirationDate'] != null) {
          updateData['premium_expiration'] = Timestamp.fromDate(
              subscriptionDetails['expirationDate'] as DateTime);
        }

        if (subscriptionDetails['originalPurchaseDate'] != null) {
          updateData['premium_original_purchase_date'] = Timestamp.fromDate(
              subscriptionDetails['originalPurchaseDate'] as DateTime);
        }

        // Store other subscription metadata
        updateData['premium_metadata'] = subscriptionDetails;
      }

      // If user is no longer premium, clear some fields
      if (!isPremium) {
        updateData['premium_expiration'] = null;
      }

      await _firestore.collection('users').doc(userId).update(updateData);
      debugPrint('Premium status updated in Firestore: $isPremium');
    } catch (e) {
      debugPrint('Error setting premium status in Firestore: $e');
    }
  }

  /// Completely removes a user's data from the Firestore database
  Future<void> deleteUserDatafromDatabase(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).delete();
    } catch (e) {
      debugPrint('Error on deleting user data: $e');
    }
  }

  /// Checks whether a user exists in the Firestore database
  /// If createIfNotExists is true, it will create a basic user document if it doesn't exist
  Future<bool> isUserExists(String userId,
      {bool createIfNotExists = false}) async {
    if (userId.isEmpty) {
      debugPrint('Cannot check user existence: userId is empty');
      return false;
    }

    try {
      // Use GetOptions to always fetch from server and not from cache
      DocumentSnapshot snap = await _firestore
          .collection('users')
          .doc(userId)
          .get(GetOptions(source: Source.server));

      if (snap.exists) {
        debugPrint('User document exists for userId: $userId');
        return true;
      } else {
        debugPrint('User document does not exist for userId: $userId');

        // Create a basic user document if requested
        if (createIfNotExists) {
          try {
            // Get user info from Firebase Auth
            final authUser = _auth.currentUser;
            if (authUser != null) {
              // Create a basic user document
              final userData = {
                'auth': {
                  'uid': authUser.uid,
                  'email': authUser.email ?? '',
                  'displayName': authUser.displayName,
                  'photoUrl': authUser.photoURL,
                },
                'firstName': authUser.displayName?.split(' ').first ?? '',
                'lastName': authUser.displayName?.split(' ').last ?? '',
                'created_at': FieldValue.serverTimestamp(),
                'updated_at': FieldValue.serverTimestamp(),
                'lastUpdated': FieldValue.serverTimestamp(),
                'diamonds': 25, // Default starting diamonds
              };

              await _firestore.collection('users').doc(userId).set(userData);
              debugPrint('Created new user document for userId: $userId');

              // Initialize allocation and weekly goals
              await _initializeAllocationForNewUser(userId);

              return true;
            }
          } catch (createError) {
            debugPrint('Error creating user document: $createError');
          }
        }

        return false;
      }
    } catch (e) {
      debugPrint('Error checking user existence: $e');
      return false;
    }
  }

  /// Adds a new vocabulary card to the user's `vocabulary` subcollection
  Future<void> addVocabCard(VocabCard card) async {
    final vocabCollection =
        _firestore.collection('users').doc(userId).collection('vocabulary');

    await vocabCollection.add(card.toFirestore());

    // Update user stats
    await _updateUserVocabStats();
  }

  /// Fetches all vocabulary cards for the current user
  Future<List<VocabCard>> fetchVocabulary() async {
    final vocabCollection =
        _firestore.collection('users').doc(userId).collection('vocabulary');

    final snapshot = await vocabCollection.get();
    return snapshot.docs.map((doc) {
      return VocabCard.fromFirestore(doc);
    }).toList();
  }

  /// Stream of vocabulary cards for real-time updates
  Stream<List<VocabCard>> watchVocabulary() {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('vocabulary')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) => VocabCard.fromFirestore(doc)).toList();
    });
  }

  /// Updates a specific vocabulary card in the user's collection
  Future<void> updateVocabCard(
      String cardId, Map<String, dynamic> updates) async {
    final cardDoc = _firestore
        .collection('users')
        .doc(userId)
        .collection('vocabulary')
        .doc(cardId);

    await cardDoc.update(updates);

    // Update user stats
    await _updateUserVocabStats();
  }

  /// Deletes a specific vocabulary card from the user's collection
  Future<void> deleteVocabCard(String cardId) async {
    final cardDoc = _firestore
        .collection('users')
        .doc(userId)
        .collection('vocabulary')
        .doc(cardId);

    await cardDoc.delete();

    // Update user stats
    await _updateUserVocabStats();
  }

  /// Get cards of a specific mastery status
  Future<List<VocabCard>> getCardsByMasteryStatus(MasteryStatus status) async {
    final List<VocabCard> allCards = await fetchVocabulary();

    return allCards.where((card) {
      final cardStatus = card.getMasteryStatus();
      return cardStatus == status;
    }).toList();
  }

  /// Get cards by word type
  Future<Map<String, List<VocabCard>>> getCardsByWordType() async {
    final List<VocabCard> allCards = await fetchVocabulary();
    Map<String, List<VocabCard>> cardsByType = {};

    for (final card in allCards) {
      for (final type in card.type) {
        if (!cardsByType.containsKey(type)) {
          cardsByType[type] = [];
        }
        cardsByType[type]!.add(card);
      }
    }

    return cardsByType;
  }

  /// Update user's vocabulary statistics
  Future<void> _updateUserVocabStats() async {
    try {
      final List<VocabCard> allCards = await fetchVocabulary();

      // Calculate counts by mastery status
      int wildCount = 0;
      int tamedCount = 0;
      int masteredCount = 0;

      // Count by word type
      Map<String, int> typeCount = {};

      for (final card in allCards) {
        // Count by mastery status
        final status = card.getMasteryStatus();
        switch (status) {
          case MasteryStatus.wild:
            wildCount++;
            break;
          case MasteryStatus.tamed:
            tamedCount++;
            break;
          case MasteryStatus.mastered:
            masteredCount++;
            break;
        }

        // Count by word type
        for (final type in card.type) {
          typeCount[type] = (typeCount[type] ?? 0) + 1;
        }
      }

      // Update user document with stats
      await _firestore.collection('users').doc(userId).update({
        'vocab_stats': {
          'total_cards': allCards.length,
          'wild_cards': wildCount,
          'tamed_cards': tamedCount,
          'mastered_cards': masteredCount,
          'word_types': typeCount,
          'last_updated': FieldValue.serverTimestamp(),
        }
      });
    } catch (e) {
      debugPrint('Error updating vocab stats: $e');
    }
  }

  /// Save onboarding data
  Future<void> saveOnboardingAnswers(Map<String, dynamic> answers) async {
    try {
      await _firestore.collection('users').doc(userId).set({
        'onboarding_answers': answers,
      }, SetOptions(merge: true));
    } catch (e) {
      debugPrint('Error saving answers: $e');
    }
  }

  // Global Vocabulary Repository Methods

  /// Get reference to global vocabulary collection
  CollectionReference get globalVocabularyCollection =>
      _firestore.collection('global_vocabulary');

  /// Search for a word in the global vocabulary repository
  Future<VocabCard?> searchGlobalVocabulary(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      final doc = await globalVocabularyCollection.doc(normalizedWord).get();

      if (doc.exists) {
        return VocabCard.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error searching global vocabulary: $e');
      return null;
    }
  }

  /// Save a vocabulary card to the global repository
  Future<bool> saveToGlobalVocabulary(VocabCard card) async {
    try {
      final globalCard = card.toGlobalCard();
      final docId = VocabCard.createGlobalId(card.word);

      // Check if word already exists
      final existingDoc = await globalVocabularyCollection.doc(docId).get();

      if (existingDoc.exists) {
        // Update usage count
        await globalVocabularyCollection.doc(docId).update({
          'usageCount': FieldValue.increment(1),
          'lastUsed': FieldValue.serverTimestamp(),
        });
        return true;
      }

      // Create new global vocabulary entry
      final globalData = globalCard.toFirestore();
      globalData.addAll({
        'createdAt': FieldValue.serverTimestamp(),
        'usageCount': 1,
        'lastUsed': FieldValue.serverTimestamp(),
      });

      await globalVocabularyCollection.doc(docId).set(globalData);
      return true;
    } catch (e) {
      debugPrint('Error saving to global vocabulary: $e');
      return false;
    }
  }

  /// Check if a word exists in the global repository
  Future<bool> wordExistsInGlobal(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      final doc = await globalVocabularyCollection.doc(normalizedWord).get();
      return doc.exists;
    } catch (e) {
      debugPrint('Error checking word existence in global: $e');
      return false;
    }
  }

  /// Get popular words from global repository
  Future<List<VocabCard>> getPopularGlobalWords({int limit = 50}) async {
    try {
      final query = await globalVocabularyCollection
          .orderBy('usageCount', descending: true)
          .limit(limit)
          .get();

      return query.docs.map((doc) => VocabCard.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting popular global words: $e');
      return [];
    }
  }

  /// Get the user's daily card allocation details
  Future<Map<String, dynamic>> getUserAllocationDetails() async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      if (userData == null) {
        // Initialize for new user
        await _initializeAllocationForNewUser(userId);

        return {
          'is_premium': false,
          'allocation_used_today': 0,
          'allocation_limit': 5,
          'last_allocation_reset': DateTime.now(),
        };
      }

      // Safety check for weekly_goals field
      if (userData['weekly_goals'] != null &&
          userData['weekly_goals'] is! Map) {
        // If weekly_goals is not a Map, fix it
        await _firestore.collection('users').doc(userId).update({
          'weekly_goals': {
            'verb': 0,
            'noun': 0,
            'adjective': 0,
            'adverb': 0,
            'preposition': 0,
            'pronoun': 0,
            'conjunction': 0,
            'interjection': 0,
          }
        });

        debugPrint('Fixed invalid weekly_goals format');
      }

      // Check if we need to reset daily allocation
      final lastReset = userData['last_allocation_reset'] != null
          ? (userData['last_allocation_reset'] as Timestamp).toDate()
          : DateTime.now()
              .subtract(const Duration(days: 1)); // Default to yesterday

      final now = DateTime.now();

      // If it's a new day, reset the allocation
      if (lastReset.day != now.day ||
          lastReset.month != now.month ||
          lastReset.year != now.year) {
        await _firestore.collection('users').doc(userId).update({
          'allocation_used_today': 0,
          'last_allocation_reset': FieldValue.serverTimestamp(),
        });

        debugPrint('Daily allocation reset');

        return {
          'is_premium': userData['is_premium'] ?? false,
          'allocation_used_today': 0,
          'allocation_limit': userData['is_premium'] ?? false ? -1 : 5,
          'last_allocation_reset': now,
        };
      }

      // Return current allocation details
      return {
        'is_premium': userData['is_premium'] ?? false,
        'allocation_used_today': userData['allocation_used_today'] ?? 0,
        'allocation_limit':
            userData['is_premium'] ?? false ? -1 : 5, // -1 means unlimited
        'last_allocation_reset': lastReset,
      };
    } catch (e) {
      debugPrint('Error getting allocation details: $e');

      // Return default values
      return {
        'is_premium': false,
        'allocation_used_today': 0,
        'allocation_limit': 5,
        'last_allocation_reset': DateTime.now(),
      };
    }
  }

  /// Update the user's daily card allocation
  Future<void> updateCardAllocation(int used) async {
    try {
      // Check the current allocation details
      final details = await getUserAllocationDetails();
      final isPremium = details['is_premium'] as bool;

      // Premium users don't need allocation updates
      if (isPremium) return;

      // Update allocation used today
      await _firestore.collection('users').doc(userId).update({
        'allocation_used_today': FieldValue.increment(used),
      });

      debugPrint('Updated card allocation: +$used');
    } catch (e) {
      debugPrint('Error updating allocation: $e');
    }
  }

  /// Check if user can add more cards today
  Future<bool> canAddMoreCards() async {
    try {
      final details = await getUserAllocationDetails();

      // Premium users have unlimited cards
      if (details['is_premium'] == true) return true;

      // Check if user has remaining cards
      final used = details['allocation_used_today'] as int;
      final limit = details['allocation_limit'] as int;

      return used < limit;
    } catch (e) {
      debugPrint('Error checking card allocation: $e');
      return false;
    }
  }

  /// Daily Word Operations ///

  /// Fetches the daily word for the current day from Firestore.
  Future<Map<String, dynamic>> fetchDailyWord() async {
    final dailyWordDoc =
        await _firestore.collection('daily_words').doc('current').get();
    if (!dailyWordDoc.exists) {
      throw Exception("Daily word not available");
    }
    return dailyWordDoc.data()!;
  }

  /// Updates the daily word in Firestore (admin functionality).
  Future<void> updateDailyWord({
    required String word,
    required String definition,
    required String example,
    required String pronunciation,
  }) async {
    final dailyWordRef = _firestore.collection('daily_words').doc('current');
    await dailyWordRef.set({
      'word': word,
      'definition': definition,
      'example': example,
      'pronunciation': pronunciation,
      'date': FieldValue.serverTimestamp(),
    });
  }

  /// Get the current user's diamonds count
  Future<int> getDiamonds() async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data();
    if (userData == null) return 25; // Default for new users
    return (userData['diamonds'] as int?) ??
        25; // Get actual diamond count with default of 25
  }

  /// Decrement diamonds for free users (returns true if successful, false if not enough diamonds)
  /// Note: This method should be called with a WidgetRef to check premium status
  /// Example: firebaseService.decrementDiamondIfFreeUser(ref.watch(subscriptionStateProvider));
  Future<bool> decrementDiamondIfFreeUser([bool? isPremium]) async {
    // If isPremium is not provided, we'll try to get it from Purchases directly
    // This is not recommended - always pass the premium status from subscriptionStateProvider
    if (isPremium == null) {
      try {
        final customerInfo = await Purchases.getCustomerInfo();
        isPremium = customerInfo.entitlements.active.containsKey('premium');
      } catch (e) {
        debugPrint('Error checking premium status: $e');
        isPremium = false;
      }
    }

    if (isPremium) return true; // Premium users are not limited
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data();
    int diamonds = (userData?['diamonds'] as int?) ??
        25; // Use default of 25 for consistency
    if (diamonds > 0) {
      await _firestore.collection('users').doc(userId).update({
        'diamonds': diamonds - 1,
      });
      return true;
    } else {
      return false;
    }
  }

  /// Decrement diamonds by a specified amount (returns true if successful, false if not enough diamonds)
  Future<bool> decrementDiamondsBy(int amount) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data();
    int diamonds = (userData?['diamonds'] as int?) ??
        25; // Use default of 25 for consistency
    if (diamonds >= amount) {
      await _firestore.collection('users').doc(userId).update({
        'diamonds': diamonds - amount,
      });
      return true;
    } else {
      return false;
    }
  }

  /// Reset diamonds to 25 for free users if a new day has started
  /// Note: This method should be called with a WidgetRef to check premium status
  /// Example: firebaseService.resetDiamondsIfNeeded(ref.watch(subscriptionStateProvider));
  Future<void> resetDiamondsIfNeeded([bool? isPremium]) async {
    // If isPremium is not provided, we'll try to get it from Purchases directly
    // This is not recommended - always pass the premium status from subscriptionStateProvider
    if (isPremium == null) {
      try {
        final customerInfo = await Purchases.getCustomerInfo();
        isPremium = customerInfo.entitlements.active.containsKey('premium');
      } catch (e) {
        debugPrint('Error checking premium status: $e');
        isPremium = false;
      }
    }

    if (isPremium) return; // Premium users are not limited
    final userDoc = await _firestore.collection('users').doc(userId).get();
    final userData = userDoc.data();
    if (userData == null) return;
    final lastReset = userData['diamonds_last_reset'] != null
        ? (userData['diamonds_last_reset'] as Timestamp).toDate()
        : null;
    final now = DateTime.now();
    bool shouldReset = false;
    if (lastReset == null) {
      shouldReset = true;
    } else if (lastReset.day != now.day ||
        lastReset.month != now.month ||
        lastReset.year != now.year) {
      shouldReset = true;
    }
    if (shouldReset) {
      await _firestore.collection('users').doc(userId).update({
        'diamonds': 25, // Reset to 25 diamonds instead of 3
        'diamonds_last_reset': FieldValue.serverTimestamp(),
      });
      debugPrint('💎 Diamonds reset to 25 for the day');
    }
  }
}
