import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/leaderboard/leaderboard_model.dart';

/// Service for managing leaderboard data
class LeaderboardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Gets the current user's ID
  String get userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception("User not authenticated");
    return user.uid;
  }

  /// Get the user's display name from the users collection
  Future<String> _getUserDisplayName() async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) return 'Anonymous';

      final data = doc.data();
      if (data == null) return 'Anonymous';

      // Try to get the user's name in different formats
      if (data['firstName'] != null && data['lastName'] != null) {
        return '${data['firstName']} ${data['lastName']}';
      } else if (data['name'] != null) {
        return data['name'];
      }

      return 'Anonymous';
    } catch (e) {
      debugPrint('Error getting user display name: $e');
      return 'Anonymous';
    }
  }

  /// Get the user's profile image URL
  Future<String?> _getUserImageUrl() async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) return null;

      final data = doc.data();
      if (data == null) return null;

      return data['image_url'] ?? data['imageUrl'];
    } catch (e) {
      debugPrint('Error getting user image: $e');
      return null;
    }
  }

  /// Update the user's score on all leaderboards
  Future<void> updateLeaderboardScore(int pointsEarned) async {
    try {
      final displayName = await _getUserDisplayName();
      final imageUrl = await _getUserImageUrl();
      final now = DateTime.now();

      // Update all leaderboard periods
      await _updateLeaderboardEntry(
        LeaderboardPeriod.daily,
        displayName,
        imageUrl,
        pointsEarned,
      );

      await _updateLeaderboardEntry(
        LeaderboardPeriod.weekly,
        displayName,
        imageUrl,
        pointsEarned,
      );

      await _updateLeaderboardEntry(
        LeaderboardPeriod.monthly,
        displayName,
        imageUrl,
        pointsEarned,
      );

      await _updateLeaderboardEntry(
        LeaderboardPeriod.allTime,
        displayName,
        imageUrl,
        pointsEarned,
      );
    } catch (e) {
      debugPrint('Error updating leaderboard: $e');
    }
  }

  /// Update a specific leaderboard entry
  Future<void> _updateLeaderboardEntry(
    LeaderboardPeriod period,
    String displayName,
    String? imageUrl,
    int pointsEarned,
  ) async {
    final collection = _firestore.collection(period.collectionName);
    final docRef = collection.doc(userId);

    // Use a transaction to safely update the entry
    await _firestore.runTransaction((transaction) async {
      final doc = await transaction.get(docRef);

      if (doc.exists) {
        // Update existing entry
        final data = doc.data() as Map<String, dynamic>;
        final totalPoints = (data['totalPoints'] ?? 0) + pointsEarned;
        final quizzesTaken = (data['quizzesTaken'] ?? 0) + 1;

        transaction.update(docRef, {
          'displayName': displayName, // Update name in case it changed
          'imageUrl': imageUrl,
          'totalPoints': totalPoints,
          'quizzesTaken': quizzesTaken,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      } else {
        // Create new entry
        transaction.set(docRef, {
          'displayName': displayName,
          'imageUrl': imageUrl,
          'totalPoints': pointsEarned,
          'quizzesTaken': 1,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    });
  }

  /// Get the leaderboard for a specific period
  Future<List<LeaderboardEntry>> getLeaderboard(
      LeaderboardPeriod period) async {
    try {
      final collection = _firestore.collection(period.collectionName);
      final snapshot = await collection
          .orderBy('totalPoints', descending: true)
          .limit(100) // Limit to top 100 users
          .get();

      final entries = <LeaderboardEntry>[];
      int rank = 1;

      for (final doc in snapshot.docs) {
        entries.add(LeaderboardEntry.fromFirestore(doc, rank));
        rank++;
      }

      return entries;
    } catch (e) {
      debugPrint('Error getting leaderboard: $e');
      return [];
    }
  }

  /// Get the current user's position on the leaderboard
  Future<LeaderboardEntry?> getCurrentUserRanking(
      LeaderboardPeriod period) async {
    try {
      // First check if the user is in the top 100
      final leaderboard = await getLeaderboard(period);
      final currentUserEntry =
          leaderboard.where((entry) => entry.userId == userId).firstOrNull;

      if (currentUserEntry != null) {
        return currentUserEntry;
      }

      // If not in top 100, get their specific entry
      final docRef = _firestore.collection(period.collectionName).doc(userId);
      final doc = await docRef.get();

      if (!doc.exists) {
        return null; // User is not on the leaderboard yet
      }

      // Count how many users have more points than the current user
      final data = doc.data();
      if (data == null) return null;

      final currentUserPoints = data['totalPoints'] ?? 0;
      final countSnapshot = await _firestore
          .collection(period.collectionName)
          .where('totalPoints', isGreaterThan: currentUserPoints)
          .count()
          .get();

      // Ensure count is not null before adding 1
      final rank = (countSnapshot.count ?? 0) + 1;

      return LeaderboardEntry.fromFirestore(doc, rank);
    } catch (e) {
      debugPrint('Error getting user ranking: $e');
      return null;
    }
  }

  /// Reset daily leaderboard if needed (should be called from a server function)
  Future<void> resetDailyLeaderboardIfNeeded() async {
    // This would ideally be a server-side operation
    // For now, we'll just check the last reset timestamp
    try {
      final configDoc =
          await _firestore.collection('config').doc('leaderboard').get();
      final data = configDoc.data();

      if (data != null && data['last_daily_reset'] != null) {
        final lastReset = (data['last_daily_reset'] as Timestamp).toDate();
        final now = DateTime.now();

        // Check if it's a new day
        if (lastReset.day != now.day ||
            lastReset.month != now.month ||
            lastReset.year != now.year) {
          // It's a new day, reset the daily leaderboard
          await _clearLeaderboard(LeaderboardPeriod.daily);

          // Update the last reset timestamp
          await _firestore.collection('config').doc('leaderboard').set({
            'last_daily_reset': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));
        }
      } else {
        // No reset timestamp found, create one
        await _firestore.collection('config').doc('leaderboard').set({
          'last_daily_reset': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      debugPrint('Error checking daily leaderboard reset: $e');
    }
  }

  /// Reset weekly leaderboard if needed (should be called from a server function)
  Future<void> resetWeeklyLeaderboardIfNeeded() async {
    try {
      final configDoc =
          await _firestore.collection('config').doc('leaderboard').get();
      final data = configDoc.data();

      if (data != null && data['last_weekly_reset'] != null) {
        final lastReset = (data['last_weekly_reset'] as Timestamp).toDate();
        final now = DateTime.now();

        // Check if it's a new week (Monday is the start of the week)
        final lastResetWeekday = lastReset.weekday;
        final nowWeekday = now.weekday;

        if ((nowWeekday < lastResetWeekday) ||
            (nowWeekday == 1 && lastResetWeekday != 1) ||
            (now.difference(lastReset).inDays >= 7)) {
          // It's a new week, reset the weekly leaderboard
          await _clearLeaderboard(LeaderboardPeriod.weekly);

          // Update the last reset timestamp
          await _firestore.collection('config').doc('leaderboard').set({
            'last_weekly_reset': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));
        }
      } else {
        // No reset timestamp found, create one
        await _firestore.collection('config').doc('leaderboard').set({
          'last_weekly_reset': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      debugPrint('Error checking weekly leaderboard reset: $e');
    }
  }

  /// Reset monthly leaderboard if needed (should be called from a server function)
  Future<void> resetMonthlyLeaderboardIfNeeded() async {
    try {
      final configDoc =
          await _firestore.collection('config').doc('leaderboard').get();
      final data = configDoc.data();

      if (data != null && data['last_monthly_reset'] != null) {
        final lastReset = (data['last_monthly_reset'] as Timestamp).toDate();
        final now = DateTime.now();

        // Check if it's a new month
        if (lastReset.month != now.month || lastReset.year != now.year) {
          // It's a new month, reset the monthly leaderboard
          await _clearLeaderboard(LeaderboardPeriod.monthly);

          // Update the last reset timestamp
          await _firestore.collection('config').doc('leaderboard').set({
            'last_monthly_reset': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));
        }
      } else {
        // No reset timestamp found, create one
        await _firestore.collection('config').doc('leaderboard').set({
          'last_monthly_reset': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      debugPrint('Error checking monthly leaderboard reset: $e');
    }
  }

  /// Clear a specific leaderboard collection
  Future<void> _clearLeaderboard(LeaderboardPeriod period) async {
    try {
      final batch = _firestore.batch();
      final snapshot =
          await _firestore.collection(period.collectionName).limit(500).get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      // If there are more documents, recursively delete them
      if (snapshot.docs.length == 500) {
        await _clearLeaderboard(period);
      }
    } catch (e) {
      debugPrint('Error clearing leaderboard: $e');
    }
  }
}
