import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/services/leaderboard_service.dart';

/// Service for managing quiz data and statistics
class QuizService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final LeaderboardService _leaderboardService = LeaderboardService();

  /// Gets the current user's ID
  String get _userId {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');
    return user.uid;
  }

  /// Save quiz results to Firestore
  Future<void> saveQuizResult({
    required String quizId,
    required String title,
    required int score,
    required int totalQuestions,
    required bool isCompleted,
    required Map<String, dynamic> questionStats,
    required String quizType,
    List<Map<String, dynamic>> incorrectAnswers = const [],
    bool isChallenge = false,
    String? challengeType,
  }) async {
    try {
      await _firestore
          .collection('users')
          .doc(_userId)
          .collection('quiz_history')
          .doc(quizId)
          .set({
        'title': title,
        'score': score,
        'totalQuestions': totalQuestions,
        'isCompleted': isCompleted,
        'completedAt': FieldValue.serverTimestamp(),
        'questionStats': questionStats,
        'quizType': quizType,
        'incorrectAnswers': incorrectAnswers,
        'isChallenge': isChallenge,
        'challengeType': challengeType,
      });

      // Update the leaderboard with the points earned from this quiz
      if (isCompleted) {
        // Award double points for challenge quizzes
        int pointsMultiplier = isChallenge ? 2 : 1;
        await _leaderboardService
            .updateLeaderboardScore(score * pointsMultiplier);
      }
    } catch (e) {
      debugPrint('Error saving quiz result: $e');
    }
  }

  /// Get quiz statistics
  Future<Map<String, dynamic>> getQuizStatistics() async {
    try {
      // Get quiz history from Firestore
      final quizHistorySnapshot = await _firestore
          .collection('users')
          .doc(_userId)
          .collection('quiz_history')
          .orderBy('completedAt', descending: true)
          .get();

      if (quizHistorySnapshot.docs.isEmpty) {
        return _getDefaultQuizStats();
      }

      // Process quiz stats
      int totalQuizzes = quizHistorySnapshot.docs.length;
      int totalScore = 0;
      int totalPoints = 0;
      int bestScore = 0;
      int completedQuizzes = 0;
      int totalCorrectAnswers = 0;
      int totalIncorrectAnswers = 0;

      // Counts for question types
      int totalFillInBlank = 0;
      int correctFillInBlank = 0;
      int totalMatchDefinition = 0;
      int correctMatchDefinition = 0;
      int totalTrueFalse = 0;
      int correctTrueFalse = 0;

      // Recent quiz history for chart
      final List<Map<String, dynamic>> quizHistory = [];

      // Recent quiz data for list view
      final List<Map<String, dynamic>> recentQuizzes = [];

      for (var doc in quizHistorySnapshot.docs) {
        final quizData = doc.data();

        // Parse the quiz data
        final score = quizData['score'] as int? ?? 0;
        final totalQuestions = quizData['totalQuestions'] as int? ?? 0;
        final isCompleted = quizData['isCompleted'] as bool? ?? false;
        final quizType = quizData['quizType'] as String? ?? 'Mixed';
        final completedAt = quizData['completedAt'] as Timestamp?;

        // Add to recent quizzes list (limit to last 10)
        if (recentQuizzes.length < 10 && completedAt != null) {
          recentQuizzes.add({
            'date': completedAt.toDate(),
            'score': score,
            'total': totalQuestions,
            'type': quizType,
            'id': doc.id,
            'incorrectAnswers': quizData['incorrectAnswers'] ?? [],
          });
        }

        // Add to quiz history for chart (limit to last 7 for weekly view)
        if (quizHistory.length < 7 && completedAt != null) {
          quizHistory.add({
            'date': completedAt.toDate(),
            'score': score,
            'total': totalQuestions,
          });
        }

        if (isCompleted) {
          completedQuizzes++;
        }

        // Update total score and best score
        if (totalQuestions > 0) {
          final scorePercentage = (score / totalQuestions) * 100;
          totalScore += scorePercentage.round();
          totalPoints += score;

          if (scorePercentage > bestScore) {
            bestScore = scorePercentage.round();
          }
        }

        // Process question type stats if available
        final questionStats =
            quizData['questionStats'] as Map<String, dynamic>? ?? {};

        // Calculate total correct and incorrect answers
        int correctAnswers = 0;
        int incorrectAnswers = 0;

        if (questionStats.containsKey('fillInBlank')) {
          final stats =
              questionStats['fillInBlank'] as Map<String, dynamic>? ?? {};
          final total = stats['total'] as int? ?? 0;
          final correct = stats['correct'] as int? ?? 0;
          totalFillInBlank += total;
          correctFillInBlank += correct;
          correctAnswers += correct;
          incorrectAnswers += (total - correct);
        }

        if (questionStats.containsKey('matchDefinition')) {
          final stats =
              questionStats['matchDefinition'] as Map<String, dynamic>? ?? {};
          final total = stats['total'] as int? ?? 0;
          final correct = stats['correct'] as int? ?? 0;
          totalMatchDefinition += total;
          correctMatchDefinition += correct;
          correctAnswers += correct;
          incorrectAnswers += (total - correct);
        }

        if (questionStats.containsKey('trueFalse')) {
          final stats =
              questionStats['trueFalse'] as Map<String, dynamic>? ?? {};
          final total = stats['total'] as int? ?? 0;
          final correct = stats['correct'] as int? ?? 0;
          totalTrueFalse += total;
          correctTrueFalse += correct;
          correctAnswers += correct;
          incorrectAnswers += (total - correct);
        }

        totalCorrectAnswers += correctAnswers;
        totalIncorrectAnswers += incorrectAnswers;
      }

      // Calculate averages
      final avgScore = totalQuizzes > 0 ? totalScore / totalQuizzes : 0;
      final completionRate =
          totalQuizzes > 0 ? (completedQuizzes / totalQuizzes) * 100 : 0;

      // Calculate question type accuracy
      final fillInBlankAccuracy = totalFillInBlank > 0
          ? (correctFillInBlank / totalFillInBlank) * 100
          : 0;
      final matchDefinitionAccuracy = totalMatchDefinition > 0
          ? (correctMatchDefinition / totalMatchDefinition) * 100
          : 0;
      final trueFalseAccuracy =
          totalTrueFalse > 0 ? (correctTrueFalse / totalTrueFalse) * 100 : 0;
      final mixedAccuracy =
          (fillInBlankAccuracy + matchDefinitionAccuracy + trueFalseAccuracy) /
              3;

      // Compile and return stats
      return {
        'totalQuizzes': totalQuizzes,
        'totalPoints': totalPoints,
        'correctAnswers': totalCorrectAnswers,
        'incorrectAnswers': totalIncorrectAnswers,
        'averageAccuracy': avgScore,
        'bestScore': bestScore,
        'completionRate': completionRate,
        'quizHistory': quizHistory,
        'recentQuizzes': recentQuizzes,
        'questionTypeAccuracy': {
          'Fill in Blanks': fillInBlankAccuracy,
          'Match Definition': matchDefinitionAccuracy,
          'True/False': trueFalseAccuracy,
          'Mixed': mixedAccuracy,
        },
      };
    } catch (e) {
      debugPrint('Error fetching quiz statistics: $e');
      return _getDefaultQuizStats();
    }
  }

  /// Get default quiz stats when no data is available
  Map<String, dynamic> _getDefaultQuizStats() {
    return {
      'totalQuizzes': 0,
      'totalPoints': 0,
      'correctAnswers': 0,
      'incorrectAnswers': 0,
      'averageAccuracy': 0,
      'bestScore': 0,
      'completionRate': 0,
      'quizHistory': <Map<String, dynamic>>[],
      'recentQuizzes': <Map<String, dynamic>>[],
      'questionTypeAccuracy': {
        'Fill in Blanks': 0.0,
        'Match Definition': 0.0,
        'True/False': 0.0,
        'Mixed': 0.0,
      },
    };
  }
}
