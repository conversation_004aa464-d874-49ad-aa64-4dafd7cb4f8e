import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';

/// Service for managing the global vocabulary repository
/// Handles search, save, and retrieve operations for shared vocabulary cards
class GlobalVocabularyService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// Collection name for global vocabulary repository
  static const String _globalCollectionName = 'global_vocabulary';
  
  /// Get reference to global vocabulary collection
  CollectionReference get _globalCollection => 
      _firestore.collection(_globalCollectionName);

  /// Search for a vocabulary card in the global repository
  /// Uses case-insensitive exact matching
  /// Returns null if not found
  Future<VocabCard?> searchGlobalVocabulary(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      debugPrint('Searching global repository for: $normalizedWord');
      
      final doc = await _globalCollection.doc(normalizedWord).get();
      
      if (doc.exists) {
        debugPrint('Found word in global repository: $normalizedWord');
        return VocabCard.fromFirestore(doc);
      }
      
      debugPrint('Word not found in global repository: $normalizedWord');
      return null;
    } catch (e) {
      debugPrint('Error searching global vocabulary: $e');
      return null;
    }
  }

  /// Save a vocabulary card to the global repository
  /// Returns true if successful, false otherwise
  Future<bool> saveToGlobalRepository(VocabCard card) async {
    try {
      final globalCard = card.toGlobalCard();
      final docId = VocabCard.createGlobalId(card.word);
      
      debugPrint('Saving to global repository: ${card.word} -> $docId');
      
      // Check if the word already exists
      final existingDoc = await _globalCollection.doc(docId).get();
      
      if (existingDoc.exists) {
        debugPrint('Word already exists in global repository: ${card.word}');
        // Update usage count if the document exists
        await _globalCollection.doc(docId).update({
          'usageCount': FieldValue.increment(1),
          'lastUsed': FieldValue.serverTimestamp(),
        });
        return true;
      }
      
      // Add metadata for global repository
      final globalData = globalCard.toFirestore();
      globalData.addAll({
        'createdAt': FieldValue.serverTimestamp(),
        'usageCount': 1,
        'lastUsed': FieldValue.serverTimestamp(),
      });
      
      await _globalCollection.doc(docId).set(globalData);
      debugPrint('Successfully saved to global repository: ${card.word}');
      return true;
    } catch (e) {
      debugPrint('Error saving to global repository: $e');
      return false;
    }
  }

  /// Save multiple vocabulary cards to the global repository
  /// Used when AI generates multiple meanings for a word
  /// Returns the number of cards successfully saved
  Future<int> saveMultipleToGlobalRepository(List<VocabCard> cards) async {
    int successCount = 0;
    
    for (final card in cards) {
      final success = await saveToGlobalRepository(card);
      if (success) successCount++;
    }
    
    return successCount;
  }

  /// Get vocabulary cards by word with multiple meanings
  /// Returns all meanings/definitions for a given word
  Future<List<VocabCard>> getWordMeanings(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      
      // First try to get the main document
      final mainCard = await searchGlobalVocabulary(word);
      if (mainCard == null) return [];
      
      List<VocabCard> meanings = [mainCard];
      
      // If the card has multiple meanings, extract them
      if (mainCard.hasMultipleMeanings && mainCard.multipleDefinitions != null) {
        for (final meaning in mainCard.multipleDefinitions!) {
          final meaningCard = VocabCard(
            id: '${normalizedWord}_${meanings.length}',
            word: mainCard.word,
            definition: meaning['definition'] ?? '',
            examples: List<String>.from(meaning['examples'] ?? []),
            type: List<String>.from(meaning['type'] ?? []),
            pronunciation: meaning['pronunciation'] ?? mainCard.pronunciation,
            level: meaning['level'] ?? mainCard.level,
            color: meaning['color'] ?? mainCard.color,
            frequency: meaning['frequency'] ?? mainCard.frequency,
          );
          meanings.add(meaningCard);
        }
      }
      
      return meanings;
    } catch (e) {
      debugPrint('Error getting word meanings: $e');
      return [];
    }
  }

  /// Check if a word exists in the global repository
  Future<bool> wordExists(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      final doc = await _globalCollection.doc(normalizedWord).get();
      return doc.exists;
    } catch (e) {
      debugPrint('Error checking word existence: $e');
      return false;
    }
  }

  /// Get usage statistics for a word
  Future<Map<String, dynamic>?> getWordStats(String word) async {
    try {
      final normalizedWord = VocabCard.normalizeWord(word);
      final doc = await _globalCollection.doc(normalizedWord).get();
      
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'usageCount': data['usageCount'] ?? 0,
          'createdAt': data['createdAt'],
          'lastUsed': data['lastUsed'],
        };
      }
      return null;
    } catch (e) {
      debugPrint('Error getting word stats: $e');
      return null;
    }
  }

  /// Batch search for multiple words
  /// Returns a map of word -> VocabCard (null if not found)
  Future<Map<String, VocabCard?>> batchSearchGlobalVocabulary(
      List<String> words) async {
    final results = <String, VocabCard?>{};
    
    // Process in batches to avoid overwhelming Firestore
    const batchSize = 10;
    for (int i = 0; i < words.length; i += batchSize) {
      final batch = words.skip(i).take(batchSize).toList();
      
      for (final word in batch) {
        results[word] = await searchGlobalVocabulary(word);
      }
    }
    
    return results;
  }

  /// Get popular words from global repository
  /// Returns words sorted by usage count
  Future<List<VocabCard>> getPopularWords({int limit = 50}) async {
    try {
      final query = await _globalCollection
          .orderBy('usageCount', descending: true)
          .limit(limit)
          .get();
      
      return query.docs.map((doc) => VocabCard.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting popular words: $e');
      return [];
    }
  }

  /// Get recently added words from global repository
  Future<List<VocabCard>> getRecentWords({int limit = 50}) async {
    try {
      final query = await _globalCollection
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();
      
      return query.docs.map((doc) => VocabCard.fromFirestore(doc)).toList();
    } catch (e) {
      debugPrint('Error getting recent words: $e');
      return [];
    }
  }
}
