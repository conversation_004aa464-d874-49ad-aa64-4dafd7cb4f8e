import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:flutter/foundation.dart';
import 'package:vocadex/src/services/firebase_service.dart';

/// Service for managing temporary storage of user data before authentication
class LocalStorageService {
  static const String _temporaryCardsKey = 'temporary_vocab_cards';
  static const String _temporaryQuizKey = 'temporary_quiz';

  /// Save a vocabulary card to local storage
  Future<void> saveVocabCard(VocabCard card) async {
    final prefs = await SharedPreferences.getInstance();

    // Get existing cards
    List<VocabCard> existingCards = await getTemporaryVocabCards();

    // Check if card with same ID already exists
    bool exists =
        existingCards.any((existingCard) => existingCard.id == card.id);

    if (!exists) {
      existingCards.add(card);
    } else {
      // Replace existing card
      existingCards = existingCards.map((existingCard) {
        if (existingCard.id == card.id) {
          return card;
        }
        return existingCard;
      }).toList();
    }

    // Convert to JSON and save
    List<Map<String, dynamic>> cardsJson =
        existingCards.map((card) => card.toFirestore()).toList();
    await prefs.setString(_temporaryCardsKey, jsonEncode(cardsJson));
  }

  /// Get all temporary vocabulary cards
  Future<List<VocabCard>> getTemporaryVocabCards() async {
    final prefs = await SharedPreferences.getInstance();
    final String? cardsJson = prefs.getString(_temporaryCardsKey);

    if (cardsJson == null || cardsJson.isEmpty) {
      return [];
    }

    try {
      final List<dynamic> decoded = jsonDecode(cardsJson);
      return decoded.map((cardJson) {
        // Create a document snapshot-like map with ID
        final map = cardJson as Map<String, dynamic>;
        final id = map['id'] as String? ?? '';
        return VocabCard(
          id: id,
          word: map['word'] ?? '',
          definition: map['definition'] ?? '',
          examples: _convertToStringList(map['example']),
          type: _convertToStringList(map['type']),
          pronunciation: map['pronunciation'] ?? '',
          level: map['level'] ?? 'A1',
          color: map['color'] ?? 'blue',
          masteryLevel: map['masteryLevel'] ?? 1,
          correctAnswers: map['correctAnswers'] ?? 0,
          totalAnswers: map['totalAnswers'] ?? 0,
          frequency: map['frequency'],
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Save a quiz to local storage
  Future<void> saveTemporaryQuiz(Quiz quiz) async {
    final prefs = await SharedPreferences.getInstance();
    final quizMap = {
      'id': quiz.id,
      'title': quiz.title,
      'type': quiz.type.toString(),
      'createdAt': quiz.createdAt.toIso8601String(),
      'isCompleted': quiz.isCompleted,
      'score': quiz.score,
      'totalQuestions': quiz.totalQuestions,
      'questions': quiz.questions.map((q) => _questionToMap(q)).toList(),
    };
    await prefs.setString(_temporaryQuizKey, jsonEncode(quizMap));
  }

  /// Get the temporary quiz from local storage
  Future<Quiz?> getTemporaryQuiz() async {
    final prefs = await SharedPreferences.getInstance();
    final String? quizJson = prefs.getString(_temporaryQuizKey);

    if (quizJson == null || quizJson.isEmpty) {
      return null;
    }

    try {
      final Map<String, dynamic> quizMap = jsonDecode(quizJson);

      // Parse the quiz type
      QuizType quizType = QuizType.mixed;
      final typeString = quizMap['type'] as String? ?? '';
      if (typeString.contains('fillInTheBlank')) {
        quizType = QuizType.fillInTheBlank;
      } else if (typeString.contains('matchDefinition')) {
        quizType = QuizType.matchDefinition;
      } else if (typeString.contains('trueFalse')) {
        quizType = QuizType.trueFalse;
      } else if (typeString.contains('spellWord')) {
        quizType = QuizType.spellWord;
      }

      // Create basic quiz without questions first
      final quiz = Quiz(
        id: quizMap['id'] as String? ?? '',
        title: quizMap['title'] as String? ?? '',
        type: quizType,
        questions: [], // Will be populated below
        createdAt: DateTime.tryParse(quizMap['createdAt'] as String? ?? '') ??
            DateTime.now(),
        isCompleted: quizMap['isCompleted'] as bool? ?? false,
        score: quizMap['score'] as int? ?? 0,
        totalQuestions: quizMap['totalQuestions'] as int? ?? 0,
      );

      // For simplicity, we're not reconstructing the questions
      // In a real implementation, you would parse the questions as well

      return quiz;
    } catch (e) {
      debugPrint('Error parsing quiz: $e');
      return null;
    }
  }

  /// Helper to convert dynamic to List<String>
  List<String> _convertToStringList(dynamic value) {
    if (value == null) return [];
    if (value is String) return [value];
    if (value is List) {
      return value.map((item) => item.toString()).toList();
    }
    return [];
  }

  /// Helper to convert a question to a map
  Map<String, dynamic> _questionToMap(QuizQuestion question) {
    // This is a simplified version - expand as needed
    final baseMap = {
      'id': question.id,
      'card': question.card.toFirestore(),
      'isAnswered': question.isAnswered,
      'isCorrect': question.isCorrect,
      'type': question.runtimeType.toString(),
    };

    // Add specific properties based on question type
    if (question is FillInBlankQuestion) {
      baseMap['sentence'] = question.sentence;
      baseMap['blankWord'] = question.blankWord;
      baseMap['options'] = question.options;
    } else if (question is MatchDefinitionQuestion) {
      baseMap['definition'] = question.definition;
      baseMap['options'] = question.options;
    } else if (question is TrueFalseQuestion) {
      baseMap['statement'] = question.statement;
      baseMap['answer'] = question.answer;
    }

    return baseMap;
  }

  /// Transfer temporary data to Firebase after authentication
  Future<void> transferDataToFirebase() async {
    try {
      final firebaseService = FirebaseService();
      
      // Get all temporary vocab cards
      final cards = await getTemporaryVocabCards();
      
      // Add each card to the user's vocabulary in Firebase
      for (final card in cards) {
        if (firebaseService.userId.isNotEmpty) {
          await firebaseService.addVocabCard(card);
          debugPrint('Transferred card ${card.word} to Firebase');
        }
      }
      
      // Get any temporary quiz data
      final quiz = await getTemporaryQuiz();
      if (quiz != null && firebaseService.userId.isNotEmpty) {
        // Save quiz data if needed
        // Implementation would depend on how quizzes are stored
        debugPrint('Transferred quiz ${quiz.title} to Firebase');
      }
      
      // Clear temporary storage after successful transfer
      await clearTemporaryStorage();
      debugPrint('Successfully transferred all local data to Firebase');
    } catch (e) {
      debugPrint('Error transferring data to Firebase: $e');
    }
  }

  /// Clear all temporary storage
  Future<void> clearTemporaryStorage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_temporaryCardsKey);
    await prefs.remove(_temporaryQuizKey);
  }
}
