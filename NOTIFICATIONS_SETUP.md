# Vocadex Notifications System Setup Guide

## 🎯 Overview

The Vocadex notifications system provides comprehensive push and local notification capabilities with an admin panel for management. This guide covers the complete setup process.

## 📋 Features Implemented

### ✅ Core Features
- **Push Notifications**: Firebase Cloud Messaging integration
- **Local Notifications**: Scheduled notifications with timezone support
- **Admin Panel**: Exclusive interface for authorized users
- **Trigger Events**: 8 different notification types with configurable schedules
- **Analytics**: Notification delivery statistics and engagement metrics

### ✅ Notification Types
1. **Daily Study Reminder** - Encourages daily practice
2. **Weekly Goal Check** - Progress on vocabulary goals
3. **Streak Reminder** - Maintains learning streaks
4. **Daily Word Notification** - New daily vocabulary words
5. **Low Diamonds Alert** - Diamond count warnings
6. **Mastery Progress** - Vocabulary mastery updates
7. **Inactivity Reminder** - Re-engagement for inactive users
8. **Achievement Unlock** - Celebration notifications

## 🔧 Firebase Backend Setup

### 1. Admin Access Configuration

Create a document in Firestore:
```
Collection: admin_config
Document: settings
Data: {
  "adminEmails": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "notificationsEnabled": true,
  "lastUpdated": [current timestamp]
}
```

### 2. Firestore Security Rules

Add these rules to your `firestore.rules`:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Admin config - only readable by authenticated users
    match /admin_config/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.adminEmails.hasAny([request.auth.token.email]);
    }
    
    // Notifications - admin write, all read
    match /notifications/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/admin_config/settings) &&
        get(/databases/$(database)/documents/admin_config/settings).data.adminEmails.hasAny([request.auth.token.email]);
    }
    
    // Trigger events - admin only
    match /trigger_events/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/admin_config/settings) &&
        get(/databases/$(database)/documents/admin_config/settings).data.adminEmails.hasAny([request.auth.token.email]);
    }
    
    // FCM tokens - users can write their own
    match /fcm_tokens/{document} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 3. Firebase Cloud Functions (Optional)

For production push notifications, create a Cloud Function:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.sendNotificationToAll = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    const notification = snap.data();
    
    if (notification.type !== 'push' || notification.status !== 'pending') {
      return null;
    }

    try {
      // Get all FCM tokens
      const tokensSnapshot = await admin.firestore()
        .collection('fcm_tokens')
        .get();
      
      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);
      
      if (tokens.length === 0) {
        return null;
      }

      // Send notification
      const message = {
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl || undefined,
        },
        data: notification.data || {},
        tokens: tokens,
      };

      const response = await admin.messaging().sendMulticast(message);
      
      // Update notification status
      await snap.ref.update({
        status: 'sent',
        sentAt: admin.firestore.FieldValue.serverTimestamp(),
        deliveryStats: {
          successCount: response.successCount,
          failureCount: response.failureCount,
        }
      });

      console.log('Notification sent successfully:', response);
      return response;
    } catch (error) {
      console.error('Error sending notification:', error);
      
      // Update notification status to failed
      await snap.ref.update({
        status: 'failed',
        sentAt: admin.firestore.FieldValue.serverTimestamp(),
        error: error.message,
      });
      
      throw error;
    }
  });
```

## 📱 Mobile App Configuration

### Android Setup

1. **Permissions** (already added to `android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
<uses-permission android:name="android.permission.USE_EXACT_ALARM" />
```

2. **Notification Receivers** (already added):
```xml
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
    <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED"/>
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
    </intent-filter>
</receiver>
```

### iOS Setup

Add to `ios/Runner/Info.plist`:
```xml
<key>UIBackgroundModes</key>
<array>
    <string>remote-notification</string>
</array>
```

## 🚀 Usage Instructions

### For Admins

1. **Access Admin Panel**:
   - Sign in with an email listed in Firebase admin_config
   - Go to Profile → Admin Panel (visible only to admins)

2. **Send Push Notifications**:
   - Navigate to "Send Notification" tab
   - Fill in title, message, and optional image URL
   - Click "Send to All Users"

3. **Configure Triggers**:
   - Go to "Triggers" tab
   - Click "Setup Defaults" to create all trigger types
   - Toggle individual triggers on/off
   - Each trigger has predefined schedules and messages

4. **View Analytics**:
   - Check "Analytics" tab for delivery statistics
   - View "History" tab for sent notifications

### For Users

- Notifications appear automatically based on configured triggers
- Users can manage notification preferences in app settings
- Push notifications work when app is closed
- Local notifications work offline

## 🔧 Customization

### Adding New Trigger Types

1. Add to `TriggerType` enum in `trigger_event_model.dart`
2. Create factory method in `TriggerEventFactory`
3. Add icon and description mappings
4. Update scheduler logic if needed

### Modifying Schedules

Edit the factory methods in `TriggerEventFactory` to change:
- Default times
- Frequency
- Conditions
- Messages

### Custom Notification Logic

Extend `NotificationScheduler` to add:
- Complex scheduling rules
- User-specific conditions
- Integration with app events

## 🐛 Troubleshooting

### Common Issues

1. **Admin panel not visible**: Check email is in Firebase admin_config
2. **Notifications not sending**: Verify Firebase Cloud Functions deployment
3. **Local notifications not working**: Check device permissions
4. **Build errors**: Run `flutter packages get` and rebuild

### Debug Commands

```bash
# Regenerate code
dart run build_runner build --delete-conflicting-outputs

# Check Firebase connection
flutter packages get
flutter run
```

## 📊 Analytics Data

The system tracks:
- Total notifications sent
- Success/failure rates
- Weekly and daily counts
- Recent activity
- Delivery statistics

All data is available in the admin analytics dashboard.

## 🔐 Security Notes

- Admin access is controlled via Firebase email whitelist
- All notification data is stored securely in Firestore
- FCM tokens are managed automatically
- Security rules prevent unauthorized access

## 🎉 Conclusion

The Vocadex notifications system is now fully implemented with:
- ✅ Push notifications via Firebase
- ✅ Local notifications with scheduling
- ✅ Admin panel with full management
- ✅ 8 different trigger event types
- ✅ Comprehensive analytics
- ✅ Secure access control

The system is production-ready and can be extended with additional features as needed.
