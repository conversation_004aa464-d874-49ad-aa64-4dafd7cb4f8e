# AdMob Configuration Setup

This document explains how to set up the AdMob configuration in the admin_config database collection.

## Overview

The AdMob configuration has been moved from hardcoded values in `ad_config.dart` to a database-driven approach using Firestore. This allows for dynamic configuration of AdMob app IDs and ad unit IDs without requiring app updates.

## Database Structure

### Collection: `admin_config`
### Document: `admob_settings`

The document should contain the following fields:

```json
{
  "appIdAndroid": "ca-app-pub-3940256099942544~3347511713",
  "interstitialAdUnitIdAndroid": "ca-app-pub-3940256099942544/1033173712",
  "bannerAdUnitIdAndroid": "ca-app-pub-3940256099942544/6300978111",
  "appIdiOS": "ca-app-pub-3940256099942544~1458002511",
  "interstitialAdUnitIdiOS": "ca-app-pub-3940256099942544/4411468910",
  "bannerAdUnitIdiOS": "ca-app-pub-3940256099942544/2934735716",
  "lastUpdated": [Firestore Timestamp]
}
```

## Setup Instructions

### Option 1: Automatic Initialization
The app will automatically create the document with default values when it starts up if the document doesn't exist.

### Option 2: Manual Setup via Firebase Console

1. Open Firebase Console
2. Navigate to Firestore Database
3. Go to the `admin_config` collection (create if it doesn't exist)
4. Create a new document with ID: `admob_settings`
5. Add the fields listed above with your actual AdMob IDs

### Option 3: Using Firebase Admin SDK (for developers)

```javascript
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.applicationDefault(),
  // Add your project config
});

const db = admin.firestore();

async function setupAdMobConfig() {
  const admobConfig = {
    appIdAndroid: 'your-android-app-id',
    interstitialAdUnitIdAndroid: 'your-android-interstitial-id',
    bannerAdUnitIdAndroid: 'your-android-banner-id',
    appIdiOS: 'your-ios-app-id',
    interstitialAdUnitIdiOS: 'your-ios-interstitial-id',
    bannerAdUnitIdiOS: 'your-ios-banner-id',
    lastUpdated: admin.firestore.FieldValue.serverTimestamp()
  };

  await db.collection('admin_config').doc('admob_settings').set(admobConfig);
  console.log('AdMob config created successfully');
}

setupAdMobConfig();
```

## How It Works

1. **App Startup**: The app fetches the AdMob configuration from Firestore during initialization
2. **Caching**: The configuration is cached in memory for fast synchronous access
3. **Fallback**: If the database is unavailable, the app uses default test AdMob IDs
4. **Real-time Updates**: The app listens for configuration changes and updates the cache automatically

## Default Values

The app includes default test AdMob IDs that will be used if:
- The Firestore document doesn't exist
- There's an error fetching the configuration
- The app is running offline

These default values are Google's test AdMob IDs and are safe to use during development.

## Production Setup

For production, replace the test AdMob IDs with your actual AdMob app and ad unit IDs:

1. Create your AdMob account and app
2. Generate your app IDs and ad unit IDs
3. Update the Firestore document with your production IDs
4. Test thoroughly before releasing

## Security

- The `admin_config` collection should have appropriate Firestore security rules
- Only authenticated admin users should be able to modify the AdMob configuration
- Consider using Firebase Functions for secure configuration updates

## Troubleshooting

### App shows test ads in production
- Check that your production AdMob IDs are correctly set in the Firestore document
- Verify the document ID is exactly `admob_settings`
- Check Firestore security rules allow reading the configuration

### Configuration not updating
- Check that the app has internet connectivity
- Verify Firestore security rules allow reading the document
- Check the app logs for any error messages

### App crashes on startup
- Ensure the AdMobConfig model matches the Firestore document structure
- Check that all required fields are present in the document
- Verify Firebase is properly initialized before accessing Firestore
