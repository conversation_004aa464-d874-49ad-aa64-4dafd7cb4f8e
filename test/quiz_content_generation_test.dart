// test/quiz_content_generation_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/models/quiz_content_models.dart';

void main() {
  group('Quiz Content Models Tests', () {
    test('WordQuizContent model serialization works correctly', () {
      // Create test data
      final fillInBlankContent = FillInBlankContent(
        id: 'test-fill-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        sentence: 'The cat sat on the _____',
        correctAnswer: 'mat',
        distractors: ['hat', 'bat', 'rat'],
        context: 'casual',
      );

      final trueFalseContent = TrueFalseContent(
        id: 'test-tf-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        statement: 'A mat is used for sleeping',
        isTrue: false,
        explanation: 'A mat is typically used for floor covering, not sleeping',
        category: 'definition',
      );

      final spellWordContent = SpellWordContent(
        id: 'test-spell-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        prompt: 'A flat piece of material used on floors',
        answer: 'mat',
        promptType: 'definition',
      );

      final wordQuizContent = WordQuizContent(
        wordId: 'test-word-id',
        word: 'mat',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [fillInBlankContent],
        trueFalseQuestions: [trueFalseContent],
        spellWordQuestions: [spellWordContent],
      );

      // Test serialization
      final firestoreData = wordQuizContent.toFirestore();
      expect(firestoreData, isA<Map<String, dynamic>>());
      expect(firestoreData['metadata']['word'], equals('mat'));
      expect(firestoreData['fillInBlank'], isA<List>());
      expect(firestoreData['trueFalse'], isA<List>());
      expect(firestoreData['spellWord'], isA<List>());

      // Test deserialization
      final reconstructed =
          WordQuizContent.fromFirestore('test-word-id', firestoreData);
      expect(reconstructed.word, equals('mat'));
      expect(reconstructed.fillInBlankQuestions.length, equals(1));
      expect(reconstructed.trueFalseQuestions.length, equals(1));
      expect(reconstructed.spellWordQuestions.length, equals(1));
      expect(reconstructed.totalQuestions, equals(3));
    });

    test('Quiz content models have correct properties', () {
      final fillInBlank = FillInBlankContent(
        id: 'test-1',
        difficulty: 'A1',
        createdAt: DateTime.now(),
        sentence: 'I love to _____ books',
        correctAnswer: 'read',
        distractors: ['write', 'buy', 'sell'],
        context: 'educational',
      );

      expect(fillInBlank.sentence, contains('_____'));
      expect(fillInBlank.correctAnswer, equals('read'));
      expect(fillInBlank.distractors.length, equals(3));
      expect(fillInBlank.context, equals('educational'));

      final trueFalse = TrueFalseContent(
        id: 'test-2',
        difficulty: 'A2',
        createdAt: DateTime.now(),
        statement: 'Reading helps improve vocabulary skills',
        isTrue: true,
        explanation: 'Reading exposes people to new words and contexts',
        category: 'characteristics',
      );

      expect(trueFalse.statement, isNotEmpty);
      expect(trueFalse.isTrue, isTrue);
      expect(trueFalse.explanation, isNotEmpty);
      expect(trueFalse.category, equals('characteristics'));

      final spellWord = SpellWordContent(
        id: 'test-3',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        prompt: 'What you do when looking at written text to understand it',
        answer: 'read',
        promptType: 'usage',
      );

      expect(spellWord.prompt, isNotEmpty);
      expect(spellWord.answer, equals('read'));
      expect(spellWord.promptType, equals('usage'));
    });

    test('VocabCard.createGlobalId generates consistent IDs', () {
      // Test that the same word always generates the same ID
      final id1 = VocabCard.createGlobalId('example');
      final id2 = VocabCard.createGlobalId('example');
      final id3 = VocabCard.createGlobalId('EXAMPLE');
      final id4 = VocabCard.createGlobalId('Example');

      expect(id1, equals(id2));
      expect(id1, equals(id3));
      expect(id1, equals(id4));

      // Test that different words generate different IDs
      final differentId = VocabCard.createGlobalId('different');
      expect(id1, isNot(equals(differentId)));
    });

    test('WordQuizContent provides correct statistics', () {
      final content = WordQuizContent(
        wordId: 'test-id',
        word: 'test',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [
          FillInBlankContent(
            id: '1',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'Test sentence 1 _____',
            correctAnswer: 'test',
            distractors: ['a', 'b', 'c'],
            context: 'test',
          ),
          FillInBlankContent(
            id: '2',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'Test sentence 2 _____',
            correctAnswer: 'test',
            distractors: ['d', 'e', 'f'],
            context: 'test',
          ),
        ],
        trueFalseQuestions: [
          TrueFalseContent(
            id: '3',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            statement: 'Test statement',
            isTrue: true,
            explanation: 'Test explanation',
            category: 'test',
          ),
        ],
        spellWordQuestions: [
          SpellWordContent(
            id: '4',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            prompt: 'Test prompt',
            answer: 'test',
            promptType: 'test',
          ),
        ],
      );

      expect(content.totalQuestions, equals(4));
      expect(content.hasContent, isTrue);
      expect(content.fillInBlankQuestions.length, equals(2));
      expect(content.trueFalseQuestions.length, equals(1));
      expect(content.spellWordQuestions.length, equals(1));
    });

    test('Empty WordQuizContent reports correctly', () {
      final emptyContent = WordQuizContent(
        wordId: 'empty-id',
        word: 'empty',
        difficulty: 'A1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [],
        trueFalseQuestions: [],
        spellWordQuestions: [],
      );

      expect(emptyContent.totalQuestions, equals(0));
      expect(emptyContent.hasContent, isFalse);
    });
  });
}
