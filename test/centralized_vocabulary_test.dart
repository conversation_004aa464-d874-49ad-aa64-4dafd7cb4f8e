import 'package:flutter_test/flutter_test.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/global_vocabulary_service.dart';
import 'package:vocadex/src/features/vocab_capture/services/centralized_vocabulary_service.dart';

void main() {
  group('Centralized Vocabulary System Tests', () {
    test('VocabCard normalization utilities work correctly', () {
      // Test word normalization
      expect(VocabCard.normalizeWord('Hello'), equals('hello'));
      expect(VocabCard.normalizeWord('  WORLD  '), equals('world'));
      expect(VocabCard.normalizeWord('Test123'), equals('test123'));

      // Test global ID creation
      expect(VocabCard.createGlobalId('Hello'), equals('hello'));
      expect(VocabCard.createGlobalId('  WORLD  '), equals('world'));
    });

    test('VocabCard word matching works correctly', () {
      final card = VocabCard(
        id: 'test',
        word: 'Hello',
        definition: 'A greeting',
        examples: ['Hello world'],
        type: ['interjection'],
        pronunciation: 'hə-ˈlō',
        level: 'A1',
        color: 'Red',
      );

      // Test case-insensitive matching
      expect(card.matchesWord('hello'), isTrue);
      expect(card.matchesWord('HELLO'), isTrue);
      expect(card.matchesWord('  Hello  '), isTrue);
      expect(card.matchesWord('world'), isFalse);
    });

    test('VocabCard conversion methods work correctly', () {
      final originalCard = VocabCard(
        id: 'user-123',
        word: 'Test',
        definition: 'A trial',
        examples: ['This is a test'],
        type: ['noun'],
        pronunciation: 'test',
        level: 'B1',
        color: 'Blue',
        masteryLevel: 3,
        correctAnswers: 5,
        totalAnswers: 10,
        frequency: 4,
      );

      // Test conversion to global card
      final globalCard = originalCard.toGlobalCard();
      expect(globalCard.id, equals('test')); // normalized
      expect(globalCard.word, equals('Test')); // preserved
      expect(globalCard.definition, equals('A trial')); // preserved
      expect(globalCard.masteryLevel, equals(1)); // reset to default
      expect(globalCard.correctAnswers, equals(0)); // reset to default
      expect(globalCard.totalAnswers, equals(0)); // reset to default
      expect(globalCard.lastReviewedAt, isNull); // reset to default

      // Test conversion to user card
      final userCard = originalCard.toUserCard();
      expect(userCard.id, equals('')); // will be set by Firebase
      expect(userCard.word, equals('Test')); // preserved
      expect(userCard.definition, equals('A trial')); // preserved
      expect(userCard.masteryLevel, equals(1)); // reset to default
      expect(userCard.correctAnswers, equals(0)); // reset to default
      expect(userCard.totalAnswers, equals(0)); // reset to default
      expect(userCard.lastReviewedAt, isNull); // reset to default
    });

    test('GlobalVocabularyService initialization works', () {
      // Skip Firebase-dependent test in unit test environment
      // This would work in integration tests with Firebase initialized
      expect(true, isTrue); // Placeholder test
    }, skip: 'Requires Firebase initialization');

    test('CentralizedVocabularyService initialization works', () {
      // Skip Firebase-dependent test in unit test environment
      // This would work in integration tests with Firebase initialized
      expect(true, isTrue); // Placeholder test
    }, skip: 'Requires Firebase initialization');

    group('VocabularyCreationResult', () {
      test('can be created with required parameters', () {
        final result = VocabularyCreationResult(
          success: true,
          message: 'Test message',
          isPremium: false,
        );

        expect(result.success, isTrue);
        expect(result.message, equals('Test message'));
        expect(result.isPremium, isFalse);
        expect(result.availableMeanings, isEmpty);
        expect(result.foundInGlobal, isFalse);
        expect(result.remainingCards, isNull);
      });

      test('can be created with all parameters', () {
        final testCard = VocabCard(
          id: 'test',
          word: 'test',
          definition: 'a trial',
          examples: ['test example'],
          type: ['noun'],
          pronunciation: 'test',
          level: 'A1',
          color: 'Red',
        );

        final result = VocabularyCreationResult(
          success: true,
          message: 'Found in global',
          availableMeanings: [testCard],
          foundInGlobal: true,
          isPremium: true,
          remainingCards: 10,
        );

        expect(result.success, isTrue);
        expect(result.message, equals('Found in global'));
        expect(result.availableMeanings.length, equals(1));
        expect(result.availableMeanings.first.word, equals('test'));
        expect(result.foundInGlobal, isTrue);
        expect(result.isPremium, isTrue);
        expect(result.remainingCards, equals(10));
      });
    });

    group('Edge Cases', () {
      test('handles empty and null strings correctly', () {
        expect(VocabCard.normalizeWord(''), equals(''));
        expect(VocabCard.normalizeWord('   '), equals(''));

        final card = VocabCard(
          id: 'test',
          word: 'test',
          definition: 'definition',
          examples: ['example'],
          type: ['noun'],
          pronunciation: 'pronunciation',
          level: 'A1',
          color: 'Red',
        );

        expect(card.matchesWord(''), isFalse);
        expect(card.matchesWord('   '), isFalse);
      });

      test('handles special characters in word normalization', () {
        expect(VocabCard.normalizeWord('café'), equals('café'));
        expect(VocabCard.normalizeWord('naïve'), equals('naïve'));
        expect(VocabCard.normalizeWord('résumé'), equals('résumé'));
      });
    });
  });
}
