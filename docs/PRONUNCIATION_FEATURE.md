# Audio Pronunciation Feature

This document explains the implementation of the audio pronunciation feature in the Vocadex app.

## Overview

The audio pronunciation feature allows users to hear the correct pronunciation of vocabulary words by tapping a speaker icon next to the phonetic pronunciation in vocabulary cards.

## Implementation Details

The feature consists of the following components:

1. **PronunciationButton Widget**: A reusable widget that plays pronunciation audio when tapped.
2. **Audio Generation Service**: Generates and stores pronunciation audio files in Firebase Storage.
3. **Pronunciation Provider**: Provides access to pronunciation functionality throughout the app.

## How It Works

1. When a vocabulary card is created:
   - The word is sent to Google Vertex AI TTS to generate pronunciation audio
   - The audio file is stored in Firebase Storage
   - The download URL is stored in the VocabCard model's `audioUrl` field

2. When viewing a vocabulary card:
   - A speaker icon appears next to the phonetic pronunciation
   - Tapping the icon plays the pronunciation audio
   - If no stored audio is available, it falls back to using Google Translate TTS API

## Technical Implementation

### 1. PronunciationButton Widget

Located at: `lib/src/features/vocab_card/widgets/pronunciation_button.dart`

This widget:
- Displays a speaker icon that changes when audio is playing
- Handles audio playback using the audioplayers package
- Provides visual feedback during playback
- Uses AppColors from the theme constants to maintain consistent styling

### 2. VocabCard Model Update

The VocabCard model has been updated to include an `audioUrl` field that stores the URL to the pronunciation audio file.

### 3. Integration with Vocabulary Card UI

The pronunciation button has been integrated into the vocabulary card UI, appearing next to the phonetic pronunciation.

## Testing

To test the audio pronunciation feature:

1. Create a new vocabulary card
2. View the card and look for the speaker icon next to the pronunciation
3. Tap the icon to hear the pronunciation
4. The icon should change while audio is playing

## Future Improvements

- Cache audio files locally for offline use
- Add support for different accents (American, Australian, etc.)
- Implement a pronunciation practice feature where users can record and compare their pronunciation

## Dependencies

- audioplayers: For audio playback
- firebase_storage: For storing audio files
