# Analytics Integration Guide

This guide shows you how to add more analytics events to your Vocadex app.

## ✅ Already Integrated Events

### 1. **Onboarding Complete**
- **Location**: `lib/src/features/onboarding/presentation/screens/success_screen.dart`
- **Trigger**: When user completes guided onboarding
- **Properties**: Completion date, method

### 2. **Word Addition**
- **Location**: `lib/src/features/vocab_capture/services/vocab_save_service.dart`
- **Trigger**: When user adds a new vocabulary word
- **Properties**: Word details, premium status, daily count

### 3. **Quiz Events**
- **Quiz Started**: `lib/src/features/vocab_quiz/quiz/quiz_button.dart`
- **Quiz Completed**: `lib/src/features/vocab_quiz/quiz/quiz_results_screen.dart`
- **Properties**: Quiz type, score, duration, premium status

### 4. **Authentication**
- **Location**: `lib/src/features/auth/providers/auth_controller.dart`
- **Events**: User sign up, sign in (email & Google)
- **Properties**: Sign in method, timestamp

### 5. **App Lifecycle**
- **Location**: `lib/main.dart`
- **Events**: App launched, resumed, paused, session ended
- **Properties**: Session duration, timing

## 🔄 How to Add More Events

### Pattern 1: Simple Event Tracking
```dart
// Import the analytics service
import 'package:vocadex/src/services/analytics_service.dart';

// Track the event
try {
  await AnalyticsService.instance.track('Event Name', {
    'Property 1': value1,
    'Property 2': value2,
    'Timestamp': DateTime.now().toIso8601String(),
  });
} catch (e) {
  debugPrint('Analytics tracking error: $e');
}
```

### Pattern 2: Feature Usage Tracking
```dart
// Track when user uses a specific feature
await AnalyticsService.instance.trackFeatureUsed('Feature Name', {
  'Context': 'where_used',
  'User Type': isPremium ? 'premium' : 'free',
});
```

### Pattern 3: User Property Updates
```dart
// Update user properties
await AnalyticsService.instance.updateUserProperty('Property Name', value);

// Increment counters
await AnalyticsService.instance.incrementUserProperty('Total Actions');
```

## 📊 Recommended Additional Events

### **Premium & Monetization**
```dart
// When paywall is shown
await AnalyticsService.instance.trackPaywallShown('trigger_reason');

// When subscription is purchased
await AnalyticsService.instance.trackSubscriptionPurchased('product_id', price);

// When ads are shown/clicked
await AnalyticsService.instance.trackAdShown('banner', 'home_screen');
await AnalyticsService.instance.trackAdClicked('interstitial', 'quiz_completion');
```

### **Learning Progress**
```dart
// When word mastery changes
await AnalyticsService.instance.trackWordMasteryChanged(
  word: 'example',
  fromLevel: 'wild',
  toLevel: 'tamed',
  totalMasteredWords: 25,
);

// When weekly goals are completed
await AnalyticsService.instance.trackWeeklyGoalCompleted('noun', 10);

// When learning streaks are achieved
await AnalyticsService.instance.track('Learning Streak Achieved', {
  'Streak Days': 7,
  'Streak Type': 'daily_words',
});
```

### **Feature Usage**
```dart
// Pronunciation feature
await AnalyticsService.instance.trackPronunciationPlayed('word', 'stored');

// Search functionality
await AnalyticsService.instance.track('Search Performed', {
  'Query': searchTerm,
  'Results Count': results.length,
});

// Filter usage
await AnalyticsService.instance.track('Filters Applied', {
  'Active Filters': ['CEFR Level', 'Word Type'],
  'Filter Count': 2,
});
```

### **Error Tracking**
```dart
// Track errors for debugging
await AnalyticsService.instance.track('Error Occurred', {
  'Error Type': 'network_error',
  'Error Message': e.toString(),
  'Screen': 'quiz_screen',
});
```

## 🎯 Integration Locations

### **Where to Add Events:**

1. **Vocabulary Features**
   - `lib/src/features/vocab_capture/` - Word addition, AI generation
   - `lib/src/features/vocab_deck/` - Vocabulary browsing, filtering

2. **Quiz Features**
   - `lib/src/features/vocab_quiz/` - Quiz interactions, performance

3. **User Management**
   - `lib/src/features/auth/` - Authentication events
   - `lib/src/features/subscriptions/` - Premium conversion

4. **Navigation & UI**
   - `lib/src/features/bottom_navbar/` - Navigation patterns
   - `lib/src/features/home/<USER>

5. **Settings & Preferences**
   - `lib/src/features/settings/` - User preferences
   - `lib/src/features/notifications/` - Notification interactions

## 📈 Daily Metrics Integration

Use the `DailyMetricsProvider` to track daily counters:

```dart
// Import the provider
import 'package:vocadex/src/features/analytics/providers/daily_metrics_provider.dart';

// Update daily metrics
await ref.read(dailyMetricsProvider.notifier).incrementWordsAdded();
await ref.read(dailyMetricsProvider.notifier).incrementQuizzesCompleted();
await ref.read(dailyMetricsProvider.notifier).incrementSessionCount();

// Get current metrics
final metrics = ref.read(dailyMetricsProvider);
print('Words added today: ${metrics.wordsAdded}');
```

## 🔍 Testing Analytics

### **Debug Mode**
Set `enableDebugLogging = true` in `AnalyticsConfig` to see events in console.

### **Verify Events**
1. Check Mixpanel dashboard for incoming events
2. Use Mixpanel's live view to see real-time events
3. Test with different user scenarios

### **Common Issues**
- Events not appearing: Check project token and network
- Missing properties: Verify all required data is available
- Performance: Ensure analytics calls don't block UI

## 🚀 Next Steps

1. **Identify Key User Actions**: What actions are most important for your app?
2. **Add Strategic Events**: Focus on conversion funnel and engagement metrics
3. **Test Thoroughly**: Verify events are firing correctly
4. **Analyze Data**: Use insights to improve user experience
5. **Iterate**: Add more events based on what you learn

## 📝 Event Naming Convention

Use consistent naming:
- **Actions**: `Word Added`, `Quiz Completed`, `Feature Used`
- **States**: `User Signed In`, `Premium Activated`
- **Milestones**: `Onboarding Completed`, `Goal Achieved`

Keep property names consistent across events for better analysis.
