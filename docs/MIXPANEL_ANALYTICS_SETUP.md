# Mixpanel Analytics Setup for Vocadex

This document explains how to set up and use Mixpanel analytics in the Vocadex app.

## Setup Instructions

### 1. Get Your Mixpanel Project Token

1. Sign up for a free Mixpanel account at [mixpanel.com](https://mixpanel.com)
2. Create a new project for your Vocadex app
3. Copy your project token from the project settings

### 2. Configure the App

1. Open `lib/src/core/config/analytics_config.dart`
2. Replace `'YOUR_MIXPANEL_PROJECT_TOKEN_HERE'` with your actual project token

```dart
static const String mixpanelProjectToken = 'your_actual_token_here';
```

### 3. Install Dependencies

Run the following command to install the Mixpanel dependency:

```bash
flutter pub get
```

## Integration Points

### Core Events Being Tracked

#### 1. **Onboarding Complete**
- **When**: User completes the guided onboarding
- **Properties**: Learning goals, proficiency level, time commitment
- **Integration**: Add to onboarding completion logic

#### 2. **Daily Words Added**
- **When**: User adds a new vocabulary word
- **Properties**: Word type, CEFR level, premium status, daily count
- **Integration**: Add to `VocabSaveService.saveVocabulary`

#### 3. **Daily Quizzes Attempted/Completed**
- **When**: User starts/completes a quiz
- **Properties**: Quiz type, score, accuracy, time spent
- **Integration**: Add to quiz service methods

#### 4. **Daily App Usage & Active Users**
- **When**: App launch, session end, feature usage
- **Properties**: Session duration, platform, time of day
- **Integration**: App lifecycle events

### Additional Recommended Events

#### **User Engagement**
- `Feature Used` - Track which features are most popular
- `Search Performed` - Track vocabulary search patterns
- `Pronunciation Played` - Track audio feature usage
- `Filters Applied` - Track how users filter their vocabulary

#### **Learning Progress**
- `Word Mastery Changed` - Track progression (wild → tamed → mastered)
- `Weekly Goal Completed` - Track goal achievement
- `Learning Streak Achieved` - Track consecutive usage days

#### **Monetization**
- `Paywall Shown` - Track premium conversion funnel
- `Subscription Purchased` - Track successful conversions
- `Ad Shown/Clicked` - Track ad performance for free users
- `Daily Limit Reached` - Track when users hit free tier limits

## Implementation Examples

### 1. Track Word Addition

```dart
// In VocabSaveService.saveVocabulary method
await AnalyticsService.instance.trackWordAdded(
  vocabCard,
  isPremium: isPremium,
  isFromGlobalRepo: isFromGlobalRepo,
  dailyWordsCount: dailyWordsCount,
);

// Update daily metrics
ref.read(dailyMetricsProvider.notifier).incrementWordsAdded();
```

### 2. Track Quiz Completion

```dart
// In your quiz completion logic
await AnalyticsService.instance.trackQuizCompleted(
  quizType: 'multiple_choice',
  score: correctAnswers,
  totalQuestions: totalQuestions,
  timeSpent: timeSpentSeconds,
  isPremium: isPremium,
);

// Update daily metrics
ref.read(dailyMetricsProvider.notifier).incrementQuizzesCompleted();
```

### 3. Track Feature Usage

```dart
// When user plays pronunciation
await AnalyticsService.instance.trackPronunciationPlayed(word, 'stored');

// When user applies filters
await AnalyticsService.instance.track('Filters Applied', {
  'Active Filters': ['CEFR Level', 'Word Type'],
  'Filter Count': 2,
});
```

### 4. Track User Authentication

```dart
// After successful authentication
await AnalyticsService.instance.identifyUser(
  userId,
  isPremium: isPremium,
  signUpDate: signUpDate,
  onboardingCompleted: onboardingCompleted,
);
```

## Daily Metrics Tracking

The app includes a `DailyMetricsProvider` that automatically tracks:

- Words added per day
- Quizzes attempted/completed per day
- App sessions per day
- Weekly and monthly summaries

### Usage:

```dart
// Get today's metrics
final metrics = ref.watch(dailyMetricsProvider);

// Get weekly summary
final weeklyMetrics = ref.watch(weeklyMetricsProvider);

// Increment counters
ref.read(dailyMetricsProvider.notifier).incrementWordsAdded();
ref.read(dailyMetricsProvider.notifier).incrementQuizzesCompleted();
```

## Key Integration Points

### 1. Authentication Flow
- Add user identification after successful login
- Track sign-up and sign-in events

### 2. Onboarding Flow
- Track completion with user preferences
- Track drop-off points for optimization

### 3. Vocabulary Management
- Track word additions with context
- Track global repository usage
- Track AI generation usage

### 4. Quiz System
- Track quiz starts and completions
- Track performance metrics
- Track quiz types and preferences

### 5. Premium Features
- Track paywall interactions
- Track subscription purchases
- Track feature usage by subscription status

### 6. App Lifecycle
- Track app launches and sessions
- Track feature navigation
- Track user engagement patterns

## Analytics Dashboard Insights

With this setup, you'll be able to analyze:

### **User Engagement**
- Daily/Weekly/Monthly Active Users
- Session duration and frequency
- Feature adoption rates
- User retention cohorts

### **Learning Effectiveness**
- Words added vs. mastery progression
- Quiz performance trends
- Learning streak patterns
- Goal completion rates

### **Monetization Performance**
- Free vs. Premium user behavior
- Conversion funnel analysis
- Ad performance metrics
- Feature usage by subscription tier

### **Product Optimization**
- Most/least used features
- User journey drop-off points
- Error rates and technical issues
- Search and filter usage patterns

## Best Practices

1. **Privacy First**: Only track necessary data and respect user privacy
2. **Consistent Naming**: Use the constants in `AnalyticsConfig` for event names
3. **Meaningful Properties**: Include context that helps with analysis
4. **Error Handling**: Wrap analytics calls in try-catch blocks
5. **Performance**: Don't block UI with analytics calls
6. **Testing**: Test analytics in development mode before production

## Troubleshooting

### Common Issues:

1. **Events not appearing**: Check your project token and network connectivity
2. **Duplicate events**: Ensure you're not calling track methods multiple times
3. **Missing properties**: Verify all required properties are being passed
4. **Performance issues**: Make sure analytics calls are asynchronous

### Debug Mode:

Set `enableDebugLogging = true` in `AnalyticsConfig` to see analytics events in the console during development.

## Next Steps

1. Set up your Mixpanel project and get the token
2. Update the configuration with your token
3. Test the integration in development
4. Deploy and monitor your analytics dashboard
5. Set up custom dashboards and alerts in Mixpanel
6. Analyze user behavior and optimize your app accordingly
